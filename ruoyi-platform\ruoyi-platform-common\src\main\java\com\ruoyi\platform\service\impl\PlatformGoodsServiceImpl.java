package com.ruoyi.platform.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.domain.PlatformGoods;
import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.mapper.PlatformGoodsMapper;
import com.ruoyi.platform.mapper.PlatformLiveMapper;
import com.ruoyi.platform.service.IPlatformGoodsService;

/**
 * 产品管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@Service
public class PlatformGoodsServiceImpl implements IPlatformGoodsService {

    @Autowired
    private PlatformGoodsMapper platformGoodsMapper;

    @Autowired
    private PlatformLiveMapper platformLiveMapper;

    /**
     * 查询产品管理
     * 
     * @param goodsId 产品管理主键
     * @return 产品管理
     */
    @Override
    public PlatformGoods selectPlatformGoodsByGoodsId(Long goodsId) {
        return platformGoodsMapper.selectPlatformGoodsByGoodsId(goodsId);
    }

    /**
     * 查询产品管理列表
     * 
     * @param platformGoods 产品管理
     * @return 产品管理
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformGoods> selectPlatformGoodsList(PlatformGoods platformGoods) {
        return platformGoodsMapper.selectPlatformGoodsList(platformGoods);
    }

    /**
     * 新增产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    @Override
    public int insertPlatformGoods(PlatformGoods platformGoods) {
        platformGoods.setCreateTime(DateUtils.getNowDate());
        platformGoods.setUpdateTime(DateUtils.getNowDate());
        return platformGoodsMapper.insertPlatformGoods(platformGoods);
    }

    /**
     * 修改产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    @Override
    public int updatePlatformGoods(PlatformGoods platformGoods) {
        platformGoods.setUpdateTime(DateUtils.getNowDate());
        return platformGoodsMapper.updatePlatformGoods(platformGoods);
    }

    /**
     * 批量删除产品管理
     * 
     * @param goodsIds 需要删除的产品管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformGoodsByGoodsIds(Long[] goodsIds) {
        return platformGoodsMapper.deletePlatformGoodsByGoodsIds(goodsIds);
    }

    /**
     * 删除产品管理信息
     * 
     * @param goodsId 产品管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformGoodsByGoodsId(Long goodsId) {
        return platformGoodsMapper.deletePlatformGoodsByGoodsId(goodsId);
    }

    //根据产品的ID查找多个产品名称
    @Override
    public Map<Long, String> getGoodsIdByNameIds(List<Long> ids) {
        List<PlatformGoods> goods = platformGoodsMapper.getGoodsByIds(ids);
        Map<Long, String> resultMap = new HashMap<>();
        for (PlatformGoods g : goods) {
            resultMap.put(g.getGoodsId(), g.getGoodsName());
        }
        return resultMap;
    }

    /**
     * 根据直播ID获取产品列表信息
     *
     * @param liveId 直播ID
     * @return 产品列表
     */
    @Override
    public List<PlatformGoods> getGoodsByLiveId(Long liveId) {
        // 1. 获取直播信息
        PlatformLive live = platformLiveMapper.selectPlatformLiveByLiveId(liveId);
        if (live == null || live.getGoodsId() == null || live.getGoodsId().isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 使用Set去重
        Set<Long> uniqueGoodsIds = new HashSet<>(live.getGoodsId());
        
        // 3. 批量查询产品信息
        return platformGoodsMapper.getGoodsByIds(new ArrayList<>(uniqueGoodsIds));
    }
}
