package com.ruoyi.video.service;

public interface IVideoEditService {

    /**
     * 列出公公素材库媒资基础信息
     * 
     * @param MediaTagId           媒资标签
     * @param NextToken            下一页目标点
     * @param MaxResults           本次请求所返回的最大记录条数
     * @param PageNO               当前页码 默认1
     * @param PageSize             分页大小 默认10
     * @param IncludeFileBasicInfo 是否返回文件基础信息
     * @param BusinessType         媒资业务类型 贴纸 背景音乐 背景图片
     * @return String
     * <AUTHOR>
     * @data 2025-07-17
     */
    String ListPublicMediaBasicInfos(String MediaTagId, String NextToken, Integer MaxResults, Integer PageNO,
            Integer PageSize, Boolean IncludeFileBasicInfo, String BusinessType) throws Exception;

    /**
     * 列出公共素材库所有标签
     * 
     * @param BusinessType 媒资业务类型
     * @param EntityId     实体ID
     * @return AjaxResult 媒资业务标签
     * @return String
     * <AUTHOR>
     * @data 2025-07-17
     */
    String ListAllPublicMediaTags(String BusinessType, String EntityId) throws Exception;
}
