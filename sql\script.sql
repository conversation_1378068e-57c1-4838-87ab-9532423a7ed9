DROP TABLE IF EXISTS sys_script;
CREATE TABLE sys_script
(
  script_id          BIGINT(20)      NOT NULL AUTO_INCREMENT COMMENT '主键',
  script_title       VARCHAR(255)    NOT NULL COMMENT '标题',
  content     TEXT            NOT NULL COMMENT '内容',
  create_by   VARCHAR(64)     DEFAULT ''               COMMENT '创建者',
  create_time DATETIME                                  COMMENT '创建时间',
  update_by   VARCHAR(64)     DEFAULT ''               COMMENT '更新者',
  update_time DATETIME                                  COMMENT '更新时间',
  remark      VARCHAR(500)    DEFAULT NULL             COMMENT '备注',
  PRIMARY KEY (script_id)
) ENGINE=InnoDB AUTO_INCREMENT=100 COMMENT='脚本表';


-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本', '1', '1', 'script', 'system/script/index', 1, 0, 'C', '0', '0', 'system:script:list', '#', 'admin', sysdate(), '', null, '脚本菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:script:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:script:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:script:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:script:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('脚本导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:script:export',       '#', 'admin', sysdate(), '', null, '');