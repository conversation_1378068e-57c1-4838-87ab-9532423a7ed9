package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * 分类对象 platform_category
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Schema(description = "分类对象")
public class PlatformCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long categoryId;

    /** 分类名称 */
    @Schema(title = "分类名称")
    @Excel(name = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String categoryTitle;

    /** 项目ID */
    @Schema(title = "项目ID")
    @Excel(name = "项目ID")
    private Long projectId;

    /** projectId 到 platform_project 映射 */
    private String projectTitle;
    
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }


    public void setCategoryTitle(String categoryTitle) 
    {
        this.categoryTitle = categoryTitle;
    }

    public String getCategoryTitle() 
    {
        return categoryTitle;
    }


    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }

    public void setProjectTitle(String projectTitle) 
    {
        this.projectTitle = projectTitle;
    }

    public String getProjectTitle() 
    {
        return projectTitle;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("categoryId", getCategoryId())
            .append("categoryTitle", getCategoryTitle())
            .append("projectId", getProjectId())
            .append("projectTitle", getProjectTitle())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
