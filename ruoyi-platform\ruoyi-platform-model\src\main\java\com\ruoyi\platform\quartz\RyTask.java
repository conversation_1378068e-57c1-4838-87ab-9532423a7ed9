package com.ruoyi.platform.quartz;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.mapper.PlatformVideoMapper;
import com.ruoyi.platform.model.domain.PlatformTask;
import com.ruoyi.platform.model.mapper.PlatformTaskMapper;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask
{
    public void ryMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i)
    {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams()
    {
        System.out.println("执行无参方法");
    }

    @Autowired
    private PlatformTaskMapper platformTaskMapper;

    @Autowired
    private PlatformVideoMapper platformVideoMapper;
    
    // 查询声音推理任务所有一小时前进入处理中状态的任务 0 待处理 1处理中 2 完成 3失败
    public void checkAndFailStuckTasks() {
       //计算一小时之前的时间段
       Date oneHourAgo = new Date(System.currentTimeMillis() - (60 * 60 * 1000));
       //查询所有一小时之前进入处理中的状态
       List<PlatformTask> stuckTasks  = platformTaskMapper.selectStuckTasks(oneHourAgo);
       //验证是否为空
       if(stuckTasks !=null && stuckTasks.isEmpty()){
          //将查询到的声音推理任务 超过一小时状态还是为处理中的任务 设置为失败状态 
          for(PlatformTask task: stuckTasks){
            task.setTaskStatus("3");
            platformTaskMapper.updatePlatformTask(task);
          }
       }
    }

    // 查询视频合成任务所有一小时前进入执行中状态的任务 1已创建 2进行中 3成功 4失败
    public void checkStuckVideos() {
        //计算一小时之前的时间段
        Date oneHourAgo = new Date(System.currentTimeMillis() - (60 * 60 * 1000));
        //查询所有一小时之前进入执行中的状态
        List<PlatformVideo> videoTasks  = platformVideoMapper.selectVideoTasks(oneHourAgo);
        //验证是否为空
        if(videoTasks !=null && videoTasks.isEmpty()){
           //将查询到的声音推理任务 超过一小时状态还是为处理中的任务 设置为失败状态 
           for(PlatformVideo muse: videoTasks){
             muse.setStatus("4");
             platformVideoMapper.updatePlatformVideo(muse);
           }
        }
     }

    // /**
    //  * 清理过期的分片上传临时文件
    //  */
    // public void cleanExpiredChunkFiles() {
    //     try {
    //         // 调用分片工具类方法清理过期任务
    //         FileSharDingUtils.cleanupExpiredTasks();
    //         log.info("清理过期分片文件完成");
    //     } catch (Exception e) {
    //         log.error("清理过期分片文件失败", e);
    //     }
    // }
}
