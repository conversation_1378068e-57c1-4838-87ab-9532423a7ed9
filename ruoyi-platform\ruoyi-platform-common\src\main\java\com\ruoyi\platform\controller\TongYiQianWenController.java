package com.ruoyi.platform.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ai")
public class TongYiQianWenController {

    // @RequestMapping(path = "/desc", produces = "text/event-stream")
    // @Anonymous
    // public Flux<String> desc(String desc) throws Exception {
    //     Flowable<GenerationResult> result = TongYiQianWen.callWithMessageAsync(desc);
    //     Flowable<GenerationOutput> map1 = result.map(GenerationResult::getOutput);
    //     Flowable<List<Choice>> map2 = map1.map(GenerationOutput::getChoices);
    //     Flowable<Choice> map3 = map2.flatMapIterable(x -> x);
    //     Flowable<Message> map4 = map3.map(Choice::getMessage);
    //     Flowable<String> map5 = map4.map(Message::getContent);
    //     return Flux.from(map5);
    // }

}
