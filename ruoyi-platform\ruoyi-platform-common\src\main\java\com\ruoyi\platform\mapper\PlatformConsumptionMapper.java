package com.ruoyi.platform.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformConsumption;
import com.ruoyi.platform.domain.vo.PlatformConsumptionVo;

/**
 * 用户算力点数变化记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface PlatformConsumptionMapper 
{
    /**
     * 查询用户算力点数变化记录
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 用户算力点数变化记录
     */
    public PlatformConsumption selectPlatformConsumptionByConsumptionId(Long consumptionId);

    /**
     * 查询用户算力点数变化记录列表
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 用户算力点数变化记录集合
     */
    public List<PlatformConsumption> selectPlatformConsumptionList(PlatformConsumption platformConsumption);

    /**
     * 新增用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    public int insertPlatformConsumption(PlatformConsumption platformConsumption);

    /**
     * 修改用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    public int updatePlatformConsumption(PlatformConsumption platformConsumption);

    /**
     * 删除用户算力点数变化记录
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 结果
     */
    public int deletePlatformConsumptionByConsumptionId(Long consumptionId);

    /**
     * 批量删除用户算力点数变化记录
     * 
     * @param consumptionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformConsumptionByConsumptionIds(Long[] consumptionIds);

   /**
     * 查询某个用户在过去一年内的算力消费信息，并统计每个功能的花费及其占比。
     *
     * @param userName 用户名
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 消费汇总列表，包含每个消费标题的花费及其占比
     */
    public List<PlatformConsumptionVo> selectConsumptionSummary(String userName, Date startDate, Date endDate);

   /**
     * 获取算力消费统计数据
     * 
     * @return 统计数据，包含总消费量、日消费量、月消费量等信息
     */
    public Map<String, Object> selectConsumptionStatistics();
}
