<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modelMessage.mapper.MessageTemplateMapper">
    
    <resultMap type="MessageTemplate" id="MessageTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateCode"    column="template_code"    />
        <result property="templateType"    column="template_type"    />
        <result property="templateContent"    column="template_content"    />
        <result property="templateVariable"    column="template_variable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMessageTemplateVo">
        select template_id, template_name, template_code, template_type, template_content, template_variable, create_by, create_time, update_by, update_time, remark from message_template
    </sql>

    <select id="selectMessageTemplateList" parameterType="MessageTemplate" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="templateType != null  and templateType != ''"> and template_type = #{templateType}</if>
        </where>
    </select>
    
    <select id="selectMessageTemplateByTemplateId" parameterType="Long" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        where message_template.template_id = #{templateId}
    </select>
        
    <insert id="insertMessageTemplate" parameterType="MessageTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into message_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null">template_name,</if>
            <if test="templateCode != null">template_code,</if>
            <if test="templateType != null">template_type,</if>
            <if test="templateContent != null">template_content,</if>
            <if test="templateVariable != null">template_variable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null">#{templateName},</if>
            <if test="templateCode != null">#{templateCode},</if>
            <if test="templateType != null">#{templateType},</if>
            <if test="templateContent != null">#{templateContent},</if>
            <if test="templateVariable != null">#{templateVariable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMessageTemplate" parameterType="MessageTemplate">
        update message_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="templateCode != null">template_code = #{templateCode},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="templateContent != null">template_content = #{templateContent},</if>
            <if test="templateVariable != null">template_variable = #{templateVariable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where message_template.template_id = #{templateId}
    </update>

    <delete id="deleteMessageTemplateByTemplateId" parameterType="Long">
        delete from message_template where template_id = #{templateId}
    </delete>

    <delete id="deleteMessageTemplateByTemplateIds" parameterType="String">
        delete from message_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
    </mapper>