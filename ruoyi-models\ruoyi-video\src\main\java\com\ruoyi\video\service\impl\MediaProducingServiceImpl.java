package com.ruoyi.video.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.GetMediaProducingJobRequest;
import com.aliyun.ice20201109.models.GetMediaProducingJobResponse;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse;
import com.google.common.base.Preconditions;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.domain.MediaEdit;
import com.ruoyi.video.domain.MediaRequest;
import com.ruoyi.video.service.IMediaEditService;
import com.ruoyi.video.service.IMediaProducingService;
import com.ruoyi.video.utils.ImgTools;
import com.ruoyi.video.utils.MediaEditStatus;
import com.ruoyi.video.utils.MediaUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量智能一键成片服务实现类
 * <p>
 * 实现阿里云ICE批量智能一键成片相关的业务逻辑。
 * 提供任务提交、状态查询、列表获取等功能的具体实现。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaProducingServiceImpl implements IMediaProducingService {

    private final Client mediaClient;

    private final IceClientAKConfig iceClientAKConfig;

    private final IMediaEditService mediaEditService;

    /**
     * 提交任务
     */
    @Override
    public String submitMediaProducingJob(MultipartRequest request, MediaRequest mediaRequest) {

        try {
            // 收集所有文件到一个列表中
            List<MultipartFile> allFiles = new ArrayList<>();
            request.getMultiFileMap().forEach((key, multipartFiles) -> {
                allFiles.addAll(multipartFiles);
            });

            // 转换为数组
            MultipartFile[] files = allFiles.toArray(new MultipartFile[0]);

            Preconditions.checkArgument(files.length > 0, "请选择要上传的文件");

            // 通过TemplateId创建合成任务
            SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();

            String clipsParam = mediaRequest.getClipsParam();
            JSONObject jsonObject = JSONObject.parseObject(clipsParam);
            JSONObject resultObject = new JSONObject();
            int fileIndex = 0;
            // 遍历原始JSONObject中的键值对
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                if ("mediaId".equals(entry.getValue()) && fileIndex < files.length) {
                    MultipartFile file = files[fileIndex];
                    String fullPath = MediaUtils.getFileFullPath(file);
                    String fileUrl = FileOperateUtils.upload(fullPath, file, null);
                    // 移除签名参数，ICE只需要纯净的OSS URL
                    String cleanUrl = MediaUtils.removeUrlSignature(fileUrl);
                    resultObject.put(entry.getKey(), cleanUrl);
                    fileIndex++;
                }
            }

            JSONObject outputMediaConfig = new JSONObject();
            String outputPath = MediaUtils.getFileOutputPath();
            outputMediaConfig.put("MediaURL", outputPath);

            JSONObject userData = new JSONObject();
            if (iceClientAKConfig.getCallback() != null
                    && iceClientAKConfig.getCallback().getNotifyUrl() != null) {
                userData.put("NotifyAddress", iceClientAKConfig.getCallback().getNotifyUrl());
            }

            // 设置参数
            submitMediaProducingJobRequest.setTemplateId(mediaRequest.getTemplateId());
            submitMediaProducingJobRequest.setClipsParam(resultObject.toJSONString());
            submitMediaProducingJobRequest
                    .setOutputMediaConfig(outputMediaConfig.toJSONString());
            submitMediaProducingJobRequest.setUserData(userData.toJSONString());
            submitMediaProducingJobRequest.setProjectMetadata(mediaRequest.getProjectMetadata());

            // 提交任务
            SubmitMediaProducingJobResponse submitMediaProducingJobResponse = mediaClient
                    .submitMediaProducingJob(submitMediaProducingJobRequest);

            log.info("提交任务成功，BODY是什么：{}", submitMediaProducingJobResponse.getBody());

            System.out.println("requestId : " +
                    submitMediaProducingJobResponse.getBody().getRequestId());
            System.out.println("jobId : " +
                    submitMediaProducingJobResponse.getBody().getJobId());
            System.out.println("projectId : " +
                    submitMediaProducingJobResponse.getBody().getProjectId());

            // 设置返回结果
            JSONObject result = new JSONObject();

            result.put("requestId", submitMediaProducingJobResponse.getBody().getRequestId());
            result.put("projectId", submitMediaProducingJobResponse.getBody().getProjectId());
            result.put("jobId", submitMediaProducingJobResponse.getBody().getJobId());
            result.put("mediaId", submitMediaProducingJobResponse.getBody().getMediaId());
            log.info("test", submitMediaProducingJobResponse.getBody().getVodMediaId());
            result.put("vodMediaId", submitMediaProducingJobResponse.getBody().getVodMediaId());

            // 创建临时文件路径
            String tempVideoPath = new File(System.getProperty("java.io.tmpdir"),
                    System.currentTimeMillis() + "_" + files[0].getOriginalFilename()).getAbsolutePath();

            // 将MultipartFile保存为临时文件
            files[0].transferTo(new java.io.File(tempVideoPath));

            // 使用文件路径调用截帧方法
            String coverUrl = ImgTools.randomGrabberFFmpegVideoImage(tempVideoPath);
            // 清理临时文件
            new java.io.File(tempVideoPath).delete();

            // 将数据插入到数据库当
            MediaEdit mediaEdit = new MediaEdit();
            JSONObject projectMetadata = JSONObject.parseObject(mediaRequest.getProjectMetadata());
            String title = projectMetadata.getString("Title");
            String description = projectMetadata.getString("Description");
            mediaEdit.setTitle(title);
            mediaEdit.setDescription(description);
            mediaEdit.setJobId(result.getString("jobId"));
            mediaEdit.setProjectId(result.getString("projectId"));
            mediaEdit.setStatus(MediaEditStatus.PRODUCING.getCode());
            mediaEdit.setCoverUrl(coverUrl);
            String path = MediaUtils.extractPathFromOssUrl(outputPath);
            mediaEdit.setMediaUrl(path);
            mediaEditService.insertMediaEdit(mediaEdit);

            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("批量智能一键成片任务提交失败: " + e.getMessage());
        }

    }

    /**
     * 查询剪辑合成作业详情
     */
    @Override
    public String getMediaProducingJobByJobId(String jobId) {
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(jobId), "作业编号不能为空");
            GetMediaProducingJobRequest request = new GetMediaProducingJobRequest();
            request.setJobId(jobId);
            GetMediaProducingJobResponse res = mediaClient.getMediaProducingJob(request);

            // 直接返回完整的响应体
            JSONObject result = new JSONObject();
            result.put("data", res.getBody());

            // 测试数据
            String mediaURL = res.getBody().getMediaProducingJob().getMediaURL();
            String url = MediaUtils.convertToAccessibleUrl(mediaURL);
            result.put("mediaURL", url);
            return result.toJSONString();

        } catch (Exception e) {
            log.error("查询剪辑合成作业详情失败", e);
            throw new RuntimeException("查询剪辑合成作业详情失败: " + e.getMessage());
        }
    }

}