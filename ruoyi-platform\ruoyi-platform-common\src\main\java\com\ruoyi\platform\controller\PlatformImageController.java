package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.service.IPlatformImageService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 形象管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
@RestController
@RequestMapping("/platform/image")
@Tag(name = "【形象管理】管理")
public class PlatformImageController extends BaseController
{

    @Autowired
    private IPlatformImageService platformImageService;

    /**
     * 查询形象管理列表
     */
    @Operation(summary = "查询形象管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:image:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformImage platformImage)
    {
        startPage();
        List<PlatformImage> list = platformImageService.selectPlatformImageList(platformImage);
        return getDataTable(list);
    }

    /**
     * 导出形象管理列表
     */
    @Operation(summary = "导出形象管理列表")
    @PreAuthorize("@ss.hasPermi('platform:image:export')")
    @Log(title = "导出形象管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformImage platformImage)
    {
        List<PlatformImage> list = platformImageService.selectPlatformImageList(platformImage);
        ExcelUtil<PlatformImage> util = new ExcelUtil<PlatformImage>(PlatformImage.class);
        util.exportExcel(response, list, "形象管理数据");
    }

    /**
     * 获取形象管理详细信息
     */
    @Operation(summary = "获取形象管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:image:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(platformImageService.selectPlatformImageByImageId(imageId));
    }

    /**
     * 删除形象管理
     */
    @Operation(summary = "删除形象管理")
    //@PreAuthorize("@ss.hasPermi('platform:image:remove')")
    @Log(title = "删除形象管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable( name = "imageIds" ) Long[] imageIds) 
    {
        return toAjax(platformImageService.deletePlatformImageByImageIds(imageIds));
    }

    /**
     * 根据形象地址获取详细信息（如预签名URL和MD5）
     */
    @Operation(summary = "根据形象地址生成临时凭证")
    @GetMapping("imageDetail")
    public AjaxResult imageDetail(@RequestParam String imageAddress) {
        try {
            return success(platformImageService.getImageDetailByAddress(imageAddress));
        } catch (Exception e) {
            return error("在当前服务没有找到该形象素材信息！");
        }
    }

    /**
     * 初始化形象分片上传
     */
    @Operation(summary = "初始化形象分片上传")
    @PostMapping("/chunk/init")
    public AjaxResult initImageChunk(@RequestParam String imageName, @RequestParam String fileName,
            @RequestParam long fileSize) {
        try {
            Object result = platformImageService.initOssUpload(imageName, fileName, fileSize);
            return success(result);
        } catch (Exception e) {
            return error("初始化分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传形象分片
     */
    @Operation(summary = "上传形象分片")
    @PostMapping("/chunk/upload")
    public AjaxResult uploadImageChunk(@RequestParam String uploadId, @RequestParam String filePath, 
            @RequestParam int chunkIndex, @RequestParam MultipartFile chunk) {
        try {
            Object result = platformImageService.uploadOssChunk(uploadId, filePath, chunkIndex, chunk);
            return success(result);
        } catch (Exception e) {
            return error("上传分片失败: " + e.getMessage());
        }
    }

    /**
     * 完成形象分片上传
     */    
    @Operation(summary = "完成形象分片上传")
    @PostMapping("/chunk/complete")
    public AjaxResult completeImageChunk(@RequestParam String imageName, @RequestParam String uploadId,
            @RequestParam String filePath, @RequestBody List<SysFilePartETag> partETags) {
        try {
            Object result = platformImageService.completeOssUpload(imageName, uploadId, filePath, partETags);
            return success(result);
        } catch (Exception e) {
            return error("完成分片上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 编辑形象
     */
    @Operation(summary = "编辑形象")
    @PostMapping("/{imageId}/edit/init")
    public AjaxResult startEditImage( @PathVariable Long imageId,@RequestParam String fileName,
        @RequestParam long fileSize ) {
        try {
            Object result = platformImageService.startEditImage(imageId, fileName, fileSize);
            return success(result);
        } catch (Exception e) {
            return error("初始化编辑失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成形象编辑
     */
    @Operation(summary = "完成形象编辑")
    @PostMapping("/{imageId}/edit/complete")
    public AjaxResult finishEditImage( @PathVariable Long imageId, @RequestParam(required = false) String uploadId,
        @RequestParam(required = false) String filePath, @RequestBody List<SysFilePartETag> partETags,
        @RequestParam(required = false) String imageName) {
        try {
            Object result = platformImageService.finishEditImage(imageId, uploadId, filePath, partETags, imageName);
            return success(result);
        } catch (Exception e) {
            return error("完成编辑失败: " + e.getMessage());
        }
    }
}
