<mxfile host="65bd71144e">
    <diagram id="eUwQ9UTd_wFMP36Shy1F" name="第 1 页">
        <mxGraphModel dx="1173" dy="766" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="获取支付链接" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="140" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="用户支付" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="220" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="5" target="7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="进入支付回调" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="310" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="7" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="验签" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="400" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="用户定义的回调业务" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="580" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="13" target="9">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="更改订单状态" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="490" width="110" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>