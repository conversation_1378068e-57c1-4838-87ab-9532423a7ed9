package com.ruoyi.platform.utils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import com.ruoyi.common.utils.StringUtils;

/*
 * 处理 List 转为 String 存入数据库
 */
@MappedTypes(List.class)
public class ListTypeHandler extends BaseTypeHandler<List<Long>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType)
            throws SQLException {
        // 处理 List 转为 String 存入数据库
        ps.setString(i, parameter.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return !StringUtils.isEmpty(value)
                ? Arrays.stream(value.split(",")).map(Long::valueOf).collect(Collectors.toList())
                : new ArrayList<>();
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return !StringUtils.isEmpty(value)
                ? Arrays.stream(value.split(",")).map(Long::valueOf).collect(Collectors.toList())
                : new ArrayList<>();
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return !StringUtils.isEmpty(value)
                ? Arrays.stream(value.split(",")).map(Long::valueOf).collect(Collectors.toList())
                : new ArrayList<>();
    }

}
