package com.ruoyi.platform.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.platform.domain.PlatformAnchor;

/**
 * 智能主播Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface IPlatformAnchorService 
{
    /**
     * 查询智能主播
     * 
     * @param anchorId 智能主播主键
     * @return 智能主播
     */
    public PlatformAnchor selectPlatformAnchorByAnchorId(Long anchorId);

    /**
     * 查询智能主播列表
     * 
     * @param platformAnchor 智能主播
     * @return 智能主播集合
     */
    public List<PlatformAnchor> selectPlatformAnchorList(PlatformAnchor platformAnchor);

    /**
     * 新增智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    public int insertPlatformAnchor(PlatformAnchor platformAnchor);

    /**
     * 修改智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    public int updatePlatformAnchor(PlatformAnchor platformAnchor);

    /**
     * 批量删除智能主播
     * 
     * @param anchorIds 需要删除的智能主播主键集合
     * @return 结果
     */
    public int deletePlatformAnchorByAnchorIds(Long[] anchorIds);

    /**
     * 删除智能主播信息
     * 
     * @param anchorId 智能主播主键
     * @return 结果
     */
    public int deletePlatformAnchorByAnchorId(Long anchorId);

    //训练智能主播
    public String trainAnchorWithProjectData(Long projectId) throws Exception;

    //根据项目ID查询数据
    public PlatformAnchor selectPlatformAnchorByProjectId(Long projectId);

    //获取智能主播知识库
    public JSONObject getKnowledgeBase(Long projectId);

    //智能主播回复
    public String generateReply(JSONObject knowledgeBase, List<String> questions,List<String> historyReples) throws Exception;

    /**
     * 删除智能主播知识库中的特定字段
     * @param anchorId 主键
     * @param anchorRepository 要删除的字段类别
     * @return 结果
     */
    public int deleteFieldFromAnchorRepository(Integer anchorId, String anchorRepository);

    /**
     * 根据直播ID获取智能主播信息
     *
     * @param liveId 直播ID
     * @return 智能主播信息列表
     */
    public List<PlatformAnchor> getAnchorsByLiveId(Long liveId);
}
