package com.ruoyi.video.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.video.domain.MediaEdit;
import com.ruoyi.video.mapper.MediaEditMapper;
import com.ruoyi.video.service.IMediaEditService;

/**
 * 媒体编辑表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class MediaEditServiceImpl implements IMediaEditService {
    @Autowired
    private MediaEditMapper mediaEditMapper;

    /**
     * 查询媒体编辑表
     *
     * @param jobId 媒体编辑表主键
     * @return 媒体编辑表
     */
    @Override
    public MediaEdit selectMediaEditByJobId(String jobId) {
        return mediaEditMapper.selectMediaEditByJobId(jobId);
    }

    /**
     * 查询媒体编辑表列表
     *
     * @param mediaEdit 媒体编辑表
     * @return 媒体编辑表
     */
    @Override
    public List<MediaEdit> selectMediaEditList(MediaEdit mediaEdit) {
        return mediaEditMapper.selectMediaEditList(mediaEdit);
    }

    /**
     * 新增媒体编辑表
     *
     * @param mediaEdit 媒体编辑表
     * @return 结果
     */
    @Override
    public int insertMediaEdit(MediaEdit mediaEdit) {
        mediaEdit.setCreateTime(DateUtils.getNowDate());
        mediaEdit.setCreateBy(SecurityUtils.getUsername());
        return mediaEditMapper.insertMediaEdit(mediaEdit);
    }

    /**
     * 修改媒体编辑表
     *
     * @param mediaEdit 媒体编辑表
     * @return 结果
     */
    @Override
    public int updateMediaEdit(MediaEdit mediaEdit) {
        mediaEdit.setUpdateTime(DateUtils.getNowDate());
        //第三方回调的时候不会存在用户名称
        try {
            String username = SecurityUtils.getUsername();
            mediaEdit.setUpdateBy(username);
        } catch (Exception e) {
        }
        return mediaEditMapper.updateMediaEdit(mediaEdit);
    }

    /**
     * 批量删除媒体编辑表
     *
     * @param jobIds 需要删除的媒体编辑表主键
     * @return 结果
     */
    @Override
    public int deleteMediaEditByJobIds(String[] jobIds) {
        return mediaEditMapper.deleteMediaEditByJobIds(jobIds);
    }

    /**
     * 删除媒体编辑表信息
     *
     * @param jobId 媒体编辑表主键
     * @return 结果
     */
    @Override
    public int deleteMediaEditByJobId(String jobId) {
        return mediaEditMapper.deleteMediaEditByJobId(jobId);
    }

    /**
     * 根据项目ID批量删除媒体编辑表
     *
     * @param projectIds 需要删除的项目ID集合
     * @return 结果
     */
    @Override
    public int deleteMediaEditByProjectIds(String[] projectIds) {
        return mediaEditMapper.deleteMediaEditByProjectIds(projectIds);
    }
}
