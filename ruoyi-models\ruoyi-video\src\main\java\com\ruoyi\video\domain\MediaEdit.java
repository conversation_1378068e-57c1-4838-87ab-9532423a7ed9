package com.ruoyi.video.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 媒体编辑表对象 media_edit
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "媒体编辑表对象")
public class MediaEdit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务编号 */
    @Schema(title = "任务编号")
    private String jobId;

    /** 标题 */
    @Schema(title = "标题")
    @Excel(name = "标题")
    private String title;

    /** 项目编号 */
    @Schema(title = "项目编号")
    @Excel(name = "项目编号")
    private String projectId;

    /** 状态 */
    @Schema(title = "状态")
    @Excel(name = "状态")
    private String status;

    /** 描述 */
    @Schema(title = "描述")
    @Excel(name = "描述")
    private String description;

    /** 封面URL */
    @Schema(title = "封面URL")
    @Excel(name = "封面URL")
    private String coverUrl;
    /** 合成结果url */
    @Schema(title = "合成结果url")
    @Excel(name = "合成结果url")
    private String mediaUrl;

    /** 开始时间 */
    @Schema(title = "开始时间")
    private String startTime;

    /** 结束时间 */
    @Schema(title = "结束时间")
    private String endTime;

}
