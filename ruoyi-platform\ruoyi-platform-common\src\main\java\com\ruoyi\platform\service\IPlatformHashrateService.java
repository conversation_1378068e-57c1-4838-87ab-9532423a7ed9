package com.ruoyi.platform.service;

import java.util.List;

import com.ruoyi.platform.domain.PlatformHashrate;

/**
 * 用户算力点数Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface IPlatformHashrateService 
{
    /**
     * 查询用户算力点数
     * 
     * @param hashrateId 用户算力点数主键
     * @return 用户算力点数
     */
    public PlatformHashrate selectPlatformHashrateByHashrateId(Long hashrateId);

    /**
     * 查询用户算力点数列表
     * 
     * @param platformHashrate 用户算力点数
     * @return 用户算力点数集合
     */
    public List<PlatformHashrate> selectPlatformHashrateList(PlatformHashrate platformHashrate);

    /**
     * 新增用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    public int insertPlatformHashrate(PlatformHashrate platformHashrate);

    /**
     * 修改用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    public int updatePlatformHashrate(PlatformHashrate platformHashrate);

    /**
     * 批量删除用户算力点数
     * 
     * @param hashrateIds 需要删除的用户算力点数主键集合
     * @return 结果
     */
    public int deletePlatformHashrateByHashrateIds(Long[] hashrateIds);

    /**
     * 删除用户算力点数信息
     * 
     * @param hashrateId 用户算力点数主键
     * @return 结果
     */
    public int deletePlatformHashrateByHashrateId(Long hashrateId);

    //给客户充值算力点
    public int consumptionPlatformHashrate(PlatformHashrate platformHashrate);

    /**
     * 扣除用户的算力点数
     * @param userName 用户ID
     * @param hashrateBalance 要扣除的算力点数
     * @return 扣除操作是否成功
     */
    public void deductHashratePoints(Long hashrateId, Long points, String consumptionTitle);

    /**
     * 根据用户名称获取算力ID
     * 
     * @param userName 用户ID（字符串类型）
     * @return 用户的算力ID
     */
    public Long getHashrateIdByUserId(String userName);

    //如果该算力用户不再使用进行强制删除算力用户和算力用户的消费、充值记录
    public int deleteByUserName(String userName);

    //批量删除算力用户 充值、消费记录
    public int deleteByUserNames(String[] userNames);

    //为用户进行 初始化操作 在算力用户中加入当前该用户的数据
    public String initializeUserHashrate(String userName);

    // 批量生成算力卡号并导出
    public List<PlatformHashrate> generateCards(int count, String balance);

    // 输入卡号激活
    public PlatformHashrate useCard(String cardNumber);
}
