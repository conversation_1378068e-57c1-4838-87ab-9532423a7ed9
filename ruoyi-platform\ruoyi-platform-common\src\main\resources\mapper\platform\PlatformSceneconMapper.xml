<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformSceneconMapper">
    
    <resultMap type="PlatformScenecon" id="PlatformSceneconResult">
        <result property="sceneconId"    column="scenecon_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="sceneconName"    column="scenecon_name"    />
        <result property="sceneconInteractionId"    column="scenecon_interaction_id"  typeHandler="com.ruoyi.platform.utils.ListTypeHandler"  />
        <result property="sceneconQuestionsId"    column="scenecon_questions_id"   typeHandler="com.ruoyi.platform.utils.ListTypeHandler" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformSceneconVo">
        select scenecon_id, project_id, scenecon_name, scenecon_interaction_id, scenecon_questions_id, s.create_by, s.create_time, s.update_by, s.update_time, s.remark from platform_scenecon s left join sys_user u on u.user_name = s.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformSceneconList" parameterType="PlatformScenecon" resultMap="PlatformSceneconResult">
        <include refid="selectPlatformSceneconVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="sceneconName != null  and sceneconName != ''"> and scenecon_name like concat('%', #{sceneconName}, '%')</if>
            ${params.dataScope}
            <if test="sceneconInteractionId != null  and sceneconInteractionId != ''"> and scenecon_interaction_id = #{sceneconInteractionId}</if>
            <if test="sceneconQuestionsId != null  and sceneconQuestionsId != ''"> and scenecon_questions_id = #{sceneconQuestionsId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformSceneconBySceneconId" parameterType="Long" resultMap="PlatformSceneconResult">
        <include refid="selectPlatformSceneconVo"/>
        where scenecon_id = #{sceneconId}
    </select>
        
    <insert id="insertPlatformScenecon" parameterType="PlatformScenecon" useGeneratedKeys="true" keyProperty="sceneconId">
        insert into platform_scenecon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="sceneconName != null">scenecon_name,</if>
            <if test="sceneconInteractionId != null">scenecon_interaction_id,</if>
            <if test="sceneconQuestionsId != null">scenecon_questions_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="sceneconName != null">#{sceneconName},</if>
            <if test="sceneconInteractionId != null">#{sceneconInteractionId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="sceneconQuestionsId != null">#{sceneconQuestionsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformScenecon" parameterType="PlatformScenecon">
        update platform_scenecon s
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="sceneconName != null">scenecon_name = #{sceneconName},</if>
            <if test="sceneconInteractionId != null">scenecon_interaction_id = #{sceneconInteractionId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="sceneconQuestionsId != null">scenecon_questions_id = #{sceneconQuestionsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where s.scenecon_id = #{sceneconId}
    </update>

    <delete id="deletePlatformSceneconBySceneconId" parameterType="Long">
        delete from platform_scenecon where scenecon_id = #{sceneconId}
    </delete>

    <delete id="deletePlatformSceneconBySceneconIds" parameterType="String">
        delete from platform_scenecon where scenecon_id in 
        <foreach item="sceneconId" collection="array" open="(" separator="," close=")">
            #{sceneconId}
        </foreach>
    </delete>
</mapper>