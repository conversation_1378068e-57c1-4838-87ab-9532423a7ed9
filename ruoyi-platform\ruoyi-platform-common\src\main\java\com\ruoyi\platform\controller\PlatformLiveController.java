package com.ruoyi.platform.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.domain.vo.PlatformLiveVo;
import com.ruoyi.platform.service.IPlatformLiveService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 直播管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-08
 */
@RestController
@RequestMapping("/platform/live")
@Tag(name = "【直播管理】管理")
public class PlatformLiveController extends BaseController {
    @Autowired
    private IPlatformLiveService platformLiveService;

    /**
     * 查询直播管理列表
     */
    @Operation(summary = "查询直播管理列表")
    // @PreAuthorize("@ss.hasPermi('platform:live:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformLive platformLive) {
        startPage();
        List<PlatformLive> list = platformLiveService.selectPlatformLiveList(platformLive);
        return getDataTable(list);
    }

    /**
     * 查询直播相关音频列表
     */
    @Operation(summary = "查询直播相关音频列表")
    @Deprecated(since = "1.1.0", forRemoval = true)
    // @PreAuthorize("@ss.hasPermi('platform:live:list')")
    @GetMapping("/{liveId}/audioList")
    public R<Map<Long, List<Long>>> audioList(@PathVariable("liveId") Long liveId) {
        return R.ok(platformLiveService.selectAudioList(liveId));
    }

    /**
     * 导出直播管理列表
     */
    @Operation(summary = "导出直播管理列表")
    @PreAuthorize("@ss.hasPermi('platform:live:export')")
    @Log(title = "导出直播管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformLive platformLive) {
        List<PlatformLive> list = platformLiveService.selectPlatformLiveList(platformLive);
        ExcelUtil<PlatformLive> util = new ExcelUtil<PlatformLive>(PlatformLive.class);
        util.exportExcel(response, list, "直播管理数据");
    }

    /**
     * 获取直播管理详细信息
     */
    @Operation(summary = "获取直播管理详细信息")
    // @PreAuthorize("@ss.hasPermi('platform:live:query')")
    @GetMapping(value = "/{liveId}")
    public AjaxResult getInfo(@PathVariable("liveId") Long liveId) {
        return success(platformLiveService.selectPlatformLiveByLiveId(liveId));
    }

    /**
     * 获取单场直播所有信息
     */
    @Operation(summary = "获取单场直播所有信息")
    @Deprecated(since = "1.1.0", forRemoval = true)
    // @PreAuthorize("@ss.hasPermi('platform:live:query')")
    @GetMapping(value = "/vo/{liveId}")
    public R<PlatformLiveVo> getAllInfoVo(@PathVariable("liveId") Long liveId) {
        return R.ok(platformLiveService.selectPlatformLiveVoByLiveId(liveId));
    }

    /**
     * 新增直播管理
     */
    @Operation(summary = "新增直播管理")
    //@PreAuthorize("@ss.hasPermi('platform:live:add')")
    @Log(title = "新增直播管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformLive platformLive) {
        platformLive.setCreateBy(getUsername());
        platformLive.setUpdateBy(getUsername());
        return toAjax(platformLiveService.insertPlatformLive(platformLive));
    }

    /**
     * 修改直播管理
     */
    @Operation(summary = "修改直播管理")
    //@PreAuthorize("@ss.hasPermi('platform:live:edit')")
    //@Log(title = "直播管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformLive platformLive) {
        platformLive.setUpdateBy(getUsername());
        return toAjax(platformLiveService.updatePlatformLive(platformLive));
    }

    /**
     * 删除直播管理
     */
    @Operation(summary = "删除直播管理")
    //@PreAuthorize("@ss.hasPermi('platform:live:remove')")
    @Log(title = "删除直播管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{liveIds}")
    public AjaxResult remove(@PathVariable(name = "liveIds") Long[] liveIds) {
        return toAjax(platformLiveService.deletePlatformLiveByLiveIds(liveIds));
    }

    /**
     * 创建者查询直播
     */
    @Operation(summary = "根据创建人查询直播信息")
    // @PreAuthorize("@ss.hasPermi('platform:live:list')")
    @GetMapping("/listCreateBy/{createBy}")
    public AjaxResult listCreateBy(@PathVariable("createBy") String createBy) {
        List<PlatformLive> live = platformLiveService.selectPlatformLiveBycreateBy(createBy);
        return success(live);
    }

    /**
     * 修改人查询直播
     */
    @Operation(summary = "根据修改人查询直播信息")
    // @PreAuthorize("@ss.hasPermi('platform:live:list')")
    @GetMapping("/listUpdateBy/{updateBy}")
    public AjaxResult listUpdateBy(@PathVariable("updateBy") String updateBy) {
        List<PlatformLive> live = platformLiveService.selectPlatformLiveByupdateBy(updateBy);
        return success(live);
    }

    /**
     * 根据直播Id打包分类下音频
     *
     * @param liveId 直播ID
     * @param response HTTP响应对象
     */
    @Operation(summary = "根据直播Id打包分类下音频")
    @PostMapping(value = "/{liveId}/audios/download", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE) 
    public void downloadAllAudiosByLiveId(@PathVariable("liveId") Long liveId, HttpServletResponse response,
            @RequestBody(required = false) Long[] audios) {
        try {
            if (audios != null && audios.length == 0) {
                throw new ServiceException("请选择要下载的音频文件！");
            }
            platformLiveService.downloadAllAudiosByLiveId(liveId, response, audios);
        } catch (ServiceException e) {
            response.setStatus(HttpStatus.PARTIAL_CONTENT.value());
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write(e.getMessage());
            } catch (IOException ioException) {}
        } catch (Exception e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("下载失败，请稍后重试: " + e.getMessage());
            } catch (IOException ioException) {}
        }
    }
}
