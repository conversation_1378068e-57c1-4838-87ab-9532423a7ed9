package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformCategory;

/**
 * 分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface IPlatformCategoryService {
    /**
     * 查询分类
     * 
     * @param categoryId 分类主键
     * @return 分类
     */
    public PlatformCategory selectPlatformCategoryByCategoryId(Long categoryId);

    /**
     * 查询分类列表
     * 
     * @param platformCategory 分类
     * @return 分类集合
     */
    public List<PlatformCategory> selectPlatformCategoryList(PlatformCategory platformCategory);

    /**
     * 新增分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    public int insertPlatformCategory(PlatformCategory platformCategory);

    /**
     * 修改分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    public int updatePlatformCategory(PlatformCategory platformCategory);

    /**
     * 删除分类信息
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public int deletePlatformCategoryByCategoryId(Long categoryId, Map<String, Object> params) throws Exception ;

    /**
     * 检查分类是否还有文案
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public boolean checkProjectHasCategory(Long categoryId);

    /**
     * 检查分类是否还有关键字
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public boolean checkProjectKeyWord(Long categoryId);

    /**
     * 检查分类是否还有音频
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public boolean checkProjectAudio(Long categoryId);

    //根据分类的ID查找多个分类名称
    public Map<Long, String> getCategoryTitlesByIds(int[] ids);

    /**
     * 根据直播ID获取分类信息
     *
     * @param liveId 直播ID
     * @return 分类信息列表
     */
    public List<PlatformCategory> getCategoriesByLiveId(Long liveId);
}
