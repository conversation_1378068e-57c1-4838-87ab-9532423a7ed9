package com.ruoyi.video.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.dto.TemplateRequestDTO;
import com.ruoyi.video.service.ITemplateService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 智能媒体服务(ICE)模板管理 前端控制器
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/video/template")
@Tag(name = "【模板管理】")
public class TemplateController {

    @Autowired
    private ITemplateService templateService;

    /**
     * 创建一个新模板
     * <p>
     * 接收包含模板详情的JSON对象，创建一个新的ICE模板。
     * </p>
     *
     * @param requestDTO 包含创建模板所需全部参数的数据传输对象。
     * @return 返回操作结果，成功时包含阿里云返回的新建模板信息。
     */
    @Operation(summary = "创建一个新模板")
    @PostMapping("/add")
    public AjaxResult addTemplate(@RequestBody TemplateRequestDTO requestDTO) {
        if (requestDTO == null) {
            return AjaxResult.error("请求参数不能为空");
        }
        try {
            String result = templateService.addTemplate(requestDTO);
            return AjaxResult.success("创建模板成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("创建模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板列表
     * <p>
     * 通过调用阿里云ICE(智能媒体服务)的 ListTemplates 接口，获取符合条件的模板列表。
     * 支持根据类型、状态、来源、关键字进行筛选和排序，并支持分页。
     * </p>
     *
     * @param pageNo       可选。当前页码。
     * @param pageSize     可选。分页大小。
     * @param type         可选。模板类型。
     * @param status       可选。模板状态。
     * @param createSource 可选。创建来源。
     * @param keyword      可选。搜索关键词。
     * @param sortType     可选。排序参数。
     * @return 返回一个包含了模板列表和分页信息的AjaxResult对象。
     */
    @Operation(summary = "获取模板列表")
    @GetMapping("/list")
    public AjaxResult listTemplates(
            @RequestParam(required = false) Long pageNo,
            @RequestParam(required = false) Long pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String createSource,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String sortType) {
        try {
            String result = templateService.listTemplates(pageNo, pageSize, type, status, createSource, keyword, sortType);
            return AjaxResult.success("查询模板列表成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("查询模板列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个模板的详细信息
     * <p>
     * 根据模板ID获取模板的详细信息。<br>
     * <strong>重要说明：</strong>当RelatedMediaidFlag=1时，会返回模板关联的素材信息（RelatedMediaids字段），
     * 该字段仅对普通模板有效。
     * </p>
     *
     * @param templateId 模板ID，必填参数
     * @param relatedMediaidFlag 相关媒体ID标记，默认值0。当值为1时返回模板关联素材（RelatedMediaids字段），仅普通模板有效
     * @return 返回包含模板详细信息的AjaxResult对象
     */
    @Operation(summary = "获取单个模板的详细信息")
    @GetMapping("/{templateId}")
    public AjaxResult getTemplate(
            @PathVariable String templateId,
            @RequestParam(required = false, defaultValue = "0") String relatedMediaidFlag) {
        if (templateId == null || templateId.trim().isEmpty()) {
            return AjaxResult.error("模板ID不能为空");
        }
        try {
            String result = templateService.getTemplate(templateId, relatedMediaidFlag);
            return AjaxResult.success("获取模板信息成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("获取模板信息失败: " + e.getMessage());
        }
    }

    /**
     * 修改模板
     *
     * @param requestDTO 包含修改模板所需全部参数的数据传输对象。
     * @return 返回操作结果，成功时包含阿里云返回的修改模板信息。
     */
    @Operation(summary = "修改模板")
    @PutMapping("/update")
    public AjaxResult updateTemplate(@RequestBody TemplateRequestDTO requestDTO) {
        if (requestDTO == null) {
            return AjaxResult.error("请求参数不能为空");
        }
        try {
            String result = templateService.updateTemplate(requestDTO);
            return AjaxResult.success("修改模板成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("修改模板失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板
     *
     * @param param 包含templateIds的Map对象
     * @return 返回操作结果，成功时包含阿里云返回的删除模板信息。
     */
    @Operation(summary = "删除模板")
    @PostMapping("/delete")
    public AjaxResult deleteTemplate(@RequestBody Map<String, String> param) {
        String templateIds = param.get("templateIds");
        if (templateIds == null || templateIds.trim().isEmpty()) {
            return AjaxResult.error("模板ID不能为空");
        }
        try {
            String result = templateService.deleteTemplate(templateIds);
            return AjaxResult.success("删除模板成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("删除模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板素材地址
     * <p>
     * 返回高级模板包中的素材地址，供高级模板编辑器使用，素材链接30分钟过期。
     * FileList为所需素材数组，不填则默认返回全部素材地址，最多返回400个。
     * </p>
     *
     * @param templateId 模板ID，从URL路径中获取
     * @param fileList 可选。所需文件列表，JSON格式字符串，如：["music.mp3","config.json","assets/1.jpg"]
     * @return 返回包含模板素材地址映射的AjaxResult对象
     */
    @Operation(summary = "获取模板素材地址")
    @GetMapping("/{templateId}/materials")
    public AjaxResult getTemplateMaterials(
            @PathVariable String templateId,
            @RequestParam(required = false) String fileList) {
        if (templateId == null || templateId.trim().isEmpty()) {
            return AjaxResult.error("模板ID不能为空");
        }
        try {
            String result = templateService.getTemplateMaterials(templateId, fileList);
            return AjaxResult.success("获取模板素材地址成功", JSON.parse(result));
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("模板不存在")) {
                return AjaxResult.error("指定的模板不存在，请检查模板ID是否正确")
                        .put("templateId", templateId)
                        .put("suggestion", "请先通过模板列表接口确认模板是否存在");
            } else if (errorMessage != null && errorMessage.contains("参数无效")) {
                return AjaxResult.error("模板ID格式无效，请检查ID格式是否正确")
                        .put("templateId", templateId);
            } else {
                return AjaxResult.error("获取模板素材地址失败: " + errorMessage);
            }
        }
    }
}