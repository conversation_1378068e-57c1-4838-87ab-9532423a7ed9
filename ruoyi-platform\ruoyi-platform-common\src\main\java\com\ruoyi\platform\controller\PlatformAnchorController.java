package com.ruoyi.platform.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformAnchor;
import com.ruoyi.platform.service.IPlatformAnchorService;
import com.ruoyi.platform.utils.TongYiQianWen;
import com.ruoyi.platform.utils.HashrateCostUtils.HashrateCost;

import io.reactivex.Flowable;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import reactor.core.publisher.Flux;

/**
 * 智能主播Controller
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
@RestController
@RequestMapping("/platform/anchor")
@Tag(name = "【智能主播】管理")
public class PlatformAnchorController extends BaseController {
    
    @Autowired
    private IPlatformAnchorService platformAnchorService;

    /**
     * 查询智能主播列表
     */
    @Operation(summary = "查询智能主播列表")
    // @PreAuthorize("@ss.hasPermi('platform:anchor:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlatformAnchor platformAnchor) {
        startPage();
        List<PlatformAnchor> list = platformAnchorService.selectPlatformAnchorList(platformAnchor);
        return getDataTable(list);
    }

    /**
     * 导出智能主播列表
     */
    @Operation(summary = "导出智能主播列表")
    @PreAuthorize("@ss.hasPermi('platform:anchor:export')")
    @Log(title = "导出智能主播", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformAnchor platformAnchor) {
        List<PlatformAnchor> list = platformAnchorService.selectPlatformAnchorList(platformAnchor);
        ExcelUtil<PlatformAnchor> util = new ExcelUtil<PlatformAnchor>(PlatformAnchor.class);
        util.exportExcel(response, list, "智能主播数据");
    }

    /**
     * 获取智能主播详细信息
     */
    @Operation(summary = "获取智能主播详细信息")
    // @PreAuthorize("@ss.hasPermi('platform:anchor:query')")
    @GetMapping(value = "/{anchorId}")
    public AjaxResult getInfo(@PathVariable("anchorId") Long anchorId) {
        return success(platformAnchorService.selectPlatformAnchorByAnchorId(anchorId));
    }

    /**
     * 新增智能主播
     */
    @Operation(summary = "新增智能主播")
    // @PreAuthorize("@ss.hasPermi('platform:anchor:add')")
    @Log(title = "新增智能主播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlatformAnchor platformAnchor) {
        platformAnchor.setCreateBy(getUsername());
        platformAnchor.setUpdateBy(getUsername());
        return toAjax(platformAnchorService.insertPlatformAnchor(platformAnchor));
    }

    /**
     * 修改智能主播
     */
    @Operation(summary = "修改智能主播")
    @Log(title = "编辑智能主播", businessType = BusinessType.UPDATE)
    @PutMapping("/{anchorId}")
    public AjaxResult edit(@PathVariable int anchorId, @RequestBody PlatformAnchor platformAnchor) {
        platformAnchor.setUpdateBy(getUsername());
        return toAjax(platformAnchorService.updatePlatformAnchor(platformAnchor));
    }

    /**
     * 删除智能主播
     */
    @Operation(summary = "删除智能主播")
    // @PreAuthorize("@ss.hasPermi('platform:anchor:remove')")
    @Log(title = "删除智能主播", businessType = BusinessType.DELETE)
    @DeleteMapping("/{anchorIds}")
    public AjaxResult remove(@PathVariable(name = "anchorIds") Long[] anchorIds) {
        return toAjax(platformAnchorService.deletePlatformAnchorByAnchorIds(anchorIds));
    }

    /**
     * 删除智能主播知识库中的特定字段
     */
    @Operation(summary = "删除智能主播知识库字段")
    @Log(title = "删除智能主播知识库字段", businessType = BusinessType.DELETE)
    @DeleteMapping("/{anchorId}/repository/{category}")
    public AjaxResult deleteFieldFromAnchorRepository(@PathVariable Integer anchorId, @PathVariable String category) {
        return toAjax(platformAnchorService.deleteFieldFromAnchorRepository(anchorId, category));
    }

    /**
     * 根据项目Id查询智能主播信息
     */
    @Operation(summary = "根据项目Id查询智能主播信息")
    @GetMapping("/selectPlatProjectId/{projectId}")
    public AjaxResult selectPlatProjectId(@PathVariable("projectId") Long projectId) {
        return success(platformAnchorService.selectPlatformAnchorByProjectId(projectId));
    }

    /**
     * 训练智能主播
     */
    @Operation(summary = "训练智能主播")
    @Log(title = "训练智能主播", businessType = BusinessType.INSERT)
    @PostMapping("/trainAnchor/{projectId}")
    @HashrateCost(description = "训练智能主播", expectedPoints = 15)
    public AjaxResult trainAnchor(@PathVariable Long projectId) {
        try {
            return success(platformAnchorService.trainAnchorWithProjectData(projectId));
        } catch (Exception e) {
            return AjaxResult.error("训练智能主播失败: " + e.getMessage());
        }
    }

    /**
     * 根据弹幕信息生成智能主播回复
     * 
     * @param requestBody 包含弹幕信息的请求体
     * @param projectId   项目ID
     * @return 智能主播的回复
     */
    @Operation(summary = "根据弹幕信息生成智能主播回复")
    @Anonymous
    @PostMapping(path = "/reply/{projectId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @HashrateCost(description = "智能主播问答", expectedPoints = 8)
    public Flux<String> generateReply(@RequestBody Map<String, List<String>> requestBody, @PathVariable Long projectId) {
        SecurityUtils.getUsername();
        try {
            List<String> questions = requestBody.get("questions");
            if (questions == null || questions.isEmpty()) {
                throw new ServiceException("没有弹幕信息！");
            }
            List<String> historyReples = requestBody.get("historyReples");
            if (historyReples != null && historyReples.size() > 3) {
                historyReples = historyReples.subList(0, 3);
            }
            JSONObject knowledgeBase = platformAnchorService.getKnowledgeBase(projectId);
            // 调用 productDescQa 方法生成回复
            Flowable<String> reply = TongYiQianWen.productDescQaAsync(knowledgeBase, questions, historyReples);
            return Flux.from(reply);
        } catch (Exception e) {
            throw new ServiceException("智能主播回复失败，" + e.getMessage());
        }
    }
}
