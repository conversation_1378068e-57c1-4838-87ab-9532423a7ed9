package com.ruoyi.platform.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CacheUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.platform.domain.PlatformConsumption;
import com.ruoyi.platform.domain.PlatformHashrate;
import com.ruoyi.platform.mapper.PlatformConsumptionMapper;
import com.ruoyi.platform.mapper.PlatformHashrateMapper;
import com.ruoyi.platform.service.IPlatformHashrateService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 用户算力点数Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class PlatformHashrateServiceImpl implements IPlatformHashrateService 
{
    
    @Autowired
    private PlatformHashrateMapper platformHashrateMapper;

    @Autowired
    private PlatformConsumptionMapper platformConsumptionMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    private static final String ERROR_COUNT_KEY_PREFIX = "card_error_count:";
    private static final String ERROR_TIME_KEY_PREFIX = "card_error_time:";
    private static final int MAX_ERROR_COUNT = 3;
    private static final long BLOCK_INTERVAL = 5 * 60 * 1000; // 5分钟，单位为毫秒
    private static final long ERROR_INTERVAL = 5 * 60 * 1000; // 5分钟，单位为毫秒

    /**
     * 查询用户算力点数
     * 
     * @param hashrateId 用户算力点数主键
     * @return 用户算力点数
     */
    @Override
    public PlatformHashrate selectPlatformHashrateByHashrateId(Long hashrateId)
    {
        return platformHashrateMapper.selectPlatformHashrateByHashrateId(hashrateId);
    }

    /**
     * 查询用户算力点数列表
     * 
     * @param platformHashrate 用户算力点数
     * @return 用户算力点数
     */
    @Override
    public List<PlatformHashrate> selectPlatformHashrateList(PlatformHashrate platformHashrate)
    {
        return platformHashrateMapper.selectPlatformHashrateList(platformHashrate);
    }

    /**
     * 新增用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    @Override
    public int insertPlatformHashrate(PlatformHashrate platformHashrate)
    {
        platformHashrate.setCreateTime(DateUtils.getNowDate());
        platformHashrate.setUpdateTime(DateUtils.getNowDate());
        return platformHashrateMapper.insertPlatformHashrate(platformHashrate);
    }

    /**
     * 修改用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    @Override
    public int updatePlatformHashrate(PlatformHashrate platformHashrate)
    {
        platformHashrate.setUpdateTime(DateUtils.getNowDate());
        return platformHashrateMapper.updatePlatformHashrate(platformHashrate);
    }

    /**
     * 批量删除用户算力点数
     * 
     * @param hashrateIds 需要删除的用户算力点数主键
     * @return 结果
     */
    @Override
    public int deletePlatformHashrateByHashrateIds(Long[] hashrateIds)
    {
        return platformHashrateMapper.deletePlatformHashrateByHashrateIds(hashrateIds);
    }

    /**
     * 删除用户算力点数信息
     * 
     * @param hashrateId 用户算力点数主键
     * @return 结果
     */
    @Override
    public int deletePlatformHashrateByHashrateId(Long hashrateId)
    {
        return platformHashrateMapper.deletePlatformHashrateByHashrateId(hashrateId);
    }

    //充值算力点
    @Override
    public int consumptionPlatformHashrate(PlatformHashrate platformHashrate) {
        platformHashrate.setCreateBy(SecurityUtils.getUsername()); // 充值人
        String currentBalanceStr = platformHashrate.getHashrateBalance(); // 获取到算力余额
        String rechargeAmountStr = platformHashrate.getRechargedHashrate(); // 获取到充值金额
        // 验证充值金额是否包含小数点
        if (rechargeAmountStr.contains(".")) {
            throw new ServiceException("充值金额不能包含小数点，请输入有效的数字！");
        }
        // 字符串转换整数类型
        long currentBalance;
        long rechargeAmount;
        try {
            currentBalance = Long.parseLong(currentBalanceStr);
            rechargeAmount = Long.parseLong(rechargeAmountStr);
        } catch (NumberFormatException e) {
            throw new ServiceException("充值金额格式不正确，请输入有效的整数！");
        }
        // 验证充值金额是否为正数
        if (rechargeAmount <= 0) {
            throw new ServiceException("充值金额必须大于零！");
        }
        // 验证充值金额是否超过100万
        long maxRechargeAmount = 1000000;
        if (rechargeAmount > maxRechargeAmount) {
            throw new ServiceException("充值金额不能超过100万！");
        }
        // 累加用户算力点
        long newBalance = currentBalance + rechargeAmount;
        platformHashrate.setHashrateBalance(String.valueOf(newBalance));
        int updateResult = updatePlatformHashrate(platformHashrate);
        if (updateResult > 0) {
            // 加入用户充值记录
            PlatformConsumption consumption = new PlatformConsumption();
            consumption.setUserName(platformHashrate.getUserName()); // 充值账户
            consumption.setConsumptionTitle("给用户充值算力点");
            consumption.setConsumptionStaus("0"); // 0 充值 1 消费 
            consumption.setConsumptionHashrate(String.valueOf(rechargeAmount)); // 充值数值
            consumption.setConsumptionResidue(String.valueOf(newBalance)); // 剩余算力值 
            consumption.setCreateBy(SecurityUtils.getUsername()); // 充值人
            consumption.setCreateTime(DateUtils.getNowDate()); // 充值时间
            consumption.setUpdateBy(SecurityUtils.getUsername());
            consumption.setUpdateTime(DateUtils.getNowDate()); 
            platformConsumptionMapper.insertPlatformConsumption(consumption); // 插入充值记录
        }
        return updateResult > 0 ? 1 : 0;
    }
    

    //根据用户查询算力用户
    @Override
    public Long getHashrateIdByUserId(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new ServiceException("算力用户不能为空！");
        }
        // 根据用户查询
        PlatformHashrate hashrate = platformHashrateMapper.selectByUserName(userName);
        return hashrate != null ? hashrate.getHashrateId() : null;
    }

    @Override
    @Transactional
    public void deductHashratePoints(Long hashrateId, Long points, String title) {
        try {
            // 直接根据hashrateId获取用户信息
            PlatformHashrate hashrate = platformHashrateMapper.selectPlatformHashrateByHashrateId(hashrateId);
            if (hashrate == null) {
                throw new ServiceException("算力用户不存在，联系管理员进行注册！");
            }
            
            // 获取用户名，用于记录消费信息
            String userName = hashrate.getUserName();
            
            // 将算力余额从字符串转换为长整型
            long currentBalance;
            try {
                currentBalance = Long.parseLong(hashrate.getHashrateBalance());
            } catch (NumberFormatException e) {
                throw new ServiceException("算力余额格式错误：" + hashrate.getHashrateBalance());
            }
            
            // 检查用户是否有足够的算力点数
            if (currentBalance < points) {
                throw new ServiceException("您算力点不足暂时无法使用此功能，请联系管理员进行充值！");
            }
            
            // 扣除算力点数
            long newBalance = currentBalance - points;
            hashrate.setHashrateBalance(String.valueOf(newBalance));
            
            // 尝试获取当前登录用户名
            String currentUserName;
            try {
                currentUserName = SecurityUtils.getUsername();
            } catch (Exception e) {
                // 如果获取不到当前用户，则默认使用算力账户的用户名
                currentUserName = userName;
            }
            
            hashrate.setUpdateBy(currentUserName); // 设置更新人为当前操作人
            hashrate.setUpdateTime(DateUtils.getNowDate()); // 设置更新时间
            
            // 更新数据库
            int updated = platformHashrateMapper.updatePlatformHashrate(hashrate);
            if (updated <= 0) {
                throw new ServiceException("更新算力账户失败");
            }
            
            // 记录消费详情，使用被扣费用户的名称，而非当前操作人
            recordConsumption(userName, title, points, newBalance, currentUserName);
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常: " + e.getMessage());
        }
    }

    //消费方法 - 增加用户名参数
    private void recordConsumption(String userName, String title, Long points, Long newBalance, String operatorName) {
        try {
            PlatformConsumption consumption = new PlatformConsumption();
            consumption.setUserName(userName); // 设置为被扣费的用户名
            consumption.setConsumptionTitle(title); // 消费标题
            consumption.setConsumptionStaus("1"); // 1 表示消费
            consumption.setConsumptionHashrate(String.valueOf(points)); // 消费数值
            consumption.setConsumptionResidue(String.valueOf(newBalance)); // 剩余算力值
            consumption.setCreateBy(operatorName); // 创建人为当前操作人
            consumption.setCreateTime(DateUtils.getNowDate()); // 消费时间
            consumption.setUpdateBy(operatorName); // 更新人为当前操作人
            consumption.setUpdateTime(DateUtils.getNowDate()); 
            
            // 插入消费记录
            platformConsumptionMapper.insertPlatformConsumption(consumption);
        } catch (Exception e) {
            //logger.error("记录算力消费失败: {}", e.getMessage());
            // 这里不抛出异常，因为主要功能已经完成，记录失败不应导致整个事务回滚
        }
    }

     //批量删除算力用户 充值、消费记录   
     @Transactional 
     @Override
     public int deleteByUserNames(String[] userNames) {
         return platformHashrateMapper.deleteByUserNames(userNames);
     }

    //如果该算力用户不再使用进行强制删除算力用户和算力用户的消费、充值记录
    @Transactional
    @Override
    public int deleteByUserName(String userName) {
        //删除之前先查询一下删除的数据是否存在
        List<Map<String, Object>> hashrateAndTitle = platformHashrateMapper.getHashrateAndConsumptionTitleByUserId(userName);
        if(hashrateAndTitle.isEmpty()){
            throw new ServiceException("没有找到该算力用户以及相关数据！");
        }
        return platformHashrateMapper.deleteByUserName(userName);
    }

    //用户管理界面  选择某个用户进行初始化操作 则在算力菜单中中加入当前该用户信息
    @Transactional
    @Override
    public String initializeUserHashrate(String userName) {
        //初始化绑定 先查询一下当前用户存不存在
        SysUser sysUser = userService.selectUserByUserName(userName);
        if(sysUser == null){
            throw new ServiceException("该用户不存在！");
        }
        if (sysUser.getDeptId() != null && sysUser.getRoleId() != null) {
            throw new ServiceException("该用户已分配部门或角色，不能重复初始化！");
        }
        //验证当前用户绑没绑定 部门 角色 绑定了 就不初始化
        if(sysUser.getDeptId() == null && sysUser.getRoleId() == null){
            try {
                // 从系统参数中获取部门ID并设置
                String configDeptId = configService.selectConfigByKey("sys.index.registerDept");
                Long deptId = Long.parseLong(configDeptId);
                sysUser.setDeptId(deptId); // 设置部门ID
                userService.updateUser(sysUser); // 更新用户信息中的部门

                // 从系统参数中获取角色ID并分配角色
                String configRole = configService.selectConfigByKey("sys.index.registerRole");
                Long roleId = Long.parseLong(configRole); 
                userService.insertUserAuth(sysUser.getUserId(), new Long[]{roleId});
            } catch (Exception e) {
                throw new ServiceException("配置项部门或角色格式不正确！");
            }
        }
        PlatformHashrate user=platformHashrateMapper.selectByUserName(userName);
        if(user == null){
            PlatformHashrate platformHashrate = new PlatformHashrate();
            platformHashrate.setUserName(userName); //算力用户
            platformHashrate.setHashrateBalance("0"); //初始化用户余额为0
            platformHashrate.setCreateBy(SecurityUtils.getUsername()); //创建人
            platformHashrate.setCreateTime(DateUtils.getNowDate()); //创建时间
            platformHashrate.setUpdateBy(SecurityUtils.getUsername()); //修改人
            platformHashrate.setUpdateTime(DateUtils.getNowDate()); //修改时间
            int result = platformHashrateMapper.insertPlatformHashrate(platformHashrate);
            return result > 0 ? "初始化成功" : "初始化失败";
        } else {
            // 如果该用户在算力信息中已存在，提示信息
            throw new ServiceException("初始化失败，当前用户已存在，且已绑定角色和部门！");
        }
    }

    //批量创建卡号
    public List<PlatformHashrate> generateCards(int count, String balance) {
        // 检查创建数量限制
        if (count > 500) {
            throw new ServiceException("一次性最多创建 500 个卡号");
        }

        // 检查金额格式和范围
        try {
            int amount = Integer.parseInt(balance);
            if (amount < 0 || amount > 1000000) {
                throw new ServiceException("金额必须在 0 到 100 万之间，且不能有小数点");
            }
        } catch (NumberFormatException e) {
            throw new ServiceException("充值金额必须是整数，且不能有小数点");
        }

        List<PlatformHashrate> cards = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            String cardNumber;
            PlatformHashrate existingHashrate;
            do {
                cardNumber = generateVideoName();
                existingHashrate = platformHashrateMapper.selectByCardNumber(cardNumber);
            } while (existingHashrate != null);

            PlatformHashrate hashrate = new PlatformHashrate();
            hashrate.setHashrateCard(cardNumber);
            hashrate.setHashrateBalance(balance); 
            hashrate.setHashrateStatus("0"); 
            hashrate.setCreateBy(SecurityUtils.getUsername());
            hashrate.setCreateTime(DateUtils.getNowDate()); 
            hashrate.setUpdateBy(SecurityUtils.getUsername());
            hashrate.setUpdateTime(DateUtils.getNowDate()); 
            cards.add(hashrate);
        }

        platformHashrateMapper.batchInsert(cards);
        return cards;
    }

    /**
     * 生成指定长度的纯数字随机字符串
     *
     * @param length 字符串长度
     * @return 纯数字随机字符串
     */
    public String generateVideoName() {
        String characters = "0123456789";
        StringBuilder randomString = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            int index = (int) (Math.random() * characters.length());
            randomString.append(characters.charAt(index));
        }
        return randomString.toString();
    }

    /**
     * 根据卡号充值算力点
     *
     * @param cardNumber 卡号
     * @return 充值结果
     */
    @Override
    @Transactional
    public PlatformHashrate useCard(String cardNumber) {
        String currentUserName = SecurityUtils.getUsername();
        // 检查当前用户是否被禁止使用卡号（基于用户名而不是卡号）
        Integer errorCount = null;
        Long lastErrorTime = null;
        // 获取当前用户的错误计数
        Object errorCountWrapper = CacheUtils.get("card_error_cache", ERROR_COUNT_KEY_PREFIX + currentUserName);
        if (errorCountWrapper != null) {
            errorCount = (Integer) ((Cache.ValueWrapper) errorCountWrapper).get();
        }
        // 获取当前用户的最后错误时间
        Object lastErrorTimeWrapper = CacheUtils.get("card_error_cache", ERROR_TIME_KEY_PREFIX + currentUserName);
        if (lastErrorTimeWrapper != null) {
            lastErrorTime = (Long) ((Cache.ValueWrapper) lastErrorTimeWrapper).get();
        }
        // 检查是否在禁用期内
        if (errorCount != null && errorCount >= MAX_ERROR_COUNT) {
            long currentTime = System.currentTimeMillis();
            if (lastErrorTime != null && currentTime - lastErrorTime < BLOCK_INTERVAL) {
                long remainingTime = (BLOCK_INTERVAL - (currentTime - lastErrorTime)) / 1000 / 60;
                throw new ServiceException("您的账号已被禁用，请" + remainingTime + "分钟后重试");
            } else {
                // 禁用期已过，清除错误记录
                CacheUtils.remove("card_error_cache", ERROR_COUNT_KEY_PREFIX + currentUserName);
                CacheUtils.remove("card_error_cache", ERROR_TIME_KEY_PREFIX + currentUserName);
                errorCount = null;
            }
        }
        // 根据卡号查询卡信息
        PlatformHashrate card = platformHashrateMapper.selectByCardNumber(cardNumber);
        if (card == null) {
            // 卡号错误，记录当前用户的错误信息
            if (errorCount == null) {
                errorCount = 0;
            }
            errorCount++;
            CacheUtils.put("card_error_cache", ERROR_COUNT_KEY_PREFIX + currentUserName, errorCount, ERROR_INTERVAL, TimeUnit.MILLISECONDS);
            CacheUtils.put("card_error_cache", ERROR_TIME_KEY_PREFIX + currentUserName, System.currentTimeMillis(), ERROR_INTERVAL, TimeUnit.MILLISECONDS);
            throw new ServiceException("卡号不存在，您还有" + (MAX_ERROR_COUNT - errorCount) + "次尝试机会");
        }
        // 检查卡号状态
        if ("1".equals(card.getHashrateStatus())) {
            throw new ServiceException("当前卡号已被使用，请尝试其他卡号");
        }
        // 获取卡号余额
        long cardBalance;
        try {
            cardBalance = Long.parseLong(card.getHashrateBalance());
        } catch (NumberFormatException e) {
            throw new ServiceException("卡号余额格式错误");
        }
        if (cardBalance <= 0) {
            throw new ServiceException("该卡号余额为0，无法充值");
        }
        // 查询当前用户的算力账户
        PlatformHashrate userHashrate = platformHashrateMapper.selectByUserName(currentUserName);
        if (userHashrate == null) {
            throw new ServiceException("您还没有算力账户，请联系管理员进行注册");
        }
        // 获取用户当前余额
        long userCurrentBalance;
        try {
            userCurrentBalance = Long.parseLong(userHashrate.getHashrateBalance());
        } catch (NumberFormatException e) {
            userCurrentBalance = 0;
        }
        // 计算充值后的余额
        long newUserBalance = userCurrentBalance + cardBalance;
        // 更新用户算力余额
        userHashrate.setHashrateBalance(String.valueOf(newUserBalance));
        userHashrate.setUpdateBy(currentUserName);
        userHashrate.setUpdateTime(DateUtils.getNowDate());
        int userUpdateResult = platformHashrateMapper.updatePlatformHashrate(userHashrate);
        if (userUpdateResult <= 0) {
            throw new ServiceException("更新用户算力余额失败");
        }
        // 标记卡号为已使用
        card.setHashrateStatus("1"); // 标记为已使用
        card.setUpdateBy(currentUserName);
        card.setUpdateTime(DateUtils.getNowDate());
        card.setRemark("卡号已被使用，使用者：" + currentUserName + "，充值算力点：" + cardBalance);
        int cardUpdateResult = platformHashrateMapper.updatePlatformHashrate(card);
        if (cardUpdateResult <= 0) {
            throw new ServiceException("更新卡号状态失败");
        }
        // 记录充值记录
        try {
            PlatformConsumption consumption = new PlatformConsumption();
            consumption.setUserName(currentUserName);
            consumption.setConsumptionTitle("使用卡号充值算力点，卡号：" + cardNumber);
            consumption.setConsumptionStaus("0"); // 0 表示充值
            consumption.setConsumptionHashrate(String.valueOf(cardBalance));
            consumption.setConsumptionResidue(String.valueOf(newUserBalance));
            consumption.setCreateBy(currentUserName);
            consumption.setCreateTime(DateUtils.getNowDate());
            consumption.setUpdateBy(currentUserName);
            consumption.setUpdateTime(DateUtils.getNowDate());
            platformConsumptionMapper.insertPlatformConsumption(consumption);
        } catch (Exception e) {
            // 记录失败不影响主要业务
        }
        // 充值成功，清除错误记录
        CacheUtils.remove("card_error_cache", ERROR_COUNT_KEY_PREFIX + currentUserName);
        CacheUtils.remove("card_error_cache", ERROR_TIME_KEY_PREFIX + currentUserName);
        // 返回用户更新后的算力信息
        return userHashrate;
    }
}
