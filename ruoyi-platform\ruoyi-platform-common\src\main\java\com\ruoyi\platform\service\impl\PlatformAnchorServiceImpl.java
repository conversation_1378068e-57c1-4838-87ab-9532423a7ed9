package com.ruoyi.platform.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.platform.domain.PlatformAnchor;
import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.domain.PlatformCategory;
import com.ruoyi.platform.mapper.PlatformAnchorMapper;
import com.ruoyi.platform.mapper.PlatformCategoryMapper;
import com.ruoyi.platform.mapper.PlatformProjectMapper;
import com.ruoyi.platform.service.IPlatformAnchorService;
import com.ruoyi.platform.service.IPlatformArticleService;
import com.ruoyi.platform.utils.TongYiQianWen;

import jakarta.transaction.Transactional;

/**
 * 智能主播Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
@Service
public class PlatformAnchorServiceImpl implements IPlatformAnchorService 
{
    @Autowired
    private PlatformAnchorMapper platformAnchorMapper;

    @Autowired
    private PlatformProjectMapper platformProjectMapper;

    @Autowired
    private IPlatformArticleService platformArticleService;

    @Autowired
    private PlatformCategoryMapper platformCategoryMapper;

    /**
     * 查询智能主播
     * 
     * @param anchorId 智能主播主键
     * @return 智能主播
     */
    @Override
    public PlatformAnchor selectPlatformAnchorByAnchorId(Long anchorId)
    {
        return platformAnchorMapper.selectPlatformAnchorByAnchorId(anchorId);
    }

    /**
     * 查询智能主播列表
     * 
     * @param platformAnchor 智能主播
     * @return 智能主播
     */
    @Override
    public List<PlatformAnchor> selectPlatformAnchorList(PlatformAnchor platformAnchor)
    {
        return platformAnchorMapper.selectPlatformAnchorList(platformAnchor);
    }

    /**
     * 新增智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    @Override
    public int insertPlatformAnchor(PlatformAnchor platformAnchor)
    {
        platformAnchor.setCreateTime(DateUtils.getNowDate());
        return platformAnchorMapper.insertPlatformAnchor(platformAnchor);
    }

    /**
     * 修改智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    @Override
    public int updatePlatformAnchor(PlatformAnchor platformAnchor) {
        platformAnchor.setUpdateTime(DateUtils.getNowDate());
        return platformAnchorMapper.updatePlatformAnchor(platformAnchor);
    }

    /**
     * 批量删除智能主播
     * 
     * @param anchorIds 需要删除的智能主播主键
     * @return 结果
     */
    @Override
    public int deletePlatformAnchorByAnchorIds(Long[] anchorIds)
    {
        return platformAnchorMapper.deletePlatformAnchorByAnchorIds(anchorIds);
    }

    /**
     * 删除智能主播信息
     * 
     * @param anchorId 智能主播主键
     * @return 结果
     */
    @Override
    public int deletePlatformAnchorByAnchorId(Long anchorId)
    {
        return platformAnchorMapper.deletePlatformAnchorByAnchorId(anchorId);
    }

    //训练智能主播
    @Override
    @Transactional
    public String trainAnchorWithProjectData(Long projectId) throws Exception {
        // 查询项目下的多个分类数据
        List<Long> categoryIds = platformProjectMapper.findCategoryIdsByProjectId(projectId);
        if (categoryIds.isEmpty()) {
            throw new ServiceException("当前项目下没有分类，请先创建分类！");
        }

        // 获取每个分类下的所有文章
        Map<Long, List<PlatformArticle>> articlesByCategory = platformArticleService.getArticlesByCategory(categoryIds);
        if (articlesByCategory.isEmpty()) {
            throw new ServiceException("当前分类下没有文案，请先创建文案！");
        }

        // 构建知识库JSON
        JSONObject knowledgeBase = new JSONObject();
        
        articlesByCategory.forEach((categoryId, articles) -> {
            PlatformCategory category = platformCategoryMapper.selectPlatformCategoryByCategoryId(categoryId);
            String categoryName = category.getCategoryTitle();
            String content = articles.stream()
                .map(PlatformArticle::getContent)
                .collect(Collectors.joining("\n"));
            
            knowledgeBase.put(categoryName, content);
        });

        // 生成知识库JSON并保存到智能主播表
        JSONObject finalKnowledgeBase = TongYiQianWen.productDesc(knowledgeBase.toJSONString());
        saveToAnchorRepository(finalKnowledgeBase, projectId);
        return "训练智能主播成功！";
    }

    //创建或者编辑
    private void saveToAnchorRepository(JSONObject knowledgeBase, Long projectId) {
        String userName=SecurityUtils.getUsername(); 
        Date date =DateUtils.getNowDate();
        // 获取智能主播ID
        Long anchorId = platformAnchorMapper.selectAnchorIdByProjectId(projectId);
        PlatformAnchor anchor;
        // 如果没有该智能主播数据，则创建一个新的智能主播
        if (anchorId == null) {
            anchor = new PlatformAnchor();
            anchor.setProjectId(projectId.intValue()); //项目ID
            anchor.setAnchorName("智能主播_" + projectId); //主播名称
            anchor.setCreateBy(userName); //创建人
            anchor.setCreateTime(date); //创建时间
            anchor.setUpdateBy(userName); //修改人
            anchor.setUpdateTime(date); //修改时间
            platformAnchorMapper.insertPlatformAnchor(anchor); //新增数据
            anchorId = anchor.getAnchorId().longValue(); // 获取新创建的智能主播ID
        } else {
            // 获取智能主播对象
            anchor = platformAnchorMapper.selectPlatformAnchorByAnchorId(anchorId);
        }
        // 如果存在当前智能主播信息 则更新智能主播的信息
        anchor.setAnchorName("智能主播_" + projectId); // 主播名称
        anchor.setAnchorRepository(knowledgeBase.toJSONString());
        anchor.setCreateBy(userName); //创建人
        anchor.setCreateTime(date); //创建时间
        anchor.setUpdateBy(userName); //修改人
        anchor.setUpdateTime(date); //修改时间
        // 更新智能主播
        platformAnchorMapper.updatePlatformAnchor(anchor);
    }

    //根据项目的ID查询数据
    @Override
    public PlatformAnchor selectPlatformAnchorByProjectId(Long projectId) {
        return platformAnchorMapper.selectPlatformAnchorByProjectId(projectId);
    }

    //获取智能主播知识库
    @Override
    public JSONObject getKnowledgeBase(Long projectId) {
        PlatformAnchor anchor = platformAnchorMapper.selectPlatformAnchorByProjectId(projectId);
        if (anchor == null) {
            return null; // 或者返回一个空的 JSON 对象
        }
        return JSONObject.parseObject(anchor.getAnchorRepository());
    }


    //智能主播回复
    @Override
    public String generateReply(JSONObject knowledgeBase, List<String> questions,List<String> historyReples) throws Exception {
        return TongYiQianWen.productDescQa(knowledgeBase, questions,historyReples);
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

     /**
     * 删除智能主播知识库中的特定字段
     * @param anchorId 主键
     * @param anchorRepository 要删除的字段类别
     * @return 结果
     */
    @Override
    public int deleteFieldFromAnchorRepository(Integer anchorId, String anchorRepository) {
        PlatformAnchor platformAnchor = platformAnchorMapper.selectPlatformAnchorByAnchorId(anchorId.longValue());
        if (platformAnchor == null) {
            throw new ServiceException("智能主播未找到！");
        }
        try {
            @SuppressWarnings("unchecked")
            Map<String, String> anchorRepositoryJson = objectMapper.readValue(platformAnchor.getAnchorRepository(), Map.class);
            anchorRepositoryJson.remove(anchorRepository);
            platformAnchor.setAnchorRepository(objectMapper.writeValueAsString(anchorRepositoryJson));
            platformAnchor.setUpdateTime(DateUtils.getNowDate());
            return updatePlatformAnchor(platformAnchor);
        } catch (Exception e) {
            throw new RuntimeException("删除智能主播信息失败！", e);
        }
    }

    /**
     * 根据直播ID获取智能主播信息
     *
     * @param liveId 直播ID
     * @return 智能主播信息列表
     */
    @Override
    public List<PlatformAnchor> getAnchorsByLiveId(Long liveId){
        return platformAnchorMapper.getAnchorsByLiveId(liveId);
    }
}
