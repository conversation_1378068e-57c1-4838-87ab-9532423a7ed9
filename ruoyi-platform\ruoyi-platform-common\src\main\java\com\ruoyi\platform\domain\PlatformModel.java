package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI模型对象 wy_model
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
@Schema(description = "模型对象")
public class PlatformModel extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 模型ID */
    @Schema(title = "模型ID")
    private Long modelId;

    /** 模型编码 */
    @Schema(title = "模型编码")
    @Excel(name = "模型编码")
    private String modelCode;

    /** 模型名称 */
    @Schema(title = "模型名称")
    @Excel(name = "模型名称")
    private String modelName;

    /** 状态：0-禁用，1-启用 */
    @Schema(title = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    private Integer modelStatus;

    /** 版本号 */
    @Schema(title = "模型价位")
    @Excel(name = "模型价位")
    private String modelVersion;
    public void setModelId(Long modelId) 
    {
        this.modelId = modelId;
    }

    public Long getModelId() 
    {
        return modelId;
    }


    public void setModelCode(String modelCode) 
    {
        this.modelCode = modelCode;
    }

    public String getModelCode() 
    {
        return modelCode;
    }


    public void setModelName(String modelName) 
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }

    public void setModelStatus(Integer modelStatus) 
    {
        this.modelStatus = modelStatus;
    }

    public Integer getModelStatus() 
    {
        return modelStatus;
    }


    public void setModelVersion(String modelVersion) 
    {
        this.modelVersion = modelVersion;
    }

    public String getModelVersion() 
    {
        return modelVersion;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("modelId", getModelId())
            .append("modelCode", getModelCode())
            .append("modelName", getModelName())
            .append("modelStatus", getModelStatus())
            .append("modelVersion", getModelVersion())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
