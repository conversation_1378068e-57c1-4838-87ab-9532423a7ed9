# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.5
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath,minio或者oss配置/ruoyi/uploadPath）
  profile: /ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # 指定默认文件服务类型(值为local代表使用磁盘作为文件操作服务,minio代表使用minio作为文件操作服务,oss代表使用oss作为文件操作服务)
  fileServer: oss
  # 指定默认文件上传方法最大文件大小(MB)
  fileMaxSize: 500

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8899
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 1000
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 500

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
  ip:
    maxRetryCount: 15
    lockTime: 15
# Spring配置
spring:
  cache:
    # 指定缓存类型 jcache 本地缓存 redis 缓存
    type: redis
    redis:
      # 指定存活时间（ms）
      time-to-live: 86400000
      # 指定前缀
      use-key-prefix: true
      # 是否缓存空值，可以防止缓存穿透
      cache-null-values: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid, auth, pay, file, coze ,ice, plugins, model, voice
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 500MB
      # 设置总上传的文件大小
      max-request-size: 1000MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）为1440 七天为10080
  expireTime: 10080  

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/script
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


createSqlSessionFactory:
  # 选择MyBatis配置方式，mybatis / mybatis-plus
  use: mybatis-plus

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

