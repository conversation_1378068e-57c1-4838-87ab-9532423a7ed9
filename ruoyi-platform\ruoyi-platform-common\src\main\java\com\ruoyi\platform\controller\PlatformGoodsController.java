package com.ruoyi.platform.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformGoods;
import com.ruoyi.platform.service.IPlatformGoodsService;
import com.ruoyi.platform.service.IPlatformLiveService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 产品管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@RestController
@RequestMapping("/platform/goods")
@Tag(name = "【产品管理】管理")
public class PlatformGoodsController extends BaseController
{
    @Autowired
    private IPlatformGoodsService platformGoodsService;

    @Autowired
    private IPlatformLiveService platformLiveService;

    /**
     * 查询产品管理列表
     */
    @Operation(summary = "查询产品管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:goods:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformGoods platformGoods)
    {
        List<PlatformGoods> list = platformGoodsService.selectPlatformGoodsList(platformGoods);
        return getDataTable(list);
    }

    /**
     * 导出产品管理列表
     */
    @Operation(summary = "导出产品管理列表")
    @PreAuthorize("@ss.hasPermi('platform:goods:export')")
    @Log(title = "导出产品管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformGoods platformGoods)
    {
        List<PlatformGoods> list = platformGoodsService.selectPlatformGoodsList(platformGoods);
        ExcelUtil<PlatformGoods> util = new ExcelUtil<PlatformGoods>(PlatformGoods.class);
        util.exportExcel(response, list, "产品管理数据");
    }

    /**
     * 获取产品管理详细信息
     */
    @Operation(summary = "获取产品管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:goods:query')")
    @GetMapping(value = "/{goodsId}")
    public AjaxResult getInfo(@PathVariable("goodsId") Long goodsId)
    {
        return success(platformGoodsService.selectPlatformGoodsByGoodsId(goodsId));
    }

    /**
     * 新增产品管理
     */
    @Operation(summary = "新增产品管理")
    //@PreAuthorize("@ss.hasPermi('platform:goods:add')")
    @Log(title = "新增产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformGoods platformGoods)
    {
        platformGoods.setCreateBy(getUsername());
        platformGoods.setUpdateBy(getUsername());
        return toAjax(platformGoodsService.insertPlatformGoods(platformGoods));
    }

    

    /**
     * 修改产品管理
     */
    @Operation(summary = "修改产品管理")
    //@PreAuthorize("@ss.hasPermi('platform:goods:edit')")
    @Log(title = "修改产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformGoods platformGoods)
    {
        platformGoods.setUpdateBy(getUsername());
        return toAjax(platformGoodsService.updatePlatformGoods(platformGoods));
    }

    /**
     * 删除产品管理
     */
    @Operation(summary = "删除产品管理")
    //@PreAuthorize("@ss.hasPermi('platform:goods:remove')")
    @Log(title = "删除产品管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{goodsIds}")
    public AjaxResult remove(@PathVariable( name = "goodsIds" ) Long[] goodsIds) 
    {
        for (Long goodsId : goodsIds) {
            if (platformLiveService.isGoodsUsed(goodsId)) {
                return AjaxResult.error("该产品已被某场直播使用，不能删除！");
            }
        }
        return toAjax(platformGoodsService.deletePlatformGoodsByGoodsIds(goodsIds));
    }

    //根据产品ids查询--产品名称
    @Operation(summary = "根据产品ids查询--产品名称")
    @GetMapping("/listForOption")
    public Map<Long, String> getGoodsIdAndName(@RequestParam(name="goodsId") List<Long> goodsId) {
        return platformGoodsService.getGoodsIdByNameIds(goodsId);
    }
}
