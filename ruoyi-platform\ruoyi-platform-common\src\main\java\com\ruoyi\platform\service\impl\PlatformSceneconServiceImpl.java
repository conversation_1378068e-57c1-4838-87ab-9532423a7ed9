package com.ruoyi.platform.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.domain.PlatformScenecon;
import com.ruoyi.platform.mapper.PlatformLiveMapper;
import com.ruoyi.platform.mapper.PlatformSceneconMapper;
import com.ruoyi.platform.service.IPlatformSceneconService;

/**
 * 场控管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@Service
public class PlatformSceneconServiceImpl implements IPlatformSceneconService 
{
    @Autowired
    private PlatformSceneconMapper platformSceneconMapper;

    @Autowired
    private PlatformLiveMapper platformLiveMapper;

    /**
     * 查询场控管理
     * 
     * @param sceneconId 场控管理主键
     * @return 场控管理
     */
    @Override
    public PlatformScenecon selectPlatformSceneconBySceneconId(Long sceneconId)
    {
        return platformSceneconMapper.selectPlatformSceneconBySceneconId(sceneconId);
    }

    /**
     * 查询场控管理列表
     * 
     * @param platformScenecon 场控管理
     * @return 场控管理
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformScenecon> selectPlatformSceneconList(PlatformScenecon platformScenecon)
    {
        return platformSceneconMapper.selectPlatformSceneconList(platformScenecon);
    }

    /**
     * 新增场控管理
     * 
     * @param platformScenecon 场控管理
     * @return 结果
     */
    @Override
    public int insertPlatformScenecon(PlatformScenecon platformScenecon)
    {
        platformScenecon.setCreateTime(DateUtils.getNowDate());
        platformScenecon.setUpdateTime(DateUtils.getNowDate());
        return platformSceneconMapper.insertPlatformScenecon(platformScenecon);
    }

    /**
     * 修改场控管理
     * 
     * @param platformScenecon 场控管理
     * @return 结果
     */
    @Override
    public int updatePlatformScenecon(PlatformScenecon platformScenecon)
    {
        platformScenecon.setUpdateTime(DateUtils.getNowDate());
        return platformSceneconMapper.updatePlatformScenecon(platformScenecon);
    }

    /**
     * 批量删除场控管理
     * 
     * @param sceneconIds 需要删除的场控管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformSceneconBySceneconIds(Long[] sceneconIds)
    {
        return platformSceneconMapper.deletePlatformSceneconBySceneconIds(sceneconIds);
    }

    /**
     * 删除场控管理信息
     * 
     * @param sceneconId 场控管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformSceneconBySceneconId(Long sceneconId)
    {
        return platformSceneconMapper.deletePlatformSceneconBySceneconId(sceneconId);
    }

    /**
     * 根据项目ID获取场控信息
     */
    @Override
    public List<PlatformScenecon> getSceneconByLiveId(Long liveId) {
        // 1. 获取直播信息
        PlatformLive live = platformLiveMapper.selectPlatformLiveByLiveId(liveId);
        if (live == null || live.getSceneconId() == null) {
            return new ArrayList<>();
        }

        // 2. 查询场控信息
        PlatformScenecon scenecon = platformSceneconMapper.selectPlatformSceneconBySceneconId(live.getSceneconId());
        if (scenecon == null) {
            return new ArrayList<>();
        }

        // 3. 返回场控信息列表
        return List.of(scenecon);
    }

}
