package com.ruoyi.platform.utils.taskUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

public class HttpClientUtil {
    
    public static String doPost(String url, String jsonBody, Map<String, String> headers) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(createPostRequest(url, jsonBody, headers))) {
            
            HttpEntity responseEntity = response.getEntity();
            return responseEntity != null ? EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : null;
        } catch (Exception e) {
            throw new RuntimeException("HTTP请求失败: " + e.getMessage());
        }
    }

    public static byte[] doGetBytes(String url, Map<String, String> headers) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(createGetRequest(url, headers))) {
            
            if (response.getStatusLine().getStatusCode() == 200) {
                return EntityUtils.toByteArray(response.getEntity());
            }
            throw new Exception("下载文件失败，状态码: " + response.getStatusLine().getStatusCode());
        }
    }

    private static HttpPost createPostRequest(String url, String jsonBody, Map<String, String> headers) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        
        if (headers != null) {
            headers.forEach(httpPost::setHeader);
        }
        
        if (jsonBody != null) {
            StringEntity entity = new StringEntity(jsonBody, StandardCharsets.UTF_8);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
        }
        
        return httpPost;
    }

    private static HttpGet createGetRequest(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        if (headers != null) {
            headers.forEach(httpGet::addHeader);
        }
        return httpGet;
    }
}
