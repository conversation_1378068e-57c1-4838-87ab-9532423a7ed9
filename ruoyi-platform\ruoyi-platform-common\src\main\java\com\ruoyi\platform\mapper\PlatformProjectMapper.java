package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformProject;

/**
 * 项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface PlatformProjectMapper {
    /**
     * 查询项目
     * 
     * @param projectId 项目主键
     * @return 项目
     */
    public PlatformProject selectPlatformProjectByProjectId(Long projectId);

    /**
     * 查询项目列表
     * 
     * @param platformProject 项目
     * @return 项目集合
     */
    public List<PlatformProject> selectPlatformProjectList(PlatformProject platformProject);

    /**
     * 新增项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    public int insertPlatformProject(PlatformProject platformProject);

    /**
     * 修改项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    public int updatePlatformProject(PlatformProject platformProject);

    /**
     * 删除项目
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    public int deletePlatformProjectByProjectId(Long projectId);

    /**
     * 检查是否还有分类
     * @param projectId 项目主键
     * @return 结果
     */
    @Select("select count(category_id) from platform_category where project_id = #{projectId}")
    public int checkProjectHasCategory(Long projectId);

    //根据项目Id查询当前项目下分类数据
    @Select("select category_id from platform_category where project_id = #{projectId}")
    public List<Long> findCategoryIdsByProjectId(Long projectId);

    /**
     * 根据直播ID查询项目信息
     *
     * @param liveId 直播ID
     * @return 项目信息
     */
    public PlatformProject selectProjectByLiveId(Long liveId);
}
