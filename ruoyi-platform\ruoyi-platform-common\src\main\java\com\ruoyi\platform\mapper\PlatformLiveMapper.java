package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformLive;

/**
 * 直播管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-08
 */
public interface PlatformLiveMapper 
{
    /**
     * 查询直播管理
     * 
     * @param liveId 直播管理主键
     * @return 直播管理
     */
    public PlatformLive selectPlatformLiveByLiveId(Long liveId);

    /**
     * 查询直播管理列表
     * 
     * @param platformLive 直播管理
     * @return 直播管理集合
     */
    public List<PlatformLive> selectPlatformLiveList(PlatformLive platformLive);

    /**
     * 新增直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    public int insertPlatformLive(PlatformLive platformLive);

    /**
     * 修改直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    public int updatePlatformLive(PlatformLive platformLive);

    /**
     * 删除直播管理
     * 
     * @param liveId 直播管理主键
     * @return 结果
     */
    public int deletePlatformLiveByLiveId(Long liveId);

    /**
     * 批量删除直播管理
     * 
     * @param liveIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformLiveByLiveIds(Long[] liveIds);

    /**
     * 强制删除关联的直播、场控和产品数据。
     *
     * @param projectId 项目ID
     * @return 影响的行数
     */
    public int deleteConstraintLiveProjectId(Long projectId);

    /**
     * 根据创建者查询直播
     * 
     * @param createBy 
     * @return 直播管理
     */
    @Select("select * from platform_live where create_by = #{createBy} order by create_time desc limit 5")
    public List<PlatformLive> selectPlatformLiveBycreateBy(String createBy);

    /**
     * 根据修改人查询直播
     * 
     * @param updateBy 
     * @return 直播管理
     */
    @Select("select * from platform_live where update_by = #{updateBy} order by update_time desc limit 5")
    public List<PlatformLive> selectPlatformLiveByupdateBy(String updateBy);

    /**
     * 检查给定的场控ID是否被任何直播使用.
     */
    @Select("SELECT COUNT(*) FROM platform_live WHERE scenecon_id = #{sceneconId}")
    public int countLiveBySceneconId(Long sceneconId);

    /**
     * 检查给定的产品ID是否被任何直播使用.
     */
    @Select("SELECT COUNT(*) FROM platform_live WHERE goods_id = #{goodsId}")
    public int countLiveByGoodsId(Long goodsId);

    //根据直播id查询当前直播信息
    public List<PlatformLive> selectLive(Long liveId);
}
