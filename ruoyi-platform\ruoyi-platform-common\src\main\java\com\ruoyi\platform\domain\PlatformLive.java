package com.ruoyi.platform.domain;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.GetMapping;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 直播管理对象 platform_live
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Schema(description = "直播管理对象")
public class PlatformLive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long liveId;

    /** 项目Id */
    @Schema(title = "项目Id")
    @Excel(name = "项目Id")
    @NotNull(message = "项目Id不能为空",groups = { GetMapping.class })
    private Long projectId;

    /** 直播类型 0实景无人播 1数字人直播 */
    @Schema(title = "直播类型 0实景无人播 1数字人直播")
    @Excel(name = "直播类型 0实景无人播 1数字人直播")
    private String liveType;

    /** 场控Id */
    @Schema(title = "场控Id")
    @Excel(name = "场控Id")
    private Long sceneconId;

    /** 产品Ids */
    @Schema(title = "产品Ids")
    @Excel(name = "产品Ids")
    private List<Long> goodsId;

    /** 直播名称 */
    @Schema(title = "直播名称")
    @Excel(name = "直播名称")
    @NotBlank(message = "直播名称不能为空")
    private String liveName;
    public void setLiveId(Long liveId) 
    {
        this.liveId = liveId;
    }

    public Long getLiveId() 
    {
        return liveId;
    }


    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }


    public void setLiveType(String liveType) 
    {
        this.liveType = liveType;
    }

    public String getLiveType() 
    {
        return liveType;
    }


    public void setSceneconId(Long sceneconId) 
    {
        this.sceneconId = sceneconId;
    }

    public Long getSceneconId() 
    {
        return sceneconId;
    }


    public void setGoodsId(List<Long> goodsId) 
    {
        this.goodsId = goodsId;
    }

    public List<Long> getGoodsId() 
    {
        return goodsId;
    }


    public void setLiveName(String liveName) 
    {
        this.liveName = liveName;
    }

    public String getLiveName() 
    {
        return liveName;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("liveId", getLiveId())
            .append("projectId", getProjectId())
            .append("liveType", getLiveType())
            .append("sceneconId", getSceneconId())
            .append("goodsId", getGoodsId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("liveName", getLiveName())
            .toString();
    }
}
