<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformHashrateMapper">
    
    <resultMap type="PlatformHashrate" id="PlatformHashrateResult">
        <result property="hashrateId"    column="hashrate_id"    />
        <result property="userName"    column="user_name"    />
        <result property="hashrateCard"    column="hashrate_card"    />
        <result property="hashrateStatus"    column="hashrate_status"    />
        <result property="hashrateBalance"    column="hashrate_balance"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    /> 
    </resultMap>

    <sql id="selectPlatformHashrateVo">
        select hashrate_id, user_name, hashrate_card, hashrate_status, hashrate_balance, create_by, create_time, update_by, update_time, remark from platform_hashrate
    </sql>

    <select id="selectPlatformHashrateList" parameterType="PlatformHashrate" resultMap="PlatformHashrateResult">
        <include refid="selectPlatformHashrateVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="createBy != null and createBy != ''"> and create_by = #{createBy}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectPlatformHashrateByHashrateId" parameterType="Long" resultMap="PlatformHashrateResult">
        <include refid="selectPlatformHashrateVo"/>
        where platform_hashrate.hashrate_id = #{hashrateId}
    </select>
    
    <!-- 添加按用户名查询算力账户 -->
    <select id="selectByUserName" parameterType="String" resultMap="PlatformHashrateResult">
        <include refid="selectPlatformHashrateVo"/>
        where user_name = #{userName} limit 1
    </select>
        
    <insert id="insertPlatformHashrate" parameterType="PlatformHashrate" useGeneratedKeys="true" keyProperty="hashrateId">
        insert into platform_hashrate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="hashrateCard != null">hashrate_card,</if>
            <if test="hashrateStatus != null">hashrate_status,</if>
            <if test="hashrateBalance != null">hashrate_balance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="hashrateCard != null">#{hashrateCard},</if>
            <if test="hashrateStatus != null">#{hashrateStatus},</if>
            <if test="hashrateBalance != null">#{hashrateBalance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformHashrate" parameterType="PlatformHashrate">
        update platform_hashrate
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="hashrateCard != null">hashrate_card = #{hashrateCard},</if>
            <if test="hashrateStatus != null">hashrate_status = #{hashrateStatus},</if>
            <if test="hashrateBalance != null">hashrate_balance = #{hashrateBalance},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where platform_hashrate.hashrate_id = #{hashrateId}
    </update>

    <delete id="deletePlatformHashrateByHashrateId" parameterType="Long">
        delete from platform_hashrate where hashrate_id = #{hashrateId}
    </delete>

    <delete id="deletePlatformHashrateByHashrateIds" parameterType="String">
        delete from platform_hashrate where hashrate_id in 
        <foreach item="hashrateId" collection="array" open="(" separator="," close=")">
            #{hashrateId}
        </foreach>
    </delete>

    <!-- 如果某用户不使用了进行强制删除算力用户和算力用户的消费、充值记录 --> 
    <delete id="deleteByUserId" parameterType="String">
        DELETE h, c FROM platform_hashrate h LEFT JOIN platform_consumption c 
        ON h.user_name = c.user_name WHERE h.user_name = #{userName}
    </delete>

    <!-- 强制删除之前检查是否有消费记录一并删除掉--> 
    <select id="getHashrateAndConsumptionTitleByUserId" parameterType="String">  
        SELECT h.*,c.consumption_title FROM platform_hashrate h LEFT JOIN 
        platform_consumption c ON h.user_name = c.user_name WHERE h.user_name = #{userName}
    </select>

    <!-- 批量删除算力用户 充值 消费记录 --> 
    <delete id="deleteByUserNames" parameterType="String">
        DELETE h, c FROM platform_hashrate h  LEFT JOIN platform_consumption c 
        ON h.user_name = c.user_name WHERE h.user_name IN
        <foreach item="userName" collection="array" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </delete>

    <!-- batchInsert --> 

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO platform_hashrate ( hashrate_card, hashrate_balance, hashrate_status, create_by, create_time, update_by, update_time, remark) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.hashrateCard},
            #{item.hashrateBalance},
            #{item.hashrateStatus},
            #{item.createBy}, NOW(), #{item.updateBy}, NOW(), #{item.remark}
            )
        </foreach>
    </insert>   
</mapper>