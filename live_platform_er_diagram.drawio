<mxfile host="65bd71144e">
    <diagram name="直播平台职位日报系统ER图" id="live-platform-position-er">
        <mxGraphModel dx="1626" dy="779" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="1500" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="user-entity" value="员工用户表 (employees)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="50" y="50" width="220" height="240" as="geometry"/>
                </mxCell>
                <mxCell id="user-id" value="employee_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="30" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-name" value="员工姓名" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="60" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-position" value="职位类型" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="90" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-dept" value="所属部门" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="120" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-level" value="职级" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="150" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-status" value="在职状态" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="180" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-created" value="入职时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="user-entity" vertex="1">
                    <mxGeometry y="210" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="position-entity" value="职位类型表 (positions)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="320" y="50" width="220" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="position-id" value="position_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="position-entity" vertex="1">
                    <mxGeometry y="30" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="position-name" value="职位名称" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="position-entity" vertex="1">
                    <mxGeometry y="60" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="position-category" value="职位分类" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="position-entity" vertex="1">
                    <mxGeometry y="90" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="position-desc" value="职位描述" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="position-entity" vertex="1">
                    <mxGeometry y="120" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="position-created" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="position-entity" vertex="1">
                    <mxGeometry y="150" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-operation-entity" value="内容运营日报表 (content_operation_daily)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="600" y="50" width="280" height="330" as="geometry"/>
                </mxCell>
                <mxCell id="content-id" value="report_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="content-operation-entity" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-employee-id" value="employee_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="content-operation-entity" vertex="1">
                    <mxGeometry y="90" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-script-count" value="脚本设计数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="120" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-short-video-count" value="短视频发布数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="150" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-live-script-count" value="直播脚本数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="180" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-other-work" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="210" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-work-duration" value="工作时长(小时)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="240" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-remarks" value="备注说明" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="270" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="content-created-time" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
                    <mxGeometry y="300" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-daily-entity" value="美工日报表 (design_daily)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
                    <mxGeometry x="920" y="50" width="280" height="360" as="geometry"/>
                </mxCell>
                <mxCell id="design-id" value="report_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="design-daily-entity" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-employee-id" value="employee_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="design-daily-entity" vertex="1">
                    <mxGeometry y="90" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-poster-count" value="海报宣传数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="120" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-background-count" value="口播背景数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="150" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-material-count" value="宣传资料数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="180" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-ppt-count" value="PPT制作数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="210" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-live-sticker-count" value="直播贴片数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="240" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-ui-count" value="UI设计数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="270" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-work-duration" value="工作时长(小时)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="300" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="design-created-time" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="design-daily-entity">
                    <mxGeometry y="330" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photography-daily-entity" value="摄影日报表 (photography_daily)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="1240" y="50" width="280" height="390" as="geometry"/>
                </mxCell>
                <mxCell id="photo-id" value="report_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="photography-daily-entity" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-employee-id" value="employee_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="photography-daily-entity" vertex="1">
                    <mxGeometry y="90" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-company-promo-count" value="公司宣传照数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="120" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-employee-daily-count" value="员工日常拍摄数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="150" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-equipment-setup" value="摄影器材建设" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="180" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-customer-photo-count" value="客户合影数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="210" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-project-management" value="项目管理工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="photography-daily-entity" vertex="1">
                    <mxGeometry y="240" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-digital-data-collection" value="数字人后台数据收集" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="270" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-other-work" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="300" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-work-duration" value="工作时长(小时)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="330" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="photo-created-time" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="photography-daily-entity">
                    <mxGeometry y="360" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copywriting-daily-entity" value="文案策划日报表 (copywriting_daily)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="310" y="440" width="320" height="540" as="geometry"/>
                </mxCell>
                <mxCell id="copy-id" value="report_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="30" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-employee-id" value="employee_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="copywriting-daily-entity">
                    <mxGeometry y="60" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="90" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-xiaohongshu-notes" value="小红书笔记数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="120" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-xiaohongshu-content" value="小红书内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="150" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-douyin-content" value="抖音内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="180" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-kuaishou-content" value="快手内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="210" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-wechat-content" value="微信公众号内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="240" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-weibo-content" value="微博内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="270" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-zhihu-content" value="知乎内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="300" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-bilibili-content" value="B站内容策划" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="330" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-total-articles" value="总文章数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="360" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-total-words" value="总字数(万字以上)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="copywriting-daily-entity" vertex="1">
                    <mxGeometry y="390" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-other-work" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="copywriting-daily-entity">
                    <mxGeometry y="420" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-work-duration" value="工作时长(小时)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="copywriting-daily-entity">
                    <mxGeometry y="450" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-remarks" value="备注说明" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="copywriting-daily-entity">
                    <mxGeometry y="480" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="copy-created-time" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="copywriting-daily-entity">
                    <mxGeometry y="510" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="employee-position-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-position" target="position-name">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="155" as="sourcePoint"/>
                        <mxPoint x="320" y="105" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="employee-content-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-dept" target="content-employee-id">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="185" as="sourcePoint"/>
                        <mxPoint x="600" y="125" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="employee-design-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-level" target="design-employee-id">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="215" as="sourcePoint"/>
                        <mxPoint x="920" y="125" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="employee-photo-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-status" target="photo-employee-id">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="245" as="sourcePoint"/>
                        <mxPoint x="1240" y="125" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="employee-copy-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-entity" target="copywriting-daily-entity">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="320" as="sourcePoint"/>
                        <mxPoint x="760" y="500" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="title" value="直播平台职位日报系统 ER图设计" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="700" y="10" width="400" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="legend" value="职位分类与日报表对应关系：&#xa;&#xa;🔵 员工用户表 - 核心用户信息管理&#xa;🔴 职位类型表 - 职位分类管理&#xa;🟡 内容运营日报 - 脚本设计、短视频、直播脚本&#xa;🟣 美工日报 - 海报、背景、PPT、UI设计&#xa;🟢 摄影日报 - 宣传照、器材建设、数据收集&#xa;🔴 文案策划日报 - 多平台内容策划与统计&#xa;&#xa;设计特点：&#xa;• 按职位分类设计日报表结构&#xa;• 统一的字段规范和外键关系&#xa;• 支持多平台内容管理&#xa;• 完整的工作量统计体系" style="text;html=1;strokeColor=#82b366;fillColor=#d5e8d4;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="1580" y="50" width="350" height="300" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>