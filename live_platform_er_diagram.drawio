<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-08-04T00:00:00.000Z" agent="5.0" etag="xxx" version="21.6.5" type="device">
  <diagram name="直播平台ER图" id="live-platform-er">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 用户管理模块 -->
        <mxCell id="user-entity" value="用户表 (users)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="200" height="180" as="geometry" />
        </mxCell>
        <mxCell id="user-id" value="user_id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="user-entity">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user-name" value="姓名" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="user-entity">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user-position" value="职位" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="user-entity">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user-dept" value="部门" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="user-entity">
          <mxGeometry y="120" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user-created" value="创建时间" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="user-entity">
          <mxGeometry y="150" width="200" height="30" as="geometry" />
        </mxCell>

        <!-- 数字人直播数据表 -->
        <mxCell id="digital-live-entity" value="数字人直播数据表 (digital_live_data)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="50" width="250" height="270" as="geometry" />
        </mxCell>
        <mxCell id="digital-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="60" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-platform" value="平台" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="90" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-account" value="账号" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="120" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-duration" value="直播时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="150" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-gmv" value="总销售额(元)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="180" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-orders" value="有效订单数" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="210" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="digital-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="digital-live-entity">
          <mxGeometry y="240" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- 交付专员日报表 -->
        <mxCell id="delivery-report-entity" value="交付专员日报表 (delivery_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="620" y="50" width="250" height="300" as="geometry" />
        </mxCell>
        <mxCell id="delivery-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="60" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-customer-count" value="接待客户数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="90" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-service-time" value="服务时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="120" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-maintenance" value="维护客户情况" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="150" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-platform-count" value="开播平台数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="180" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-digital-count" value="数字人服务调试数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="210" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-work-content" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="240" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="delivery-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="delivery-report-entity">
          <mxGeometry y="270" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- IP运营日报表 -->
        <mxCell id="ip-operation-entity" value="IP运营日报表 (ip_operation_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="920" y="50" width="250" height="300" as="geometry" />
        </mxCell>
        <mxCell id="ip-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="60" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-live-count" value="直播场次数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="90" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-platform" value="平台" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="120" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-account" value="账号名称" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="150" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-content-type" value="对标账号" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="180" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-work-content" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="210" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-duration" value="时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="240" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ip-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="ip-operation-entity">
          <mxGeometry y="270" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- 主播日报表 -->
        <mxCell id="anchor-report-entity" value="主播日报表 (anchor_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="250" height="330" as="geometry" />
        </mxCell>
        <mxCell id="anchor-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="60" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-live-duration" value="直播时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="90" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-platform" value="平台" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="120" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-gmv" value="总销售额(元)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="150" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-orders" value="有效订单数" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="180" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-fans" value="粉丝增长数" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="210" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-interaction" value="互动力" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="240" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-work-content" value="工作优先级" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="270" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="anchor-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="anchor-report-entity">
          <mxGeometry y="300" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- 机构运营日报表 -->
        <mxCell id="org-operation-entity" value="机构运营日报表 (org_operation_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="350" y="400" width="280" height="330" as="geometry" />
        </mxCell>
        <mxCell id="org-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="30" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="60" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-project" value="项目管理" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="90" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-mcn-data" value="MCN达人管理数据" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="120" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-mcn-business" value="MCN达人管理商业化实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="150" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-live-duration" value="直播时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="180" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-gmv" value="GMV" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="210" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-work-content" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="240" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-other-info" value="其他信息" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="270" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="org-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="org-operation-entity">
          <mxGeometry y="300" width="280" height="30" as="geometry" />
        </mxCell>

        <!-- 内容运营日报表 -->
        <mxCell id="content-operation-entity" value="内容运营日报表 (content_operation_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="680" y="400" width="280" height="270" as="geometry" />
        </mxCell>
        <mxCell id="content-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="30" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="60" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-video-count" value="脚本设计数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="90" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-short-video" value="短视频发布数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="120" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-live-script" value="直播脚本数量" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="150" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-work-content" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="180" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-duration" value="时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="210" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="content-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="content-operation-entity">
          <mxGeometry y="240" width="280" height="30" as="geometry" />
        </mxCell>

        <!-- 客服运营日报表 -->
        <mxCell id="customer-service-entity" value="客服运营日报表 (customer_service_report)" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#e6f7ff;strokeColor=#1890ff;" vertex="1" parent="1">
          <mxGeometry x="1010" y="400" width="280" height="300" as="geometry" />
        </mxCell>
        <mxCell id="cs-id" value="id (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="30" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-date" value="日期" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="60" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-customer-count" value="客服数据" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="90" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-consultation" value="引流客服咨询数据" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="120" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-daily-summary" value="日常客服数据" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="150" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-video-data" value="视频号数据" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="180" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-work-content" value="其他临时性工作" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="210" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-duration" value="时长" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="240" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cs-user-id" value="user_id (FK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=2;" vertex="1" parent="customer-service-entity">
          <mxGeometry y="270" width="280" height="30" as="geometry" />
        </mxCell>

        <!-- 关系连线 -->
        <mxCell id="user-digital-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-position" target="digital-user-id">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="155" as="sourcePoint" />
            <mxPoint x="320" y="305" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-delivery-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-dept" target="delivery-user-id">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="185" as="sourcePoint" />
            <mxPoint x="620" y="335" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-ip-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-created" target="ip-user-id">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="215" as="sourcePoint" />
            <mxPoint x="920" y="335" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-anchor-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user-entity" target="anchor-report-entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="250" as="sourcePoint" />
            <mxPoint x="175" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-org-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="digital-live-entity" target="org-operation-entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="445" y="350" as="sourcePoint" />
            <mxPoint x="490" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-content-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="delivery-report-entity" target="content-operation-entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="745" y="370" as="sourcePoint" />
            <mxPoint x="820" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="user-cs-relation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ip-operation-entity" target="customer-service-entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1045" y="370" as="sourcePoint" />
            <mxPoint x="1150" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 标题 -->
        <mxCell id="title" value="直播平台工作日报系统 ER图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="10" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例说明：&#xa;🔵 用户管理模块&#xa;🟡 数字人直播数据&#xa;🟣 交付专员日报&#xa;🔴 IP运营日报&#xa;🟢 主播日报&#xa;🟠 机构运营日报&#xa;⚪ 内容运营日报&#xa;🔵 客服运营日报" style="text;html=1;strokeColor=#82b366;fillColor=#d5e8d4;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1320" y="50" width="200" height="200" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
