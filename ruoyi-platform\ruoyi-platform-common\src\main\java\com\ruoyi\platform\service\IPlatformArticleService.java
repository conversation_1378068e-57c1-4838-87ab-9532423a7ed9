package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformArticle;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 文案Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface IPlatformArticleService 
{
    /**
     * 查询文案
     * 
     * @param articleId 文案主键
     * @return 文案
     */
    public PlatformArticle selectPlatformArticleByArticleId(Long articleId);

    /**
     * 查询文案列表
     * 
     * @param platformArticle 文案
     * @return 文案集合
     */
    public List<PlatformArticle> selectPlatformArticleList(PlatformArticle platformArticle);

    /**
     * 新增文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    public int insertPlatformArticle(PlatformArticle platformArticle);

    /**
     * 修改文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    public int updatePlatformArticle(PlatformArticle platformArticle);

    /**
     * 批量删除文案
     * 
     * @param articleIds 需要删除的文案主键集合
     * @return 结果
     */
    public int deletePlatformArticleByArticleIds(Long[] articleIds);

    /**
     * 删除文案信息
     * 
     * @param articleId 文案主键
     * @return 结果
     */
    public int deletePlatformArticleByArticleId(Long articleId);

    //获取每个分类下的所有文章
    public Map<Long, List<PlatformArticle>> getArticlesByCategory(List<Long> categories);
    
    //导出当前项目下所有文案数据 格式为 .txt
    public void exportTxt(Long projectId,HttpServletResponse response);

    //批量新增文案
    public int batchInsertPlatformArticle(List<PlatformArticle> articles);

    //根据直播id查询文案信息
    public List<PlatformArticle> getArticlesByLiveId(Long liveId);

    //根据文案Ids查询文案信息
    public Map<Long, String> getArticleByIds(List<Long> articleIds);
}
