package com.ruoyi.video.common;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 将 DTO 对象序列化为JSON,在反序列化成Map,过滤掉 null 值字段, 最后自动注入到API请求参数中
 * * 该工具类可以用于将数据传输对象（DTO）转换为 Map，以便在调用 API 时使用。
 * @date 2025-07-09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DtoToMapUtil {

    // 用于 Spring 注入
    @Autowired
    private ObjectMapper objectMapper;

    // 静态变量，由 Spring 注入后赋值
    private static ObjectMapper staticObjectMapper;

    // Spring 注入完成后，赋值给静态变量
    @PostConstruct
    public void init() {
        staticObjectMapper = objectMapper;
    }

    /**
     * 静态方法：将 DTO 转换为 Map，并过滤 null 值字段
     */
    public static Map<String, Object> convertDtoToMap(Object dto) {
        if (dto == null) {
            return Collections.emptyMap();
        }

        try {
            Map<String, Object> map = staticObjectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {});

            return map.entrySet().stream()
                    .filter(entry -> Objects.nonNull(entry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        } catch (Exception e) {
            log.error("DTO 转换为 Map 失败: {}", dto.getClass().getName(), e);
            return Collections.emptyMap();
        }
    }
}
