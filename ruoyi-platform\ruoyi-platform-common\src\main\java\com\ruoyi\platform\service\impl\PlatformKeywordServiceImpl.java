package com.ruoyi.platform.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.domain.PlatformKeyword;
import com.ruoyi.platform.mapper.PlatformKeywordMapper;
import com.ruoyi.platform.service.IPlatformKeywordService;

/**
 * 关键词管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
public class PlatformKeywordServiceImpl implements IPlatformKeywordService {
   
    @Autowired
    private PlatformKeywordMapper platformKeywordMapper;

    /**
     * 查询关键词管理
     * 
     * @param keywordId 关键词管理主键
     * @return 关键词管理
     */
    @Override
    public PlatformKeyword selectPlatformKeywordByKeywordId(Long keywordId) {
        return platformKeywordMapper.selectPlatformKeywordByKeywordId(keywordId);
    }

    //匹配关键词分类
    @Override
    public Long listByFormat(List<Long> categoryIds, List<String> strs) {
        List<PlatformKeyword> platformKeywords = platformKeywordMapper.selectPlatformKeywordByCategoryIds(categoryIds);
        Map<Long, List<PlatformKeyword>> map = platformKeywords.stream()
                .collect(Collectors.groupingBy(PlatformKeyword::getCategoryId));
        for (String str : strs) {
            for (Long key : map.keySet()) {
                platformKeywords = map.get(key);
                for (PlatformKeyword platformKeyword : platformKeywords) {
                    String keyWord = platformKeyword.getKeywordName();
                    if (str.contains(keyWord)) {
                        return key;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 查询关键词管理列表
     * 
     * @param platformKeyword 关键词管理
     * @return 关键词管理
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformKeyword> selectPlatformKeywordList(PlatformKeyword platformKeyword) {

        List<PlatformKeyword> list = platformKeywordMapper.selectPlatformKeywordList(platformKeyword);
        // 按创建时间降序排序，最新的在前
        list.sort(Comparator.comparing(PlatformKeyword::getCreateTime).reversed());
        return list;
    }

    /**
     * 新增关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    @Override
    public int insertPlatformKeyword(PlatformKeyword platformKeyword) {
        platformKeyword.setCreateTime(DateUtils.getNowDate());
        return platformKeywordMapper.insertPlatformKeyword(platformKeyword);
    }

    /**
     * 修改关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    @Override
    public int updatePlatformKeyword(PlatformKeyword platformKeyword) {
        platformKeyword.setUpdateTime(DateUtils.getNowDate());
        return platformKeywordMapper.updatePlatformKeyword(platformKeyword);
    }

    /**
     * 批量删除关键词管理
     * 
     * @param keywordIds 需要删除的关键词管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformKeywordByKeywordIds(Long[] keywordIds) {
        return platformKeywordMapper.deletePlatformKeywordByKeywordIds(keywordIds);
    }

    /**
     * 删除关键词管理信息
     * 
     * @param keywordId 关键词管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformKeywordByKeywordId(Long keywordId) {
        return platformKeywordMapper.deletePlatformKeywordByKeywordId(keywordId);
    }

    /**
     * 根据直播ID获取关键词列表
     *
     * @param liveId 直播ID
     * @return 关键词列表
     */
    @Override
    public List<PlatformKeyword> getKeywordsByLiveId(Long liveId){
        return platformKeywordMapper.getKeywordsByLiveId(liveId);
    }
}
