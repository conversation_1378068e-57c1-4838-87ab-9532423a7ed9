package com.ruoyi.platform.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformConsumption;
import com.ruoyi.platform.domain.vo.PlatformConsumptionVo;
import com.ruoyi.platform.service.IPlatformConsumptionService;
import com.ruoyi.system.service.ISysConfigService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用户算力点数变化记录Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@RestController
@RequestMapping("/platform/consumption")
@Tag(name = "【算力点记录】管理")
public class PlatformConsumptionController extends BaseController
{
    @Autowired
    private IPlatformConsumptionService platformConsumptionService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询用户算力点数变化记录列表
     */
    @Operation(summary = "查询用户算力点数变化记录列表")
    //@PreAuthorize("@ss.hasPermi('platform:consumption:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlatformConsumption platformConsumption)
    {
        startPage();
        getUsername();
        List<PlatformConsumption> list = platformConsumptionService.selectPlatformConsumptionList(platformConsumption);
        return getDataTable(list);
    }

    /**
     * 导出用户算力点数变化记录列表
     */
    @Operation(summary = "导出用户算力点数变化记录列表")
    @PreAuthorize("@ss.hasPermi('platform:consumption:export')")
    @Log(title = "导出算力点数变化记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformConsumption platformConsumption)
    {
        List<PlatformConsumption> list = platformConsumptionService.selectPlatformConsumptionList(platformConsumption);
        ExcelUtil<PlatformConsumption> util = new ExcelUtil<PlatformConsumption>(PlatformConsumption.class);
        util.exportExcel(response, list, "用户算力点数变化记录数据");
    }

    /**
     * 获取用户算力点数变化记录详细信息
     */
    @Operation(summary = "获取用户算力点数变化记录详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:consumption:query')")
    @GetMapping(value = "/{consumptionId}")
    public AjaxResult getInfo(@PathVariable("consumptionId") Long consumptionId)
    {
        return success(platformConsumptionService.selectPlatformConsumptionByConsumptionId(consumptionId));
    }

    /**
     * 新增用户算力点数变化记录
     */
    @Operation(summary = "新增用户算力点数变化记录")
    //@PreAuthorize("@ss.hasPermi('platform:consumption:add')")
    @Log(title = "新增用户算力点数变化记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlatformConsumption platformConsumption)
    {
        platformConsumption.setCreateBy(getUsername());
        platformConsumption.setUpdateBy(getUsername());
        return toAjax(platformConsumptionService.insertPlatformConsumption(platformConsumption));
    }

    /**
     * 修改用户算力点数变化记录
     */
    @Operation(summary = "修改用户算力点数变化记录")
    //@PreAuthorize("@ss.hasPermi('platform:consumption:edit')")
    @Log(title = "修改用户算力点数变化记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlatformConsumption platformConsumption)
    {
        platformConsumption.setUpdateBy(getUsername());
        return toAjax(platformConsumptionService.updatePlatformConsumption(platformConsumption));
    }

    /**
     * 删除用户算力点数变化记录
     */
    @Operation(summary = "删除用户算力点数变化记录")
    //@PreAuthorize("@ss.hasPermi('platform:consumption:remove')")
    @Log(title = "删除用户算力点数变化记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{consumptionIds}")
    public AjaxResult remove(@PathVariable( name = "consumptionIds" ) Long[] consumptionIds) 
    {
        return toAjax(platformConsumptionService.deletePlatformConsumptionByConsumptionIds(consumptionIds));
    }

    /**
     * 刷新算力点缓存
     */
    @Operation(summary = "刷新算力点缓存")
    //@PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "刷新算力点缓存", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        configService.resetConfigCache();
        return success();
    }

    /**
     * 查询用户算力点消费情况 进行统计
     */
    @Operation(summary = "查询用户算力点消费情况")
    @GetMapping("/userConsumption")
    public TableDataInfo userConsumption(PlatformConsumption platformConsumption){
        List<PlatformConsumption> list = platformConsumptionService.selectPlatformConsumptionList(platformConsumption);
        return getDataTable(list);
    }

    /**
     * 查询并汇总某个用户在过去一年内每个业务模块的算力消费情况。
     *
     * @param userName 用户名称
     * @return 用户在各业务模块上的算力消费汇总及占比
     */
    @Operation(summary = "获取用户算力点使用占比")
    @GetMapping("/getUserHashratePercent/{userName}")
    public List<PlatformConsumptionVo> getUserHashratePercent(@PathVariable("userName") String userName) {
        return platformConsumptionService.summarizeUserConsumption(userName);
    }

    /**
     * 获取算力消费统计数据
     */
    @Operation(summary = "获取算力消费统计数据")
    @GetMapping("/statistics")
    public AjaxResult statistics()
    {
        Map<String, Object> statistics = platformConsumptionService.getConsumptionStatistics();
        return success(statistics);
    }
}
