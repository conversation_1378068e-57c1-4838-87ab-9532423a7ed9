<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformGoodsMapper">

    <resultMap type="PlatformGoods" id="PlatformGoodsResult">
        <result property="goodsId" column="goods_id" />
        <result property="projectId" column="project_id" />
        <result property="goodsName" column="goods_name" />
        <result property="goodsInteractionId" column="goods_interaction_id" typeHandler="com.ruoyi.platform.utils.ListTypeHandler" />
        <result property="goodsQuestionsId" column="goods_questions_id" typeHandler="com.ruoyi.platform.utils.ListTypeHandler" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectPlatformGoodsVo">
        select goods_id, g.project_id, goods_name, goods_interaction_id, goods_questions_id, g.create_by, g.create_time, g.update_by, g.update_time, g.remark from platform_goods g left join sys_user u on u.user_name = g.create_by
        left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformGoodsList" parameterType="PlatformGoods" resultMap="PlatformGoodsResult">
        <include refid="selectPlatformGoodsVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="goodsName != null  and goodsName != ''"> and goods_name like concat('%', #{goodsName}, '%')</if>
            ${params.dataScope}
            <if test="goodsInteractionId != null  and goodsInteractionId != ''"> and goods_interaction_id = #{goodsInteractionId}</if>
            <if test="goodsQuestionsId != null  and goodsQuestionsId != ''"> and goods_questions_id = #{goodsQuestionsId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPlatformGoodsByGoodsId" parameterType="Long" resultMap="PlatformGoodsResult">
        <include refid="selectPlatformGoodsVo"/>
        where goods_id = #{goodsId}
    </select>

    <select id="selectPlatformGoodsByGoodsIds" parameterType="Long" resultMap="PlatformGoodsResult">
        <include refid="selectPlatformGoodsVo"/>
        <where>
            AND goods_id IN
            <foreach item="item" index="index" collection="goodsIds" open="(" close=")" separator=",">  
              #{item}   
            </foreach>
        </where>
    </select>

    <insert id="insertPlatformGoods" parameterType="PlatformGoods" useGeneratedKeys="true" keyProperty="goodsId">
        INSERT INTO platform_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="goodsInteractionId != null">goods_interaction_id,</if>
            <if test="goodsQuestionsId != null">goods_questions_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="goodsName != null">#{goodsName},</if>
            <if test="goodsInteractionId != null">#{goodsInteractionId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="goodsQuestionsId != null">#{goodsQuestionsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlatformGoods" parameterType="PlatformGoods">
        update platform_goods g
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="goodsName != null">goods_name = #{goodsName},</if>
            <if test="goodsInteractionId != null">goods_interaction_id = #{goodsInteractionId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="goodsQuestionsId != null">goods_questions_id = #{goodsQuestionsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where g.goods_id = #{goodsId}
    </update>

    <delete id="deletePlatformGoodsByGoodsId" parameterType="Long">
        delete from platform_goods where goods_id = #{goodsId}
    </delete>

    <delete id="deletePlatformGoodsByGoodsIds" parameterType="String">
        delete from platform_goods where goods_id in 
        <foreach item="goodsId" collection="array" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </delete>

    <!-- 根据传入的数组数据查找一一对应的商品数据。-->
    <select id="getGoodsByIds" parameterType="java.util.List" resultMap="PlatformGoodsResult">
        SELECT * FROM platform_goods WHERE goods_id IN
        <foreach item='id' collection='ids' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </select>

    <!-- 根据产品IDs批量查询产品信息 -->
    <select id="selectPlatformGoodsByIds" resultMap="PlatformGoodsResult">
        <include refid="selectPlatformGoodsVo"/>
        where goods_id in
        <foreach collection="list" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </select>
</mapper>