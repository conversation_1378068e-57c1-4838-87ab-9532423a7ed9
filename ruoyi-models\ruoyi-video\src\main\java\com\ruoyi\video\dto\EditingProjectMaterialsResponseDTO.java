package com.ruoyi.video.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 获取剪辑工程关联素材响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class EditingProjectMaterialsResponseDTO {

    /**
     * 请求ID
     */
    @JsonProperty("RequestId")
    private String requestId;

    /**
     * 项目ID号
     */
    @JsonProperty("ProjectId")
    private String projectId;

    /**
     * 符合要求的媒资集合
     */
    @JsonProperty("MediaInfos")
    private List<MediaInfo> mediaInfos;

    /**
     * 直播流关联素材
     */
    @JsonProperty("LiveMaterials")
    private List<LiveStreamConfig> liveMaterials;

    /**
     * 剪辑工程关联素材
     */
    @JsonProperty("ProjectMaterials")
    private List<String> projectMaterials;

    /**
     * 媒资信息
     */
    @Data
    public static class MediaInfo {
        /**
         * 媒资ID
         */
        @JsonProperty("MediaId")
        private String mediaId;

        /**
         * 媒资基础信息
         */
        @JsonProperty("MediaBasicInfo")
        private MediaBasicInfo mediaBasicInfo;

        /**
         * 文件信息
         */
        @JsonProperty("FileInfoList")
        private List<FileInfo> fileInfoList;
    }

    /**
     * 媒资基础信息
     */
    @Data
    public static class MediaBasicInfo {
        /**
         * 媒资ID
         */
        @JsonProperty("MediaId")
        private String mediaId;

        /**
         * 待注册的媒资在相应系统中的地址
         */
        @JsonProperty("InputURL")
        private String inputURL;

        /**
         * 媒资媒体类型
         */
        @JsonProperty("MediaType")
        private String mediaType;

        /**
         * 媒资业务类型
         */
        @JsonProperty("BusinessType")
        private String businessType;

        /**
         * 来源
         */
        @JsonProperty("Source")
        private String source;

        /**
         * 标题
         */
        @JsonProperty("Title")
        private String title;

        /**
         * 内容描述
         */
        @JsonProperty("Description")
        private String description;

        /**
         * 分类
         */
        @JsonProperty("Category")
        private String category;

        /**
         * 标签
         */
        @JsonProperty("MediaTags")
        private String mediaTags;

        /**
         * 封面地址
         */
        @JsonProperty("CoverURL")
        private String coverURL;

        /**
         * 用户数据
         */
        @JsonProperty("UserData")
        private String userData;

        /**
         * 截图
         */
        @JsonProperty("Snapshots")
        private String snapshots;

        /**
         * 资源状态
         */
        @JsonProperty("Status")
        private String status;

        /**
         * 转码状态
         */
        @JsonProperty("TranscodeStatus")
        private String transcodeStatus;

        /**
         * 媒资创建时间
         */
        @JsonProperty("CreateTime")
        private String createTime;

        /**
         * 媒资修改时间
         */
        @JsonProperty("ModifiedTime")
        private String modifiedTime;

        /**
         * 媒资删除时间
         */
        @JsonProperty("DeletedTime")
        private String deletedTime;

        /**
         * 雪碧图
         */
        @JsonProperty("SpriteImages")
        private String spriteImages;
    }

    /**
     * 文件信息
     */
    @Data
    public static class FileInfo {
        /**
         * 文件基础信息
         */
        @JsonProperty("FileBasicInfo")
        private FileBasicInfo fileBasicInfo;
    }

    /**
     * 文件基础信息
     */
    @Data
    public static class FileBasicInfo {
        /**
         * 文件名
         */
        @JsonProperty("FileName")
        private String fileName;

        /**
         * 文件状态
         */
        @JsonProperty("FileStatus")
        private String fileStatus;

        /**
         * 文件类型
         */
        @JsonProperty("FileType")
        private String fileType;

        /**
         * 文件大小（字节）
         */
        @JsonProperty("FileSize")
        private Long fileSize;

        /**
         * 文件OSS地址
         */
        @JsonProperty("FileUrl")
        private String fileUrl;

        /**
         * 文件存储区域
         */
        @JsonProperty("Region")
        private String region;

        /**
         * 封装格式
         */
        @JsonProperty("FormatName")
        private String formatName;

        /**
         * 时长
         */
        @JsonProperty("Duration")
        private Double duration;

        /**
         * 码率
         */
        @JsonProperty("Bitrate")
        private Double bitrate;

        /**
         * 宽
         */
        @JsonProperty("Width")
        private Integer width;

        /**
         * 高
         */
        @JsonProperty("Height")
        private Integer height;
    }

    /**
     * 直播流配置
     */
    @Data
    public static class LiveStreamConfig {
        /**
         * 直播播流应用名
         */
        @JsonProperty("AppName")
        private String appName;

        /**
         * 直播播流流名
         */
        @JsonProperty("StreamName")
        private String streamName;

        /**
         * 直播播流域名
         */
        @JsonProperty("DomainName")
        private String domainName;

        /**
         * 直播播流地址
         */
        @JsonProperty("LiveUrl")
        private String liveUrl;
    }
}
