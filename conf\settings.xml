<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <!-- mirrors节点配置了Maven仓库镜像 -->
    <mirrors>
        <mirror>
            <!-- 阿里云公共仓库镜像配置 -->
            <id>mirror</id>
            <!-- mirrorOf指定该镜像替代的仓库，!ruoyi-geek-springbooo3表示除了自定义仓库外都使用该镜像 -->
            <mirrorOf>central,jcenter,!ruoyi-geek-springbooo3</mirrorOf>
            <name>mirror</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
        </mirror>
    </mirrors>
    <!-- servers节点配置访问私服仓库的用户认证信息 -->
    <servers>
        <server>
            <!-- 阿里云云效制品仓库的认证信息 -->
            <id>ruoyi-geek-springbooo3</id>
            <!-- 阿里云制品仓库的访问用户名 -->
            <username>66ea36c041876371224defcf</username>
            <!-- 阿里云制品仓库的访问密码 -->
            <password>v))ciQsnYAZg</password>
        </server>
    </servers>
    <profiles>
        <profile>
            <!-- 阿里云云效RDC(Rapid Development Cloud)配置文件 -->
            <id>rdc</id>
            <properties>
                <!-- 发布稳定版本构件的仓库配置 -->
                <altReleaseDeploymentRepository>
                    <!-- 格式为: id::layout::url，这里使用阿里云云效的制品仓库 -->
                    ruoyi-geek-springbooo3::default::https://packages.aliyun.com/66cd2c0809c623f9a5e4d5db/maven/ruoyi-geek-springbooo3
                </altReleaseDeploymentRepository>
                
                <!-- 发布快照版本构件的仓库配置 -->
                <!-- 快照版本通常以-SNAPSHOT结尾，用于开发阶段频繁更新的版本 -->
                <altSnapshotDeploymentRepository>
                    <!-- 格式同上，这里快照版本也部署到同一个阿里云云效制品仓库 -->
                    ruoyi-geek-springbooo3::default::https://packages.aliyun.com/66cd2c0809c623f9a5e4d5db/maven/ruoyi-geek-springbooo3
                </altSnapshotDeploymentRepository>
                
            </properties>
            <!-- repositories节点配置从哪些仓库下载依赖 -->
            <repositories>
                <repository>
                    <!-- 中央仓库配置(使用阿里云镜像) -->
                    <id>central</id>
                    <url>https://maven.aliyun.com/nexus/content/groups/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <!-- 快照仓库配置(使用阿里云镜像) -->
                    <id>snapshots</id>
                    <url>https://maven.aliyun.com/nexus/content/groups/public</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <!-- 阿里云云效制品仓库配置 -->
                    <id>ruoyi-geek-springbooo3</id>
                    <url>https://packages.aliyun.com/66cd2c0809c623f9a5e4d5db/maven/ruoyi-geek-springbooo3</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <!-- pluginRepositories节点配置从哪些仓库下载插件 -->
            <pluginRepositories>
                <pluginRepository>
                    <!-- 中央插件仓库配置(使用阿里云镜像) -->
                    <id>central</id>
                    <url>https://maven.aliyun.com/nexus/content/groups/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
                <pluginRepository>
                    <!-- 快照插件仓库配置(使用阿里云镜像) -->
                    <id>snapshots</id>
                    <url>https://maven.aliyun.com/nexus/content/groups/public</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
                <pluginRepository>
                    <!-- 阿里云云效制品插件仓库配置 -->
                    <id>ruoyi-geek-springbooo3</id>
                    <url>https://packages.aliyun.com/66cd2c0809c623f9a5e4d5db/maven/ruoyi-geek-springbooo3</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <!-- activeProfiles节点配置激活的profile -->
    <activeProfiles>
        <activeProfile>rdc</activeProfile>
    </activeProfiles>
</settings>
