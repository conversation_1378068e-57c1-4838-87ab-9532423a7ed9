package com.ruoyi.platform.utils.taskUtils;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 第三方视频合成服务配置
 */

@Component
@Data
@ConfigurationProperties(prefix = "video.synthesis")
public class PlatformVideoConfig {
    /** 服务器URL */
    private String serverUrl;
    
    /** 合成接口路径 */
    private String synthesisPath;
    
    /** 查询接口路径 */
    private String queryPath;
    
    /** API密钥 */
    private String apiKey;
}
