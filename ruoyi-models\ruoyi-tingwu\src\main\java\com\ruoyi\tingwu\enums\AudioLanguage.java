package com.ruoyi.tingwu.enums;

/**
 * 音视频语言枚举
 */
public enum AudioLanguage {
    CHINESE("0", "cn"), //中文
    ENGLISH("1", "en"), //英语
    JAPANESE("2", "ja"), //日语
    CANTONESE("3", "yue"), //粤语
    CHINESE_ENGLISH_FREE("4", "multilingual"), //中英双语
    AUTO("5","auto"), //自动识别语言
    MULTILINGUAL("6","multilingual"); //多语言
    

    private final String code;
    private final String description;

    AudioLanguage(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static AudioLanguage fromCode(String code) {
        for (AudioLanguage language : AudioLanguage.values()) {
            if (language.getCode().equals(code)) {
                return language;
            }
        }
        return null;
    }
}