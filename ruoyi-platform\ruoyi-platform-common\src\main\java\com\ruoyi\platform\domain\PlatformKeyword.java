package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.GetMapping;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 关键词管理对象 platform_keyword
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Schema(description = "关键词管理对象")
public class PlatformKeyword extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long keywordId;

    /** 分类ID */
    @Schema(title = "分类ID")
    @Excel(name = "分类ID")
    @NotNull(message = "分类ID不能为空", groups = { GetMapping.class })
    private Long categoryId;

    /** 关键词内容 */
    @Schema(title = "关键词内容")
    @Excel(name = "关键词内容")
    @NotBlank(message = "关键词名称不能为空")
    private String keywordName;

    public void setKeywordId(Long keywordId) {
        this.keywordId = keywordId;
    }

    public Long getKeywordId() {
        return keywordId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getKeywordName() {
        return keywordName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("keywordId", getKeywordId())
                .append("categoryId", getCategoryId())
                .append("keywordName", getKeywordName())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
