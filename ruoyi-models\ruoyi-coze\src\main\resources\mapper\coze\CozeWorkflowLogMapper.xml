<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.coze.mapper.CozeWorkflowLogMapper">

    <resultMap type="CozeWorkflowLog" id="CozeWorkflowLogResult">
        <result property="id" column="id" />
        <result property="workflowId" column="workflow_id" />
        <result property="name" column="name" />
        <result property="inputParam" column="input_param" />
        <result property="outputParam" column="output_param" />
        <result property="executeId" column="execute_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectCozeWorkflowLogVo">
        select 
            cwl.id,
            cwl.workflow_id,
            cwl.execute_id,
            cwl.name,
            cwl.input_param,
            cwl.output_param,
            cwl.create_by,
            cwl.create_time,
            cwl.update_by,
            cwl.update_time,
            cwl.remark
        from coze_workflow_log cwl
    </sql>

    <select id="selectCozeWorkflowLogList" parameterType="CozeWorkflowLog" resultMap="CozeWorkflowLogResult">
        <include refid="selectCozeWorkflowLogVo"/>
        <where>
            <if test="workflowId != null  and workflowId != ''"> and cwl.workflow_id = #{workflowId}</if>
            <if test="executeId != null  and executeId != ''"> and cwl.execute_id = #{executeId}</if>
            <if test="name != null  and name != ''"> and cwl.name like concat('%', #{name}, '%')</if>
            <if test="inputParam != null  and inputParam != ''"> and cwl.input_param = #{inputParam}</if>
            <if test="outputParam != null  and outputParam != ''"> and cwl.output_param = #{outputParam}</if>
        </where>
    </select>

    <select id="selectCozeWorkflowLogById" parameterType="Long" resultMap="CozeWorkflowLogResult">
        <include refid="selectCozeWorkflowLogVo"/>
        where cwl.id = #{id}
    </select>

    <insert id="insertCozeWorkflowLog" parameterType="CozeWorkflowLog" useGeneratedKeys="true" keyProperty="id">
        insert into coze_workflow_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workflowId != null">workflow_id,</if>
            <if test="executeId != null">execute_id,</if>
            <if test="name != null">name,</if>
            <if test="inputParam != null">input_param,</if>
            <if test="outputParam != null">output_param,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workflowId != null">#{workflowId},</if>
            <if test="executeId != null">#{executeId},</if>
            <if test="name != null">#{name},</if>
            <if test="inputParam != null">#{inputParam},</if>
            <if test="outputParam != null">#{outputParam},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCozeWorkflowLog" parameterType="CozeWorkflowLog">
        update coze_workflow_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="workflowId != null">workflow_id = #{workflowId},</if>
            <if test="executeId != null">execute_id = #{executeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="inputParam != null">input_param = #{inputParam},</if>
            <if test="outputParam != null">output_param = #{outputParam},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where coze_workflow_log.id = #{id}
    </update>

    <delete id="deleteCozeWorkflowLogById" parameterType="Long">
        delete from coze_workflow_log where id = #{id}
    </delete>

    <delete id="deleteCozeWorkflowLogByIds" parameterType="String">
        delete from coze_workflow_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- updateCozeWorkflowLogoutputParam -->
    <update id="updateCozeWorkflowLogOutput">
    UPDATE coze_workflow_log
    SET output_param = #{resultJson},
        update_by = #{updateBy},
        update_time = #{updateTime}
    WHERE workflow_id = #{workflowId}
      AND execute_id = #{executeId}
    </update>

    <!-- updateCozeWorkflowLogOutputById -->

    <insert id="updateCozeWorkflowLogOutputById">
    UPDATE coze_workflow_log
    SET output_param = #{resultJson},
        update_by = #{updateBy},
        update_time = #{updateTime}
    WHERE id = #{id}
    </insert>

</mapper>