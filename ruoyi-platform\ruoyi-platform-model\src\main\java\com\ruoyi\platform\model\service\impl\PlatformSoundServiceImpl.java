package com.ruoyi.platform.model.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.model.domain.PlatformMachineCode;
import com.ruoyi.platform.model.domain.PlatformSound;
import com.ruoyi.platform.model.mapper.PlatformMachineCodeMapper;
import com.ruoyi.platform.model.mapper.PlatformSoundMapper;
import com.ruoyi.platform.model.service.IPlatformSoundService;
import com.ruoyi.platform.model.service.IPlatformTaskService;
import com.ruoyi.platform.model.utils.ModelException;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 声音管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class PlatformSoundServiceImpl implements IPlatformSoundService {

    @Autowired
    private PlatformSoundMapper platformSoundMapper;

    @Autowired
    private PlatformMachineCodeMapper platformMachineCodeMapper;

    @Autowired
    @Lazy
    private IPlatformTaskService platformTaskService;

    // 获取登录人
    private String getUsername() {
        return SecurityUtils.getUsername();
    }

    /**
     * 查询声音管理
     * 
     * @param soundId 声音管理主键
     * @return 声音管理
     */
    @Override
    public PlatformSound selectPlatformSoundBySoundId(Long soundId) {
        return platformSoundMapper.selectPlatformSoundBySoundId(soundId);
    }

    /**
     * 查询声音管理列表
     * 
     * @param platformSound 声音管理
     * @return 声音管理
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformSound> selectPlatformSoundList(PlatformSound platformSound) {
        platformSound.getParams().put("dataScopeEnabled", true);
        platformSound.getParams().put("deptId", SecurityUtils.getDeptId());
        platformSound.getParams().put("currentUserName", SecurityUtils.getUsername());
        return platformSoundMapper.selectPlatformSoundList(platformSound);
    }

    /**
     * 新增声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    @Override
    public int insertPlatformSound(PlatformSound platformSound) {
        platformSound.setCreateTime(DateUtils.getNowDate());
        return platformSoundMapper.insertPlatformSound(platformSound);
    }

    /**
     * 修改声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    @Override
    public int updatePlatformSound(PlatformSound platformSound) {
        //声音资源列表合成回调函数会调用该方法，此时 username 不存在
        try {
            String username = getUsername();
            if (StringUtils.isNotEmpty(username)) {
                platformSound.setUpdateBy(username);
            }
        } catch (Exception e) {
            System.out.println("警告：无法获取用户信息，updateBy字段将不被设置");
        }
        platformSound.setUpdateTime(DateUtils.getNowDate());
        return platformSoundMapper.updatePlatformSound(platformSound);
    }

    /**
     * 批量删除声音管理
     * 
     * @param soundIds 需要删除的声音管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformSoundBySoundIds(Long[] soundIds) {
        int deletedCount = 0; // 记录成功删除的数量
        List<String> failedLogs = new ArrayList<>(); // 用于记录删除失败的日志
        for (Long soundId : soundIds) {
            if (soundId == null) {
                failedLogs.add("声音Id为空，无法删除");
                continue; // 如果声音Id为空，跳过
            }
            // 根据 soundId 查询声音信息
            PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
            if (sound == null) {
                failedLogs.add("声音不存在！Id：" + soundId);
                continue; // 如果声音不存在，跳过
            }
            // 获取文件路径 训练 参考 gpt sovits地址分别删除掉
            List<String> fileUrls = Arrays.asList(
                    sound.getSoundTrain(),
                    sound.getSoundRef(),
                    sound.getSoundGpt(),
                    sound.getSoundSovits());
            // 删除数据库记录
            deletedCount += platformSoundMapper.deletePlatformSoundBySoundIds(new Long[] { soundId });
            // 删除 Oss 中的文件
            for (String fileUrl : fileUrls) {
                if (fileUrl != null && !fileUrl.isEmpty()) {
                    try {
                        boolean deleteSuccess = FileOperateUtils.deleteFile(fileUrl);
                        if (!deleteSuccess) {
                            throw new RuntimeException("Oss文件删除失败，路径：" + fileUrl);
                        }
                    } catch (Exception e) {
                        failedLogs.add("删除声音文件时发生错误: " + e.getMessage() + "，声音ID: " + soundId);
                    }
                }
            }
        }
        // 打印或记录所有删除失败的日志
        if (!failedLogs.isEmpty()) {
            failedLogs.forEach(System.err::println); // 使用System.err或logger记录失败信息
        }
        System.out.println("总共删除成功的音频记录数: " + deletedCount);
        return deletedCount; // 返回成功删除的总记录数
    }

    /**
     * 删除声音管理信息
     * 
     * @param soundId 声音管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformSoundBySoundId(Long soundId) {
        return platformSoundMapper.deletePlatformSoundBySoundId(soundId);
    }

    // 根据任务的状态去决定是成功还是失败 2：成功 3：失败
    @Override
    public int updateSoundStaus(Long soundId, Long soundStatus) {
        return platformSoundMapper.updateSoundStaus(soundId, soundStatus);
    }

    // 根据声音id 去修改模型的文件地址
    @Override
    public int updateModelSoundService(PlatformSound platformSound) {
        return platformSoundMapper.updateModelSoundMapper(platformSound);
    }

    // 添加待训练声音文件
    @Override
    public Object uploadSoundtrainAndRef(String soundName, MultipartFile trainAudio, MultipartFile refAudio,
            String refText, Long deptId, String soundFiltration) {
        try {
            // 检查训练音频文件是否为空
            if (trainAudio == null || trainAudio.isEmpty()) {
                throw new ModelException("请上传训练音频文件");
            }
            // 检查参考音频文件是否为空
            if (refAudio == null || refAudio.isEmpty()) {
                throw new ModelException("请上传参考音频文件");
            }
            // 限制文件大小
            long maxTrainFileSize = 60 * 1024 * 1024;
            long maxRefFileSize = 10 * 1024 * 1024;
            // 检查文件大小（大于规定大小则拒绝上传）
            if (trainAudio.getSize() > maxTrainFileSize) {
                throw new ModelException("训练文件不能超过60MB");
            }
            if (refAudio.getSize() > maxRefFileSize) {
                throw new ModelException("参考文件不能超过10MB");
            }
            // 设置文件路径
            String filePath = "ModelCheckpoint/sound";
            // 处理训练音频文件
            String trainFilePath = handleAudioFile(trainAudio, filePath);
            // 处理参考音频文件
            String refFilePath = handleAudioFile(refAudio, filePath);
            // 创建 PlatformSound 实例并设置属性
            Date nowDate = DateUtils.getNowDate(); // 获取创建时间
            PlatformSound sound = new PlatformSound();
            sound.setSoundName(soundName); // 声音名称
            sound.setSoundStatus("4"); // 状态 待提交
            sound.setCreateBy(getUsername()); // 创建人
            sound.setCreateTime(nowDate); // 创建时间
            sound.setUpdateBy(getUsername()); // 修改人
            sound.setUpdateTime(nowDate); // 修改时间
            sound.setSoundTrain(trainFilePath); // 设置训练文件路径
            sound.setSoundRef(refFilePath); // 设置参考文件路径
            sound.setSoundRefText(refText); // 设置文本内容
            sound.setDeptId(SecurityUtils.getDeptId()); // 部门
            sound.setSoundFiltration("0"); // 数据权限过滤 0 私有 1公开
            // 插入到数据库
            platformSoundMapper.insertPlatformSound(sound);
            // 处理返回结果
            Map<String, String> result = new HashMap<>();
            result.put("trainAudio", trainFilePath);
            result.put("refAudio", refFilePath);
            return result; // 返回成功响应
        } catch (Exception e) {
            throw new ModelException("上传文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    /**
     * 处理音频文件上传，使用内容指纹避免重复存储
     */
    private String handleAudioFile(MultipartFile audioFile, String basePath) throws IOException {
        // 计算文件内容的MD5哈希值作为指纹
        String fileHash = calculateFileHash(audioFile);
        // 获取原始文件名和扩展名
        String originalFilename = audioFile.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        // 使用哈希值作为文件名，确保相同内容的文件具有相同的文件名
        String uniqueFileName = fileHash + (fileExtension != null ? "." + fileExtension : "");
        String fullFilePath = basePath + "/" + uniqueFileName;
        // 检查文件是否已存在，如果不存在才进行上传
        File targetFile = new File(fullFilePath);
        if (!targetFile.exists()) {
            FileOperateUtils.upload(fullFilePath, audioFile, null);
        }
        return fullFilePath;
    }

    /**
     * 计算文件内容的MD5哈希值
     */
    private String calculateFileHash(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
            byte[] hashBytes = digest.digest();
            // 将字节数组转换为十六进制字符串
            StringBuilder hashString = new StringBuilder();
            for (byte b : hashBytes) {
                hashString.append(String.format("%02x", b));
            }
            return hashString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 这种情况不会发生，因为MD5是Java标准算法
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null) {
            return null;
        }
        int lastIndex = fileName.lastIndexOf('.');
        return lastIndex >= 0 ? fileName.substring(lastIndex + 1) : null;
    }

    /** 上传代训练音频的第一步 */
    @Override
    public Object uploadSoundtrainStepOne(MultipartFile trainAudio) {
        try {
            // 检查训练音频文件是否为空
            if (trainAudio == null || trainAudio.isEmpty()) {
                throw new ModelException("请上传训练音频文件");
            }
            // 限制文件大小
            long maxTrainFileSize = 60 * 1024 * 1024;
            // 检查文件大小（大于规定大小则拒绝上传）
            if (trainAudio.getSize() > maxTrainFileSize) {
                throw new ModelException("训练文件不能超过60MB");
            }

            // 设置文件路径
            String filePath = "ModelCheckpoint/sound";
            // 处理训练音频文件（使用内容指纹）
            String trainFilePath = handleAudioFile(trainAudio, filePath);

            // 创建 PlatformSound 实例并设置属性
            Date nowDate = DateUtils.getNowDate(); // 获取创建时间
            PlatformSound sound = new PlatformSound();
            sound.setSoundTrain(trainFilePath); // 设置训练文件路径
            sound.setCreateBy(getUsername()); // 创建人
            sound.setCreateTime(nowDate); // 创建时间
            sound.setUpdateBy(getUsername()); // 修改人
            sound.setUpdateTime(nowDate); // 修改时间
            sound.setSoundStatus("4"); // 状态 待提交

            // 插入到数据库
            platformSoundMapper.insertPlatformSound(sound);
            Long tempId = sound.getSoundId();

            // 处理返回结果
            Map<String, String> result = new HashMap<>();
            result.put("trainAudio", trainFilePath);
            result.put("tempId", String.valueOf(tempId));
            return result; // 返回成功响应
        } catch (Exception e) {
            throw new ModelException("上传文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    /** 上传代训练音频的第二步 */
    @Override
    public Object uploadSoundrefStepTwo(String tempId, String soundName, MultipartFile refAudio, String refText,
            Long deptId, String soundFiltration) {
        try {
            // 检查声音ID是否有效
            if (tempId == null || tempId.isEmpty()) {
                throw new ModelException("声音ID无效，无法处理");
            }
            // 检查参考音频文件是否为空
            if (refAudio == null || refAudio.isEmpty()) {
                throw new ModelException("请上传参考音频文件");
            }
            // 限制文件大小
            long maxRefFileSize = 10 * 1024 * 1024;
            // 检查文件大小（大于规定大小则拒绝上传）
            if (refAudio.getSize() > maxRefFileSize) {
                throw new ModelException("参考文件不能超过10MB");
            }

            // 设置文件路径
            String filePath = "ModelCheckpoint/sound";
            // 处理参考音频文件（使用内容指纹）
            String refFilePath = handleAudioFile(refAudio, filePath);

            // 创建 PlatformSound 实例并设置属性
            Date nowDate = DateUtils.getNowDate(); // 获取创建时间
            PlatformSound sound = new PlatformSound();
            sound.setSoundId(Long.parseLong(tempId));// 声音ID
            sound.setSoundName(soundName); // 声音名称
            sound.setSoundStatus("4"); // 状态 待提交
            sound.setUpdateBy(getUsername()); // 修改人
            sound.setUpdateTime(nowDate); // 修改时间
            sound.setSoundRef(refFilePath); // 设置参考文件路径
            sound.setSoundRefText(refText); // 设置文本内容
            sound.setDeptId(SecurityUtils.getDeptId()); // 部门
            sound.setSoundFiltration("0"); // 数据权限过滤 0 私有 1公开

            // 更新到数据库
            platformSoundMapper.updatePlatformSound(sound);

            // 处理返回结果
            Map<String, String> result = new HashMap<>();
            result.put("refAudio", refFilePath);
            return result; // 返回成功响应
        } catch (Exception e) {
            // 如果失败，删除之前的文件
            cleanupOnFailure(tempId);
            throw new ModelException("上传文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    /**
     * 上传失败时清理临时记录
     * 
     * @param tempId 临时ID
     */
    private void cleanupOnFailure(String tempId) {
        if (tempId == null || tempId.isEmpty()) {
            return;
        }
        try {
            platformSoundMapper.deletePlatformSoundBySoundId(Long.parseLong(tempId));
        } catch (Exception e) {
            throw new ModelException("清除原始文件失败: " + e.getMessage());
        }
    }

    // 上传声音文件训练接口
    @Override
    public String uploadSoundtrain(MultipartFile file, String filePath) throws Exception {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                throw new ModelException("音频文件不能为空");
            }
            // 检查文件大小（大于60MB则拒绝上传）
            long maxFileSize = 60 * 1024 * 1024; // 60MB
            if (file.getSize() > maxFileSize) {
                throw new ModelException("音频文件不能超过60MB");
            }
            // 设置默认上传路径
            if (filePath == null || filePath.isEmpty()) {
                filePath = "user/" + getUsername() + "/sound/model"; // minio上传路径 model是模型
            }
            // 构建文件名
            String fileName = file.getOriginalFilename();
            String uniqueFileName = System.currentTimeMillis() + "_" + fileName;
            // 合并成最终的上传路径
            String fullPath = filePath + "/" + uniqueFileName;
            // 上传文件到 Oss
            FileOperateUtils.upload(fullPath, file, null);
            return fullPath; // 返回成功响应
        } catch (IOException e) {
            throw new ModelException("上传文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    @Override
    public String uploadSoundref(MultipartFile file, String filePath) throws Exception {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                throw new ModelException("上传的音频文件不能为空");
            }
            // 检查文件大小（大于10MB则拒绝上传）
            long maxFileSize = 10 * 1024 * 1024; // 10MB
            if (file.getSize() > maxFileSize) {
                throw new ModelException("上传的音频文件不能超过10MB");
            }

            // 使用与创建时相同的内容指纹策略
            String basePath = "ModelCheckpoint/sound";
            // 计算文件内容的MD5哈希值作为指纹
            String fileHash = calculateFileHash(file);
            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            // 使用哈希值作为文件名，确保相同内容的文件具有相同的文件名
            String uniqueFileName = fileHash + (fileExtension != null ? "." + fileExtension : "");
            String fullPath = basePath + "/" + uniqueFileName;

            // 检查文件是否已存在，如果不存在才进行上传
            File targetFile = new File(fullPath);
            if (!targetFile.exists()) {
                FileOperateUtils.upload(fullPath, file, null);
            }

            return fullPath;
        } catch (Exception e) {
            throw new ModelException("上传文件失败: " + e.getMessage());
        }
    }

    // 根据声音Id下载参考音频
    @Override
    public void downloadAudioId(Long soundId, HttpServletResponse response) throws Exception {
        // 检查音频的id是否有效
        if (soundId == null) {
            throw new ModelException("声音Id无效，无法下载！");
        }
        // 根据id查询音频的数据
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        // 根据id查询到数据获取到文件的地址是否正确 正确进行下载
        String fileUrl = sound.getSoundRef();
        if (StringUtils.isEmpty(fileUrl)) {
            throw new ModelException("未找到对应的音频文件！");
        }
        // 设置响应类型和文件名
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        // 通过流从URL文件中下载音频文件
        FileOperateUtils.downLoad(fileUrl, response);
    }

    // 声音模型下载
    @Override
    public void downloadModel(Long soundId, String model, String machineCode, HttpServletResponse response)
            throws Exception {
        // 查询声音数据
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (sound == null) {
            throw new ModelException("该声音信息数据不存在！");
        }
        // 验证机器码是否有效
        PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                .selectPlatformMachineCodeByMachineCode(machineCode);
        if (machineCodeObject == null) {
            throw new ModelException("无效的机器码，无法下载文件。");
        }
        // 获取机器码状态
        Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
        // 验证机器码状态是否为“正常”（1表示正常）
        if (machineCodeStatus == 0L) {
            throw new ModelException("机器码状态为禁用，无法下载文件。");
        }
        // 设置响应类型
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        OutputStream outputStream = response.getOutputStream();
        // 根据模型类型下载相应的文件
        String soundFilePath;
        switch (model) {
            case "gpt":
                soundFilePath = sound.getSoundGpt(); // gpt文件
                FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(soundFilePath));
                break;
            case "sovits":
                soundFilePath = sound.getSoundSovits(); // sovits文件
                FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(soundFilePath));
                break;
            default:
                throw new ModelException("未知的模型类型");
        }
        // 下载文件
        FileOperateUtils.downLoad(soundFilePath, outputStream);
        outputStream.close(); // 关闭输出流
    }

    // 修改声音状态为待处理
    @Override
    public int updateSoundState(PlatformSound platformSound) {
        return platformSoundMapper.updateSoundState(platformSound);
    }

    // 重新训练接口 暂时没有重新训练 后续有可能删除
    @Override
    public int retrainAndResetStatus(Long soundId) {
        if (soundId == null) {
            throw new ModelException("声音Id为空，无法处理");
        }
        // 根据 soundId 查询声音信息
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (sound == null) {
            throw new ModelException("该声音信息不存在！Id：" + soundId);
        }

        // 获取文件路径并删除OSS中的文件
        List<String> fileUrls = Arrays.asList(
                sound.getSoundGpt(),
                sound.getSoundSovits());
        for (String fileUrl : fileUrls) {
            if (StringUtils.isNotEmpty(fileUrl)) {
                try {
                    FileOperateUtils.deleteFile(fileUrl);
                } catch (Exception e) {
                    throw new ModelException("删除OSS文件时发生错误: " + e.getMessage() + "，声音ID: " + soundId);
                }
            }
        }

        // 更新声音状态记录状态为待处理
        String currentStatus = sound.getSoundStatus();
        if ("2".equals(currentStatus) || "3".equals(currentStatus)) { // 如果该声音状态是“完成”或“失败”
            sound.setSoundStatus("0"); // 设置为“待审核”
        } else {
            throw new ModelException("声音状态不允许重新训练操作！");
        }

        int updateResult = platformSoundMapper.updateSoundState(sound);
        if (updateResult <= 0) {
            throw new ModelException("更新声音状态失败，声音ID: " + soundId);
        }

        return 1;
    }

    // 根据声音id 模型类型下载模型
    @Override
    public void downloadModelBySoundId(Long soundId, String model, HttpServletResponse response) throws Exception {
        // 查询声音模型
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (sound == null) {
            throw new ModelException("声音模型不存在！");
        }
        // 设置响应类型
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        // 根据模型类型下载相应文件 gpt sovits
        String soundFilePath;
        switch (model) {
            case "gpt":
                soundFilePath = sound.getSoundGpt();// gpt文件
                FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(soundFilePath));
                break;
            case "sovits":
                soundFilePath = sound.getSoundSovits(); // sovits文件
                FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(soundFilePath));
                break;
            default:
                throw new ModelException("未知的模型类型");
        }
        // 下载文件
        FileOperateUtils.downLoad(soundFilePath, response);
    }

    // 审核接口
    @Override
    public int soundAudit(Long soundId, boolean isApproved) {
        if (soundId == null) {
            throw new ModelException("声音Id为空，无法处理！");
        }
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (sound == null) {
            throw new ModelException("未找到该声音信息！");
        }

        // 检查当前状态是否为“待审核”
        if ("0".equals(sound.getSoundStatus())) {
            if (isApproved) {
                sound.setSoundStatus("1"); // 设置状态为“队列中”
                // 创建训练任务
                String xunlian = sound.getSoundTrain(); // 获取训练音频的地址
                Long taskId = platformTaskService.createTaskSAudio(soundId, xunlian); // 任务回调
                if (taskId == null) {
                    throw new ModelException("创建任务失败，声音ID: " + soundId);
                }
            } else {
                sound.setSoundStatus("5"); // 设置状态为“已驳回”
                sound.setSoundFiltration("0"); // 0私有
            }
            int updateResult = platformSoundMapper.updatePlatformSound(sound);
            if (updateResult <= 0) {
                throw new ModelException("更新声音状态失败，声音ID: " + soundId);
            }
            return updateResult;
        } else {
            throw new ModelException("声音状态不允许审核操作！");
        }
    }

    // 创建者查询声音，数据公开 1
    @Override
    public List<PlatformSound> selectSoundList() {
        return platformSoundMapper.selectSoundList(getUsername());
    }

    /**
     * 获取声音文件的存储地址和临时访问凭证
     * 
     * @param soundId 声音ID
     * @return 包含文件地址和临时凭证的映射
     */
    @Override
    public Map<String, Map<String, String>> getSoundFileUrls(Long soundId) throws Exception {
        // 查询声音信息
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (sound == null) {
            throw new ModelException("声音不存在");
        }

        Map<String, Map<String, String>> result = new HashMap<>();

        // 处理参考音频
        if (StringUtils.isNotEmpty(sound.getSoundRef())) {
            Map<String, String> refUrls = new HashMap<>();
            refUrls.put("fileUrl", sound.getSoundRef());
            refUrls.put("presignedUrl", FileOperateUtils.getURL(sound.getSoundRef()));
            result.put("refAudio", refUrls);
        }

        // 处理GPT模型
        if (StringUtils.isNotEmpty(sound.getSoundGpt())) {
            Map<String, String> gptUrls = new HashMap<>();
            gptUrls.put("fileUrl", sound.getSoundGpt());
            gptUrls.put("presignedUrl", FileOperateUtils.getURL(sound.getSoundGpt()));
            result.put("gptModel", gptUrls);
        }

        // 处理Sovits模型
        if (StringUtils.isNotEmpty(sound.getSoundSovits())) {
            Map<String, String> sovitsUrls = new HashMap<>();
            sovitsUrls.put("fileUrl", sound.getSoundSovits());
            sovitsUrls.put("presignedUrl", FileOperateUtils.getURL(sound.getSoundSovits()));
            result.put("sovitsModel", sovitsUrls);
        }

        return result;
    }

    private Map<String, Object> soundUploadSessions = new ConcurrentHashMap<>(); // 存储分片会话
    private Map<String, Map<String, String>> soundUploadIdMappings = new ConcurrentHashMap<>(); // 存储实际的上传ID映射
    // 初始化分片

    @Override
    public Object initSoundUpload(String soundName, String gptFileName, String sovitsFileName, String refAudioFileName,
            long gptFileSize, long sovitsFileSize, long refAudioFileSize, String refText,
            Long deptId, String soundFiltration) throws Exception {
        if (StringUtils.isEmpty(gptFileName) || gptFileSize <= 0 ||
                StringUtils.isEmpty(sovitsFileName) || sovitsFileSize <= 0 ||
                StringUtils.isEmpty(refAudioFileName) || refAudioFileSize <= 0) {
            throw new ServiceException("文件名或大小无效");
        }
        // 检查文件大小限制
        long maxGptSize = 500 * 1024 * 1024; // 500MB
        long maxSovitsSize = 500 * 1024 * 1024; // 500MB
        long maxRefAudioSize = 10 * 1024 * 1024; // 10MB
        if (gptFileSize > maxGptSize || sovitsFileSize > maxSovitsSize || refAudioFileSize > maxRefAudioSize) {
            throw new ServiceException("文件大小超过限制");
        }
        // 生成上传ID和文件路径
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 修改：使用固定路径，移除日期路径部分
        String basePath = "ModelCheckpoint/sound";
        // 处理原始文件名
        sovitsFileName = sovitsFileName.replaceFirst("^([^_]+)_", "");
        gptFileName = gptFileName.replaceFirst("^([^\\-]+)\\-", "-");
        gptFileName = gptFileName.replaceFirst("^([^\\-]+)\\-", "-");
        // 创建声音记录
        PlatformSound sound = new PlatformSound();
        sound.setSoundName(soundName);
        sound.setSoundStatus("1"); // 状态：上传中
        sound.setCreateBy(getUsername());
        sound.setCreateTime(DateUtils.getNowDate());
        sound.setUpdateBy(getUsername());
        sound.setUpdateTime(sound.getCreateTime());
        sound.setSoundRefText(refText);
        sound.setDeptId(deptId != null ? deptId : SecurityUtils.getDeptId());
        sound.setSoundFiltration(soundFiltration);
        // 先插入记录获取soundId
        platformSoundMapper.insertPlatformSound(sound);
        Long soundId = sound.getSoundId();
        // 构造带soundId的文件名
        String finalGptFileName = timestamp + "_" + soundId + gptFileName;
        String finalSovitsFileName = timestamp + "_" + soundId + "_" + sovitsFileName;
        String finalRefAudioFileName = timestamp + "_" + soundId + "_" + refAudioFileName;
        // 构造最终路径（使用固定路径）
        String gptFilePath = basePath + "/" + finalGptFileName;
        String sovitsFilePath = basePath + "/" + finalSovitsFileName;
        String refAudioFilePath = basePath + "/" + finalRefAudioFileName;
        // 设置真实的文件路径
        sound.setSoundGpt(gptFilePath);
        sound.setSoundSovits(sovitsFilePath);
        sound.setSoundRef(refAudioFilePath);
        // 更新记录
        platformSoundMapper.updatePlatformSound(sound);
        // 初始化分片上传
        String uploadId = UUID.randomUUID().toString();

        // 为三种文件类型分别初始化分片上传并保存实际的uploadId
        Map<String, String> actualUploadIds = new HashMap<>();
        actualUploadIds.put("gpt", FileOperateUtils.initMultipartUpload(gptFilePath));
        actualUploadIds.put("sovits", FileOperateUtils.initMultipartUpload(sovitsFilePath));
        actualUploadIds.put("refAudio", FileOperateUtils.initMultipartUpload(refAudioFilePath));
        soundUploadIdMappings.put(uploadId, actualUploadIds);
        // 使用Map保存额外的上传会话信息
        Map<String, Object> sessionData = new HashMap<>();
        sessionData.put("soundId", soundId);
        sessionData.put("gptFilePath", gptFilePath);
        sessionData.put("sovitsFilePath", sovitsFilePath);
        sessionData.put("refAudioFilePath", refAudioFilePath);
        sessionData.put("gptFileSize", gptFileSize);
        sessionData.put("sovitsFileSize", sovitsFileSize);
        sessionData.put("refAudioFileSize", refAudioFileSize);
        sessionData.put("refText", refText);
        sessionData.put("deptId", deptId);
        sessionData.put("soundFiltration", soundFiltration);
        sessionData.put("gptPartETags", new ArrayList<SysFilePartETag>());
        sessionData.put("sovitsPartETags", new ArrayList<SysFilePartETag>());
        sessionData.put("refAudioPartETags", new ArrayList<SysFilePartETag>());
        // 保存每种文件的实际uploadId
        sessionData.put("gptUploadId", actualUploadIds.get("gpt"));
        sessionData.put("sovitsUploadId", actualUploadIds.get("sovits"));
        sessionData.put("refAudioUploadId", actualUploadIds.get("refAudio"));
        soundUploadSessions.put(uploadId, sessionData);
        return Map.of("uploadId", uploadId, "gptFilePath", gptFilePath, "sovitsFilePath", sovitsFilePath,
                "refAudioFilePath", refAudioFilePath, "soundId", soundId);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object uploadSoundChunk(String uploadId, String fileType, String filePath, int chunkIndex,
            MultipartFile chunk) throws Exception {
        if (chunk == null || chunk.isEmpty())
            throw new ServiceException("分片数据不能为空");
        Map<String, Object> sessionData = (Map<String, Object>) soundUploadSessions.get(uploadId);
        if (sessionData == null)
            throw new ServiceException("上传会话不存在");
        // 获取实际的uploadId
        String actualUploadId = (String) sessionData.get(fileType + "UploadId");
        if (actualUploadId == null)
            throw new ServiceException("无效的文件类型或UploadId");
        // 验证文件类型
        if (!"gpt".equals(fileType) && !"sovits".equals(fileType) && !"refAudio".equals(fileType)) {
            throw new ServiceException("无效的文件类型");
        }
        // 验证文件路径匹配
        String expectedPath = "gpt".equals(fileType) ? (String) sessionData.get("gptFilePath")
                : "sovits".equals(fileType) ? (String) sessionData.get("sovitsFilePath")
                        : (String) sessionData.get("refAudioFilePath");
        if (!expectedPath.equals(filePath))
            throw new ServiceException("文件路径不匹配");
        // 上传分片
        String etag = FileOperateUtils.uploadPart(filePath, actualUploadId, chunkIndex + 1,
                chunk.getSize(), chunk.getInputStream());
        if (StringUtils.isEmpty(etag))
            throw new ServiceException("上传分片失败：未获取到ETag");
        // 记录已上传分片
        String partListKey = fileType + "PartETags";
        List<SysFilePartETag> partETags = (List<SysFilePartETag>) sessionData.computeIfAbsent(partListKey,
                k -> new ArrayList<>());
        partETags.add(new SysFilePartETag(chunkIndex + 1, etag, chunk.getSize(), null));
        return Map.of("etag", etag, "chunkIndex", chunkIndex, "partNumber", chunkIndex + 1,
                "fileType", fileType);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object completeSoundUpload(String soundName, String uploadId, String gptFilePath, String sovitsFilePath,
            String refAudioFilePath, String refText, Long deptId, String soundFiltration) throws Exception {
        // 从会话中获取上传会话数据
        Map<String, Object> sessionData = (Map<String, Object>) soundUploadSessions.get(uploadId);
        if (sessionData == null) {
            throw new ServiceException("上传会话不存在");
        }
        // 从会话中获取已保存的分片信息
        List<SysFilePartETag> gptPartETags = (List<SysFilePartETag>) sessionData.get("gptPartETags");
        List<SysFilePartETag> sovitsPartETags = (List<SysFilePartETag>) sessionData.get("sovitsPartETags");
        List<SysFilePartETag> refAudioPartETags = (List<SysFilePartETag>) sessionData.get("refAudioPartETags");
        // 验证分片信息
        validatePartETags(gptPartETags, "gpt模型");
        validatePartETags(sovitsPartETags, "sovits模型");
        validatePartETags(refAudioPartETags, "参考音频");
        // 获取实际的uploadId
        String gptUploadId = (String) sessionData.get("gptUploadId");
        String sovitsUploadId = (String) sessionData.get("sovitsUploadId");
        String refAudioUploadId = (String) sessionData.get("refAudioUploadId");
        // 完成分片合并
        String finalGptPath = FileOperateUtils.completeMultipartUpload(gptFilePath, gptUploadId, gptPartETags);
        String finalSovitsPath = FileOperateUtils.completeMultipartUpload(sovitsFilePath, sovitsUploadId,
                sovitsPartETags);
        String finalRefPath = FileOperateUtils.completeMultipartUpload(refAudioFilePath, refAudioUploadId,
                refAudioPartETags);
        if (StringUtils.isEmpty(finalGptPath) || StringUtils.isEmpty(finalSovitsPath)
                || StringUtils.isEmpty(finalRefPath)) {
            throw new ServiceException("合并分片失败");
        }
        // 清理映射
        soundUploadIdMappings.remove(uploadId);
        // 更新声音记录
        PlatformSound sound = platformSoundMapper.selectPlatformSoundBySoundId((Long) sessionData.get("soundId"));
        if (sound == null) {
            throw new ServiceException("声音记录不存在");
        }
        sound.setSoundGpt(finalGptPath);
        sound.setSoundSovits(finalSovitsPath);
        sound.setSoundRef(finalRefPath);
        sound.setSoundStatus("2"); // 状态：已完成
        sound.setUpdateBy(getUsername());
        sound.setUpdateTime(DateUtils.getNowDate());
        platformSoundMapper.updatePlatformSound(sound);
        // 清理会话
        soundUploadSessions.remove(uploadId);
        return Map.of("soundId", sound.getSoundId(), "soundName", sound.getSoundName(),
                "gptModel", finalGptPath, "sovitsModel", finalSovitsPath, "refAudio", finalRefPath);
    }

    // 分片信息验证方法
    private void validatePartETags(List<SysFilePartETag> partETags, String fileType) throws ServiceException {
        if (partETags == null || partETags.isEmpty()) {
            throw new ServiceException(fileType + "分片信息不能为空");
        }
        List<SysFilePartETag> validParts = partETags.stream()
                .filter(part -> part != null && part.getPartNumber() != null && StringUtils.isNotEmpty(part.getETag()))
                .peek(part -> {
                    int partNumber = part.getPartNumber().intValue();
                    String etag = part.getETag();
                    if (partNumber <= 0 || StringUtils.isEmpty(etag)) {
                        throw new ServiceException(fileType + "分片序号或ETag无效");
                    }
                })
                .collect(Collectors.toList());
        if (validParts.size() != partETags.size()) {
            throw new ServiceException(fileType + "分片信息格式不正确");
        }
    }
}
