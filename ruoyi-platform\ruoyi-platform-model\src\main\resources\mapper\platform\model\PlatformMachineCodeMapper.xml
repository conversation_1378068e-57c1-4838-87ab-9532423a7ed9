<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.model.mapper.PlatformMachineCodeMapper">
    
    <resultMap type="PlatformMachineCode" id="PlatformMachineCodeResult">
        <result property="machineCodeId"    column="machine_code_id"    />
        <result property="machineCodeName"    column="machine_code_name"    />
        <result property="machineCodeStatus"    column="machine_code_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformMachineCodeVo">
        select machine_code_id, machine_code_name, machine_code_status, m.create_by, m.create_time, m.update_by, m.update_time, m.remark from platform_machine_code m left join sys_user u on u.user_name = m.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformMachineCodeList" parameterType="PlatformMachineCode" resultMap="PlatformMachineCodeResult">
        <include refid="selectPlatformMachineCodeVo"/>
        <where>  
            <if test="machineCodeName != null  and machineCodeName != ''"> and machine_code_name like concat('%', #{machineCodeName}, '%')</if>
            ${params.dataScope}
            <if test="machineCodeStatus != null "> and machine_code_status = #{machineCodeStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformMachineCodeByMachineCodeId" parameterType="Long" resultMap="PlatformMachineCodeResult">
        <include refid="selectPlatformMachineCodeVo"/>
        where machine_code_id = #{machineCodeId}
    </select>
        
    <insert id="insertPlatformMachineCode" parameterType="PlatformMachineCode" useGeneratedKeys="true" keyProperty="machineCodeId">
        insert into platform_machine_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="machineCodeName != null">machine_code_name,</if>
            <if test="machineCodeStatus != null">machine_code_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="machineCodeName != null">#{machineCodeName},</if>
            <if test="machineCodeStatus != null">#{machineCodeStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformMachineCode" parameterType="PlatformMachineCode">
        update platform_machine_code m
        <trim prefix="SET" suffixOverrides=",">
            <if test="machineCodeName != null">machine_code_name = #{machineCodeName},</if>
            <if test="machineCodeStatus != null">machine_code_status = #{machineCodeStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where m.machine_code_id = #{machineCodeId}
    </update>

    <delete id="deletePlatformMachineCodeByMachineCodeId" parameterType="Long">
        delete from platform_machine_code where machine_code_id = #{machineCodeId}
    </delete>

    <delete id="deletePlatformMachineCodeByMachineCodeIds" parameterType="String">
        delete from platform_machine_code where machine_code_id in 
        <foreach item="machineCodeId" collection="array" open="(" separator="," close=")">
            #{machineCodeId}
        </foreach>
    </delete>
</mapper>