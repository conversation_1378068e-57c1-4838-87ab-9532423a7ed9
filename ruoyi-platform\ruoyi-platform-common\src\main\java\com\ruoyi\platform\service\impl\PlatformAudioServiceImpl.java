package com.ruoyi.platform.service.impl;

import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.service.IPlatformAudioService;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 音频管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
@Service
public class PlatformAudioServiceImpl implements IPlatformAudioService {

    private static final Logger log = LoggerFactory.getLogger(PlatformAudioServiceImpl.class);

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    /**
     * 查询音频管理
     * 
     * @param audioId 音频管理主键
     * @return 音频管理
     */
    @Override
    public PlatformAudio selectPlatformAudioByAudioId(Long audioId) {
        return platformAudioMapper.selectPlatformAudioByAudioId(audioId);
    }

    /**
     * 查询音频管理列表
     * 
     * @param platformAudio 音频管理
     * @return 音频管理
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformAudio> selectPlatformAudioList(PlatformAudio platformAudio) {
        return platformAudioMapper.selectPlatformAudioList(platformAudio);
    }

    /**
     * 新增音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    @Override
    public int insertPlatformAudio(PlatformAudio platformAudio) {
        platformAudio.setCreateTime(DateUtils.getNowDate());
        return platformAudioMapper.insertPlatformAudio(platformAudio);
    }

    /**
     * 修改音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    @Override
    public int updatePlatformAudio(PlatformAudio platformAudio) {
        platformAudio.setUpdateTime(DateUtils.getNowDate());
        return platformAudioMapper.updatePlatformAudio(platformAudio);
    }

    /**
     * 批量删除音频管理
     * 
     * @param audioIds 需要删除的音频管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deletePlatformAudioByAudioIds(Long[] audioIds) {
        if (audioIds == null || audioIds.length == 0) {
            return 0;
        }
        int totalDeletedCount = 0; // 记录删除总条数
        for (Long audioId : audioIds) {
            try {
                PlatformAudio audio = platformAudioMapper.selectPlatformAudioByAudioId(audioId);
                if (audio == null) {
                    log.warn("音频不存在，ID: {}", audioId);
                    continue;
                }

                String fileUrl = audio.getAudioPath();
                if (fileUrl == null || fileUrl.isEmpty()) {
                    log.warn("文件路径为空，ID: {}", audioId);
                    continue;
                }

                // 删除 MinIO 中的文件
                if (!FileOperateUtils.deleteFile(fileUrl)) {
                    log.warn("MinIO 文件删除失败，路径: {}", fileUrl);
                }

                // 删除数据库记录
                int deletedCount = platformAudioMapper.deletePlatformAudioByAudioId(audioId);
                totalDeletedCount += deletedCount;

            } catch (Exception e) {
                log.error("删除音频时发生错误，ID: {}, 错误信息: {}", audioId, e.getMessage(), e);
            }
        }

        log.info("总共删除成功的音频记录数: {}", totalDeletedCount);
        return totalDeletedCount;
    }

    /**
     * 删除音频管理信息
     * 
     * @param audioId 音频管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformAudioByAudioId(Long audioId) {
        return platformAudioMapper.deletePlatformAudioByAudioId(audioId);
    }

    // 上传音频文件
    @Override
    public Long uploadAudio(MultipartFile file, String filePath, Long categoryId) throws Exception {
        String username = SecurityUtils.getUsername();
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        if (StringUtils.isEmpty(filePath)) {
            filePath = "user/" + username + "/audio/" + currentDate; // 上传路径
        }
        // 构建文件名
        String fileName = file.getOriginalFilename();
        String uniqueFileName = System.currentTimeMillis() + "_" + fileName;
        // 合并成最终的上传路径
        String fullPath = filePath + "/" + uniqueFileName;
        // 上传文件到 MinIO
        FileOperateUtils.upload(fullPath, file, null);
        // 创建 PlatformAudio 实例
        PlatformAudio audio = new PlatformAudio();
        Date nowDate = DateUtils.getNowDate();
        audio.setCategoryId(categoryId); // 分类ID
        // 去除文件扩展名，只在新增时使用
        String audioName = fileName != null && fileName.lastIndexOf('.') > 0
                ? fileName.substring(0, fileName.lastIndexOf('.'))
                : fileName;
        audio.setAudioName(audioName); // 文件名称（去掉扩展名）
        audio.setAudioFrom("0"); // 文件来源 0上传 1合成
        audio.setAudioPath(fullPath); // 上传地址
        audio.setCreateBy(username); // 创建人
        audio.setCreateTime(nowDate); // 创建时间
        platformAudioMapper.insertPlatformAudio(audio); // 确保此方法能返回自增 ID
        Long audioId = audio.getAudioId();
        return audioId;
    }

    // 根据音频的ID去下载音频文件
    @Override
    public void downloadAudioId(Long audioId, Boolean idToName, HttpServletResponse response) throws Exception {
        // 检查音频的id是否有效
        if (audioId == null) {
            throw new ServiceException("音频Id无效，无法下载！");
        }
        // 根据id查询音频数据
        PlatformAudio audio = platformAudioMapper.selectPlatformAudioByAudioId(audioId);
        if (audio == null) {
            throw new ServiceException("未找到对应的音频记录！");
        }
        // 获取文件的地址
        String fileUrl = audio.getAudioPath();
        if (StringUtils.isEmpty(fileUrl)) {
            throw new ServiceException("未找到对应的音频文件！");
        }
        // 设置响应类型和文件名
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        String filename = FileUtils.getName(fileUrl);
        if (idToName) {
            String ext = filename.substring(filename.lastIndexOf(".") + 1);
            filename = audioId + "." + ext; // 使用音频ID作为文件名
        }
        FileUtils.setAttachmentResponseHeader(response, filename);
        // 下载文件
        try (OutputStream outputStream = response.getOutputStream()) {
            FileOperateUtils.downLoad(fileUrl, outputStream);
            outputStream.flush(); // 确保所有的数据都写入到输出流
        } catch (Exception e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("下载失败，请稍后重试: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 根据直播id查询音频信息
     *
     * @param liveId 直播ID
     * @return 关键词列表
     */
    @Override
    public List<PlatformAudio> getAudiosByLiveId(Long liveId) {
        return platformAudioMapper.getAudiosByLiveId(liveId);
    }
    /**
     * 根据路径生成临时url实现播放
     */
    @Override
    public String getTemporaryUrlByPath(String audioPath) {
        try {
            String url = FileOperateUtils.getURL(audioPath);
            return url;
        } catch (Exception e) {
            throw new ServiceException("生成临时URL失败: " + e.getMessage());
        }
    }

    
    /**
     * 根据分类ID查询文本转音频数据
     */
    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<PlatformAudio> audioAndText(PlatformAudio platformAudio){
        return platformAudioMapper.audioAndText(platformAudio);
    }

    /**
     * 根据音频地址生成临时凭证
     * @throws Exception 
     */
    @Override
    public PlatformAudio getAudioDetailByAddress(String audioPath) throws Exception{
        PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
        if (audio == null) {
            throw new ServiceException("未找到当前音频素材!");
        }
        String url = FileOperateUtils.getURL(audio.getAudioPath());
        audio.setAudioUrl(url);  
        return audio;
    }
}
