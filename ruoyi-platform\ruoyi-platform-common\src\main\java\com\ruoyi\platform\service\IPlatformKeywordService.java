package com.ruoyi.platform.service;

import java.util.List;

import com.ruoyi.platform.domain.PlatformKeyword;

/**
 * 关键词管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface IPlatformKeywordService 
{
    /**
     * 查询关键词管理
     * 
     * @param keywordId 关键词管理主键
     * @return 关键词管理
     */
    public PlatformKeyword selectPlatformKeywordByKeywordId(Long keywordId);

    //匹配关键词分类
    public Long listByFormat(List<Long> categoryIds,List<String> strs);
    
    /**
     * 查询关键词管理列表
     * 
     * @param platformKeyword 关键词管理
     * @return 关键词管理集合
     */
    public List<PlatformKeyword> selectPlatformKeywordList(PlatformKeyword platformKeyword);

    /**
     * 新增关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    public int insertPlatformKeyword(PlatformKeyword platformKeyword);

    /**
     * 修改关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    public int updatePlatformKeyword(PlatformKeyword platformKeyword);

    /**
     * 批量删除关键词管理
     * 
     * @param keywordIds 需要删除的关键词管理主键集合
     * @return 结果
     */
    public int deletePlatformKeywordByKeywordIds(Long[] keywordIds);

    /**
     * 删除关键词管理信息
     * 
     * @param keywordId 关键词管理主键
     * @return 结果
     */
    public int deletePlatformKeywordByKeywordId(Long keywordId);

    //根据直播id查询关键词数据
    public List<PlatformKeyword> getKeywordsByLiveId(Long liveId);
}
