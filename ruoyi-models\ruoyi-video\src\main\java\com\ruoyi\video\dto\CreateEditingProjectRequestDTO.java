package com.ruoyi.video.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 创建云剪辑工程请求DTO
 * <p>
 * 用于创建新的云剪辑工程，包含所有必要的参数。
 * 注意: Timeline、TemplateId 有且只有一个非空。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class CreateEditingProjectRequestDTO {

    /**
     * 必填。
     * 云剪辑工程标题。
     */
    @JsonProperty("Title")
    private String title;

    /**
     * 可选。
     * 云剪辑工程描述。
     */
    @JsonProperty("Description")
    private String description;

    /**
     * 可选。
     * 云剪辑工程封面 URL。
     */
    @JsonProperty("CoverURL")
    private String coverURL;

    /**
     * 可选。
     * 云剪辑工程时间线，JSON 格式字符串。
     * 注意: Timeline 与 TemplateId 必须且只有一个非空。
     */
    @JsonProperty("Timeline")
    private String timeline;

    /**
     * 可选。
     * 模板 ID，用于快速通过模板构建时间线。
     * 注意: Timeline 与 TemplateId 必须且只有一个非空。
     */
    @JsonProperty("TemplateId")
    private String templateId;

    /**
     * 可选。
     * 模板对应的素材参数，JSON 格式字符串。
     * 当 TemplateId 不为空时，此项不能为空。
     */
    @JsonProperty("ClipsParam")
    private String clipsParam;

    /**
     * 可选。
     * 模板类型。当使用模板创建工程时需要填写。
     * 取值范围：
     * <ul>
     *   <li>Timeline（默认）：普通模板</li>
     *   <li>VETemplate：高级模板</li>
     * </ul>
     */
    @JsonProperty("TemplateType")
    private String templateType;

    /**
     * 可选。
     * 工程关联的素材映射，JSON 格式字符串。
     */
    @JsonProperty("MaterialMaps")
    private String materialMaps;

    /**
     * 可选。
     * 工程业务配置，JSON 格式字符串。
     * 主要用于直播剪辑工程。
     */
    @JsonProperty("BusinessConfig")
    private String businessConfig;

    /**
     * 可选。
     * 剪辑工程类型。
     * 取值范围：
     * <ul>
     *   <li>EditingProject（默认）：普通剪辑工程</li>
     *   <li>LiveEditingProject：直播剪辑工程</li>
     * </ul>
     */
    @JsonProperty("ProjectType")
    private String projectType;
}