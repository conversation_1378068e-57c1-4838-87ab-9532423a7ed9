<mxfile host="65bd71144e">
    <diagram id="PZYBk4sDCuIX38xMYcUk" name="第 1 页">
        <mxGraphModel dx="880" dy="766" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="标记有RateLimiter注解的方法会被RateLimiterAspect拦截" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="224" y="80" width="380" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=none;html=1;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="获取注解上的时间和次数" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="224" y="150" width="380" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=none;html=1;" parent="1" source="5" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&amp;nbsp;通过拼接{KEY}-{IP/USERID/DEPTID}-{ClassName}-{MethodName}形成rediskey" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="164" y="230" width="500" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="edgeStyle=none;html=1;" parent="1" source="7" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="通过redisTemplate.excute来检测time时间内最大请求次数" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="164" y="310" width="500" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="通过返回的number再次确认请求次数" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="164" y="390" width="500" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>