package com.ruoyi.video.utils;

/**
 * 模板排序类型枚举
 * 
 * 定义了阿里云智能媒体服务(ICE)模板列表的排序方式
 * 可用于 {@link com.ruoyi.video.service.ITemplateService#listTemplates} 方法
 */
public enum TemplateSortType {

    /**
     * 按创建时间升序排序
     * 
     * 对应阿里云API中的 "CreationTime:ASC" 参数值
     * 排序结果：最早创建的模板排在最前
     */
    CREATION_TIME_ASC("CreationTime:ASC"),

    /**
     * 按创建时间降序排序
     * 
     * 对应阿里云API中的 "CreationTime:Desc" 参数值
     * 排序结果：最新创建的模板排在最前
     */
    CREATION_TIME_DESC("CreationTime:Desc");

    private final String value;

    TemplateSortType(String value) {
        this.value = value;
    }

    /**
     * 获取排序类型对应的API参数值
     * 
     * @return 排序类型的字符串值，如 "CreationTime:ASC"
     */
    public String getValue() {
        return value;
    }
}