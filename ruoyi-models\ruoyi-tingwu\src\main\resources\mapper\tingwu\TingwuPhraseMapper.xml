<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tingwu.mapper.TingwuPhraseMapper">

    <resultMap type="TingwuPhrase" id="TingwuPhraseResult">
        <result property="phraseId" column="phraseId" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="wordWeights" column="word_weights" />
        <result property="createTime" column="create_time" />
        <result property="createBy" column="create_by" />
        <result property="updateTime" column="update_time" />
        <result property="updateBy" column="update_by" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectTingwuPhraseVo">
        select
            tp.phraseId,
            tp.name,
            tp.description,
            tp.word_weights,
            tp.create_time,
            tp.create_by,
            tp.update_time,
            tp.update_by,
            tp.remark
        from tingwu_phrase tp
        left join sys_user u on u.user_name = tp.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectTingwuPhraseList" parameterType="TingwuPhrase" resultMap="TingwuPhraseResult">
        <include refid="selectTingwuPhraseVo"/>
        <where>
            <if test="phraseId != null  and phraseId != ''"> and tp.phraseId = #{phraseId}</if>
            <if test="name != null  and name != ''"> and tp.name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and tp.description = #{description}</if>
            <if test="wordWeights != null  and wordWeights != ''"> and tp.word_weights = #{wordWeights}</if>
            <if test="createBy != null  and createBy != ''"> and tp.create_by = #{createBy}</if>
            ${params.dataScope}
        </where>
    </select>


    <insert id="insertTingwuPhrase" parameterType="TingwuPhrase">
        insert into tingwu_phrase
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phraseId != null">phraseId,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="wordWeights != null">word_weights,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phraseId != null">#{phraseId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="wordWeights != null">#{wordWeights},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTingwuPhrase" parameterType="TingwuPhrase">
        update tingwu_phrase
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="wordWeights != null">word_weights = #{wordWeights},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where tingwu_phrase.phraseId = #{phraseId}
    </update>


    <!-- deleteTingwuPhraseByPhraseIdIds -->
    <delete id="deleteTingwuPhraseByPhraseIdIds" parameterType="String">
        delete from tingwu_phrase where phraseId in
        <foreach item="phraseId" collection="array" open="(" separator="," close=")">
            #{phraseId}
        </foreach>
    </delete>

</mapper>