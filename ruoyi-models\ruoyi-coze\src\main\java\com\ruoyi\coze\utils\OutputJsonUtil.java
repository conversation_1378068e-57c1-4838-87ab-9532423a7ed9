package com.ruoyi.coze.utils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * 工具类：用于解析带有 Output 字段的 JSON 字符串
 */
public class OutputJsonUtil {
    private static final Pattern OUTPUT_PATTERN = Pattern.compile("\"Output\":\"(\\{.*?\\})\"");

    /**
     * 解析 result 对象中的 Output 字段内容为 JSON 字符串
     * 
     * @param result 原始对象
     */
    public static String parseOutputToJsonString(Map<String, Object> result) {
        if (result.get("output") != null) {
            String str = result.get("output").toString();
            Matcher matcher = OUTPUT_PATTERN.matcher(str);
            if (matcher.find()) {
                String outputContent = matcher.group(1);
                String replaceStr = outputContent.replace("\\\"", "\"");
                JSONObject obj = JSON.parseObject(replaceStr);
                result.put("output", obj);
            }
        }
        if(result.get("data") != null){
            result.put("data", JSON.parseObject(result.get("data").toString()));
        }
        return JSON.toJSONString(result);
    }
}
