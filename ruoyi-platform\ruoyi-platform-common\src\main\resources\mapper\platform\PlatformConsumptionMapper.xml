<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformConsumptionMapper">
    
    <resultMap type="PlatformConsumption" id="PlatformConsumptionResult">
        <result property="consumptionId"    column="consumption_id"    />
        <result property="userName"    column="user_name"    />
        <result property="consumptionTitle"    column="consumption_title"    />
        <result property="consumptionStaus"    column="consumption_staus"    />
        <result property="consumptionHashrate"    column="consumption_hashrate"    />
        <result property="consumptionResidue"    column="consumption_residue"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformConsumptionVo">
        select consumption_id, user_name, consumption_title, consumption_staus, consumption_hashrate, consumption_residue, create_by, create_time, update_by, update_time, remark from platform_consumption
    </sql>

    <select id="selectPlatformConsumptionList" parameterType="PlatformConsumption" resultMap="PlatformConsumptionResult">
        <include refid="selectPlatformConsumptionVo"/>
        <where>  
            <if test="consumptionTitle != null  and consumptionTitle != ''"> and consumption_title = #{consumptionTitle}</if>
            <if test="consumptionStaus != null  and consumptionStaus != ''"> and consumption_staus = #{consumptionStaus}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="createTime != null and updateTime != null"> 
                and create_time BETWEEN #{createTime} AND #{updateTime}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformConsumptionByConsumptionId" parameterType="Long" resultMap="PlatformConsumptionResult">
        <include refid="selectPlatformConsumptionVo"/>
        where platform_consumption.consumption_id = #{consumptionId}
    </select>
        
    <insert id="insertPlatformConsumption" parameterType="PlatformConsumption" useGeneratedKeys="true" keyProperty="consumptionId">
        insert into platform_consumption
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="consumptionTitle != null">consumption_title,</if>
            <if test="consumptionStaus != null">consumption_staus,</if>
            <if test="consumptionHashrate != null">consumption_hashrate,</if>
            <if test="consumptionResidue != null">consumption_residue,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="consumptionTitle != null">#{consumptionTitle},</if>
            <if test="consumptionStaus != null">#{consumptionStaus},</if>
            <if test="consumptionHashrate != null">#{consumptionHashrate},</if>
            <if test="consumptionResidue != null">#{consumptionResidue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformConsumption" parameterType="PlatformConsumption">
        update platform_consumption
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="consumptionTitle != null">consumption_title = #{consumptionTitle},</if>
            <if test="consumptionStaus != null">consumption_staus = #{consumptionStaus},</if>
            <if test="consumptionHashrate != null">consumption_hashrate = #{consumptionHashrate},</if>
            <if test="consumptionResidue != null">consumption_residue = #{consumptionResidue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where platform_consumption.consumption_id = #{consumptionId}
    </update>

    <delete id="deletePlatformConsumptionByConsumptionId" parameterType="Long">
        delete from platform_consumption where consumption_id = #{consumptionId}
    </delete>

    <delete id="deletePlatformConsumptionByConsumptionIds" parameterType="String">
        delete from platform_consumption where consumption_id in 
        <foreach item="consumptionId" collection="array" open="(" separator="," close=")">
            #{consumptionId}
        </foreach>
    </delete>

    <!-- 查询某个用户在过去一年内的算力消费信息，并统计每个功能的花费及其占比 --> 
    <select id="selectConsumptionSummary" resultType="com.ruoyi.platform.domain.vo.PlatformConsumptionVo">
        SELECT consumption_title, SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))) AS total_consumption_hashrate,
        (SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))) / (SELECT SUM(CAST(consumption_hashrate AS DECIMAL(20, 2)))
        FROM platform_consumption WHERE user_name = #{userName} AND create_time BETWEEN #{startDate} AND #{endDate}
         AND consumption_staus = '1')) * 100 AS percentage FROM platform_consumption WHERE user_name = #{userName}
        AND create_time BETWEEN #{startDate} AND #{endDate} AND consumption_staus = '1' GROUP BY consumption_title;
    </select>

    <!-- 获取算力消费统计数据 -->
    <select id="selectConsumptionStatistics" resultType="java.util.HashMap">
        SELECT 
            (SELECT COALESCE(SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))), 0) 
             FROM platform_consumption 
             WHERE consumption_staus = '1') AS total_consumption,
            
            (SELECT COALESCE(SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))), 0) 
             FROM platform_consumption 
             WHERE consumption_staus = '1' 
             AND DATE(create_time) = CURDATE()) AS today_consumption,
            
            (SELECT COALESCE(SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))), 0) 
             FROM platform_consumption 
             WHERE consumption_staus = '1' 
             AND DATE(create_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) AS week_consumption,
            
            (SELECT COALESCE(SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))), 0) 
             FROM platform_consumption 
             WHERE consumption_staus = '1' 
             AND DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')) AS month_consumption,
            
            (SELECT COUNT(DISTINCT user_name) 
             FROM platform_consumption 
             WHERE consumption_staus = '1') AS active_users,
            
            (SELECT consumption_title 
             FROM platform_consumption 
             WHERE consumption_staus = '1' 
             GROUP BY consumption_title 
             ORDER BY SUM(CAST(consumption_hashrate AS DECIMAL(20, 2))) DESC 
             LIMIT 1) AS most_used_module
    </select>
</mapper>