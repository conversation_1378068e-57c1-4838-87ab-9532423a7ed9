package com.ruoyi.tingwu.domain;



import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 音视频任务对象 tingwu_trans
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Schema(description = "音视频任务对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class TingwuTrans extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long vaId;

    /** 音视频名称 */
    @Schema(title = "音视频名称")
    @Excel(name = "音视频名称")
    private String vaName;

    /** 音视频状态 */
    @Schema(title = "音视频状态")
    @Excel(name = "音视频状态")
    private String vaStatus;

    /** 音视频地址 */
    @Schema(title = "音视频地址")
    @Excel(name = "音视频地址")
    private String vaPath;

    /** 任务编号 */
    @Schema(title = "任务编号")
    @Excel(name = "任务编号")
    private String taskId;

    /** 关键词 */
    @Schema(title = "关键词")
    @Excel(name = "关键词")
    private String tags;

    @Schema(title = "音视频时长（毫秒）")
    @Excel(name = "音视频时长（毫秒）")
    private Long duration;

    /** 全文概要 */
    @Schema(title = "全文概要")
    @Excel(name = "全文概要")
    private String summary;

    /** 章节速览 */
    @Schema(title = "章节速览")
    @Excel(name = "章节速览")
    private String overview;

    /** 发言总结 */
    @Schema(title = "发言总结")
    @Excel(name = "发言总结")
    private String statements;

    /** 问答回顾 */
    @Schema(title = "问答回顾")
    @Excel(name = "问答回顾")
    private String qaReview;

    @Schema(title = "对话章节")
    @Excel(name = "对话章节")
    private String paragraphs;

    @Schema(title = "开始时间")
    private String startTime;

    @Schema(title = "结束时间")
    private String endTime;

}
