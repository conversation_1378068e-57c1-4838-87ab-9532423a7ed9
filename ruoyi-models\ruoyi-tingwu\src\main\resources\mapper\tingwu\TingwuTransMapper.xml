<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tingwu.mapper.TingwuTransMapper">

    <resultMap type="TingwuTrans" id="TingwuTransResult">
        <result property="vaId" column="va_id" />
        <result property="vaName" column="va_name" />
        <result property="vaStatus" column="va_status" />
        <result property="vaPath" column="va_path" />
        <result property="taskId" column="task_id" />
        <result property="tags" column="tags" />
        <result property="duration" column="duration" />
        <result property="summary" column="summary" />
        <result property="overview" column="overview" />
        <result property="statements" column="statements" />
        <result property="qaReview" column="qa_review" />
        <result property="paragraphs" column="paragraphs" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectTingwuTransVo">
        select
            tt.va_id,
            tt.va_name,
            tt.va_status,
            tt.va_path,
            tt.task_id,
            tt.tags,
            tt.duration,
            tt.summary,
            tt.overview,
            tt.statements,
            tt.qa_review,
            tt.paragraphs,
            tt.create_by,
            tt.create_time,
            tt.update_by,
            tt.update_time,
            tt.remark
        from tingwu_trans tt
         left join sys_user u on u.user_name = tt.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectTingwuTransList" parameterType="TingwuTrans" resultMap="TingwuTransResult">
        <include refid="selectTingwuTransVo"/>
        <where>
            <if test="vaName != null  and vaName != ''"> and tt.va_name like concat('%', #{vaName}, '%')</if>
            <if test="vaStatus != null  and vaStatus != ''"> and tt.va_status = #{vaStatus}</if>
            <if test="vaPath != null  and vaPath != ''"> and tt.va_path = #{vaPath}</if>
            <if test="taskId != null  and taskId != ''"> and tt.task_id = #{taskId}</if>
            <if test="tags != null  and tags != ''"> and tt.tags = #{tags}</if>
            <if test="summary != null  and summary != ''"> and tt.summary = #{summary}</if>
            <if test="overview != null  and overview != ''"> and tt.overview = #{overview}</if>
            <if test="statements != null  and statements != ''"> and tt.statements = #{statements}</if>
            <if test="qaReview != null  and qaReview != ''"> and tt.qa_review = #{qaReview}</if>
            <if test="duration != null  and duration != ''"> and tt.duration = #{duration}</if>
            <if test="paragraphs != null  and paragraphs != ''"> and tt.paragraphs = #{paragraphs}</if>
            <if test="startTime != null and startTime != ''"> and tt.create_time &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and tt.create_time &lt;= #{endTime}</if>
            <if test="createBy != null and createBy != ''"> and tt.create_by = #{createBy}</if>
            ${params.dataScope}
        </where>
        <choose>
            <when test="params.sortField == 'createTime' and params.sortOrder == 'asc'">
            ORDER BY tt.create_time ASC
            </when>
            <when test="params.sortField == 'createTime' and params.sortOrder == 'desc'">
            ORDER BY tt.create_time DESC
            </when>
            <when test="params.sortField == 'duration' and params.sortOrder == 'asc'">
            ORDER BY tt.duration ASC
            </when>
            <when test="params.sortField == 'duration' and params.sortOrder == 'desc'">
            ORDER BY tt.duration DESC
            </when>
        </choose>
    </select>

    <select id="selectTingwuTransByVaId" parameterType="Long" resultMap="TingwuTransResult">
        <include refid="selectTingwuTransVo"/>
        where tt.va_id = #{vaId}
    </select>

    <insert id="insertTingwuTrans" parameterType="TingwuTrans">
        insert into tingwu_trans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vaId != null">va_id,</if>
            <if test="vaName != null">va_name,</if>
            <if test="vaStatus != null">va_status,</if>
            <if test="vaPath != null">va_path,</if>
            <if test="taskId != null">task_id,</if>
            <if test="tags != null">tags,</if>
            <if test="duration != null">duration,</if>
            <if test="summary != null">summary,</if>
            <if test="overview != null">overview,</if>
            <if test="statements != null">statements,</if>
            <if test="qaReview != null">qa_review,</if>
            <if test="paragraphs != null">paragraphs,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vaId != null">#{vaId},</if>
            <if test="vaName != null">#{vaName},</if>
            <if test="vaStatus != null">#{vaStatus},</if>
            <if test="vaPath != null">#{vaPath},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="tags != null">#{tags},</if>
            <if test="duration != null">#{duration},</if>
            <if test="summary != null">#{summary},</if>
            <if test="overview != null">#{overview},</if>
            <if test="statements != null">#{statements},</if>
            <if test="qaReview != null">#{qaReview},</if>
            <if test="paragraphs != null">#{paragraphs},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTingwuTrans" parameterType="TingwuTrans">
        update tingwu_trans
        <trim prefix="SET" suffixOverrides=",">
            <if test="vaName != null">va_name = #{vaName},</if>
            <if test="vaStatus != null">va_status = #{vaStatus},</if>
            <if test="vaPath != null">va_path = #{vaPath},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="overview != null">overview = #{overview},</if>
            <if test="statements != null">statements = #{statements},</if>
            <if test="qaReview != null">qa_review = #{qaReview},</if>
            <if test="paragraphs != null">paragraphs = #{paragraphs},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where tingwu_trans.va_id = #{vaId}
    </update>

    <delete id="deleteTingwuTransByVaId" parameterType="Long">
        delete from tingwu_trans where va_id = #{vaId}
    </delete>

    <delete id="deleteTingwuTransByVaIds" parameterType="String">
        delete from tingwu_trans where va_id in
        <foreach item="vaId" collection="array" open="(" separator="," close=")">
            #{vaId}
        </foreach>
    </delete>

    <!-- updateTingwuTransByTaskId -->

    <update id="updateTingwuTransByTaskId" parameterType="TingwuTrans">
        update tingwu_trans
        <trim prefix="SET" suffixOverrides=",">
            <if test="vaName != null">va_name = #{vaName},</if>
            <if test="vaStatus != null">va_status = #{vaStatus},</if>
            <if test="vaPath != null">va_path = #{vaPath},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="overview != null">overview = #{overview},</if>
            <if test="statements != null">statements = #{statements},</if>
            <if test="qaReview != null">qa_review = #{qaReview},</if>
            <if test="paragraphs != null">paragraphs = #{paragraphs},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>



</mapper>