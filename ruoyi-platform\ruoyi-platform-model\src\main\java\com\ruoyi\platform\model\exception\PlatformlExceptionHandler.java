package com.ruoyi.platform.model.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.platform.model.utils.ModelException;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 */
@RestControllerAdvice
public class PlatformlExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(PlatformlExceptionHandler.class);

    /**
     * 模型操作异常处理
     */
    @ExceptionHandler(ModelException.class)
    public AjaxResult handleModelOperationException(ModelException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.error("模型操作失败" + e.getMessage());
    }
}
