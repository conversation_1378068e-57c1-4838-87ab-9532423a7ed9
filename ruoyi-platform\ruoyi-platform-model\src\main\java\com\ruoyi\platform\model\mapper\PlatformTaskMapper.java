package com.ruoyi.platform.model.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.ruoyi.platform.model.domain.PlatformTask;

/**
 * 任务管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
@Mapper
public interface PlatformTaskMapper 
{
    /**
     * 查询任务管理
     * 
     * @param taskId 任务管理主键
     * @return 任务管理
     */
    public PlatformTask selectPlatformTaskByTaskId(Long taskId);

    /**
     * 查询任务管理列表
     * 
     * @param platformTask 任务管理
     * @return 任务管理集合
     */
    public List<PlatformTask> selectPlatformTaskList(PlatformTask platformTask);

    /**
     * 新增任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    public int insertPlatformTask(PlatformTask platformTask);

    /**
     * 修改任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    public int updatePlatformTask(PlatformTask platformTask);

    /**
     * 删除任务管理
     * 
     * @param taskId 任务管理主键
     * @return 结果
     */
    public int deletePlatformTaskByTaskId(Long taskId);

    /**
     * 批量删除任务管理
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformTaskByTaskIds(Long[] taskIds);

    //获取任务队列待处理的数据 0为待处理 1处理中 2为已完成
    @Select("select * from platform_task where task_status = 0 LIMIT 5")
    public List<PlatformTask> selectPlatformTaskListSuccess();
    
    // 根据任务Id和任务状态 更新任务状态 和 机器码  0为待处理 1处理中 2为已完成
    @Update("UPDATE platform_task SET task_status = 1, machine_code = #{machineCode} WHERE task_id = #{taskId} AND task_status = 0")
    public int updateStateByStatusAndCode(Long taskId, String machineCode);

    //只显示任务的结果、任务ID、任务内容
    @Select("select task_id, task_result, task_content from platform_task where task_id = #{taskId}")
    public List<PlatformTask> selectIdAndResult(Long taskId);

    // 根据 ID 更新任务结果
    @Update("UPDATE platform_task SET task_result = #{taskResult} WHERE task_id = #{taskId}")
    public void updateTaskResultById(Long taskId, String taskResult);

    // 查询所有一小时前进入处理中状态的任务 0 待处理 1处理中 2 完成 3失败
    @Select(" SELECT * FROM platform_task WHERE task_status = '1' AND update_time < #{oneHourAgo}")
    public List<PlatformTask> selectStuckTasks(Date oneHourAgo); 
}
