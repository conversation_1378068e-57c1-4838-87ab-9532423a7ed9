package com.ruoyi.platform.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.platform.domain.PlatformAudio;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 音频管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface IPlatformAudioService 
{
    /**
     * 查询音频管理
     * 
     * @param audioId 音频管理主键
     * @return 音频管理
     */
    public PlatformAudio selectPlatformAudioByAudioId(Long audioId);

    /**
     * 查询音频管理列表
     * 
     * @param platformAudio 音频管理
     * @return 音频管理集合
     */
    public List<PlatformAudio> selectPlatformAudioList(PlatformAudio platformAudio);

    /**
     * 新增音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    public int insertPlatformAudio(PlatformAudio platformAudio);

    /**
     * 修改音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    public int updatePlatformAudio(PlatformAudio platformAudio);

    /**
     * 批量删除音频管理
     * 
     * @param audioIds 需要删除的音频管理主键集合
     * @return 结果
     */
    public int deletePlatformAudioByAudioIds(Long[] audioIds);

    /**
     * 删除音频管理信息
     * 
     * @param audioId 音频管理主键
     * @return 结果
     */
    public int deletePlatformAudioByAudioId(Long audioId);

    //上传音频文件
    public Long uploadAudio(MultipartFile file, String filePath, Long categoryId) throws Exception;

    //根据音频的ID去下载音频文件
    public void downloadAudioId(Long audioId, Boolean idToName, HttpServletResponse response) throws Exception;

    //根据直播id查询音频信息
    public List<PlatformAudio> getAudiosByLiveId(Long liveId);

    //   根据音频路径获取临时URL用于在线播放
    public String getTemporaryUrlByPath(String audioPath);

    // 根据分类ID查询文本转音频数据
    public List<PlatformAudio> audioAndText(PlatformAudio platformAudio);

    /**
     * 根据音频地址生成临时凭证
     */
    public PlatformAudio getAudioDetailByAddress(String audioPath) throws Exception;
}
