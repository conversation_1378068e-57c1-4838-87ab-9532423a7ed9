package com.ruoyi.platform.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.domain.SysScript;
import com.ruoyi.platform.mapper.SysScriptMapper;
import com.ruoyi.platform.service.ISysScriptService;

/**
 * 脚本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@Service
public class SysScriptServiceImpl implements ISysScriptService 
{
    @Autowired
    private SysScriptMapper sysScriptMapper;

    /**
     * 查询脚本
     * 
     * @param scriptId 脚本主键
     * @return 脚本
     */
    @Override
    public SysScript selectSysScriptByScriptId(Long scriptId)
    {
        return sysScriptMapper.selectSysScriptByScriptId(scriptId);
    }

    /**
     * 查询脚本列表
     * 
     * @param sysScript 脚本
     * @return 脚本
     */
    @Override
    public List<SysScript> selectSysScriptList(SysScript sysScript)
    {
        return sysScriptMapper.selectSysScriptList(sysScript);
    }

    /**
     * 新增脚本
     * 
     * @param sysScript 脚本
     * @return 结果
     */
    @Override
    public int insertSysScript(SysScript sysScript)
    {
        sysScript.setCreateTime(DateUtils.getNowDate());
        return sysScriptMapper.insertSysScript(sysScript);
    }

    /**
     * 修改脚本
     * 
     * @param sysScript 脚本
     * @return 结果
     */
    @Override
    public int updateSysScript(SysScript sysScript)
    {
        sysScript.setUpdateTime(DateUtils.getNowDate());
        return sysScriptMapper.updateSysScript(sysScript);
    }

    /**
     * 批量删除脚本
     * 
     * @param scriptIds 需要删除的脚本主键
     * @return 结果
     */
    @Override
    public int deleteSysScriptByScriptIds(Long[] scriptIds)
    {
        return sysScriptMapper.deleteSysScriptByScriptIds(scriptIds);
    }

    /**
     * 删除脚本信息
     * 
     * @param scriptId 脚本主键
     * @return 结果
     */
    @Override
    public int deleteSysScriptByScriptId(Long scriptId)
    {
        return sysScriptMapper.deleteSysScriptByScriptId(scriptId);
    }
}
