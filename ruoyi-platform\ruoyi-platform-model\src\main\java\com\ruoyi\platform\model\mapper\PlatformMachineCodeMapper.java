package com.ruoyi.platform.model.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.model.domain.PlatformMachineCode;

/**
 * 机器管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-20
 */
public interface PlatformMachineCodeMapper 
{
    /**
     * 查询机器管理
     * 
     * @param machineCodeId 机器管理主键
     * @return 机器管理
     */
    public PlatformMachineCode selectPlatformMachineCodeByMachineCodeId(Long machineCodeId);

    /**
     * 查询机器管理列表
     * 
     * @param platformMachineCode 机器管理
     * @return 机器管理集合
     */
    public List<PlatformMachineCode> selectPlatformMachineCodeList(PlatformMachineCode platformMachineCode);

    /**
     * 新增机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    public int insertPlatformMachineCode(PlatformMachineCode platformMachineCode);

    /**
     * 修改机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    public int updatePlatformMachineCode(PlatformMachineCode platformMachineCode);

    /**
     * 删除机器管理
     * 
     * @param machineCodeId 机器管理主键
     * @return 结果
     */
    public int deletePlatformMachineCodeByMachineCodeId(Long machineCodeId);

    /**
     * 批量删除机器管理
     * 
     * @param machineCodeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformMachineCodeByMachineCodeIds(Long[] machineCodeIds);

    // 查询机器管理列表
    @Select("select * from platform_machine_code where machine_code_name = #{machineCode}")
    public PlatformMachineCode selectPlatformMachineCodeByMachineCode(String machineCode);
}
