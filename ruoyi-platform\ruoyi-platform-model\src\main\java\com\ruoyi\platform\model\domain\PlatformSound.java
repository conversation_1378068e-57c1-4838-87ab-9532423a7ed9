package com.ruoyi.platform.model.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.Id;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.validation.constraints.NotBlank;

/**
 * 声音管理对象 platform_sound
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Schema(description = "声音管理对象")
public class PlatformSound extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    public interface AddGroup {}
    public interface UpdateGroup {}

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(title = "主键")
    private Long soundId;

    /** 声音名称 */
    @Schema(title = "声音名称")
    @Excel(name = "声音名称")
    @NotBlank(message = "声音名称不能为空",groups = AddGroup.class)
    private String soundName;

    /** 声音状态 */
    @Schema(title = "声音状态")
    @Excel(name = "声音状态")
    private String soundStatus;

    /** 训练音频 */
    @Schema(title = "训练音频")
    @Excel(name = "训练音频")
    @NotBlank(message = "训练音频不能为空",groups = AddGroup.class)
    private String soundTrain;

    /** 参考音频 */
    @Schema(title = "参考音频")
    @Excel(name = "参考音频")
    @NotBlank(message = "参考音频不能为空",groups = AddGroup.class)
    private String soundRef;

    /** gpt模型路径 */
    @Schema(title = "gpt模型路径")
    @Excel(name = "gpt模型路径")
    @NotBlank(message = "gpt模型不能为空",groups = AddGroup.class)
    private String soundGpt;

    /** sovits模型路径 */
    @Schema(title = "sovits模型路径")
    @Excel(name = "sovits模型路径")
    @NotBlank(message = "sovits模型不能为空",groups = AddGroup.class)
    private String soundSovits;

    /** 参考文本 */
    @Schema(title = "参考文本")
    @Excel(name = "参考文本")
    private String soundRefText;

    /** 声音部门id */
    @Schema(title = "声音部门id")
    @Excel(name = "声音部门id")
    private Long deptId;

    /** 数据过滤  0 私有 1公开 */
    @Schema(title = "数据过滤")
    @Excel(name = "数据过滤")
    private String soundFiltration;
    
    @TableField(exist = false)
    private String deptName; //获取到部门名称

    public void setSoundId(Long soundId) 
    {
        this.soundId = soundId;
    }

    public Long getSoundId() 
    {
        return soundId;
    }

    public String getSoundFiltration() {
        return soundFiltration;
    }

    public void setSoundFiltration(String soundFiltration) {
        this.soundFiltration = soundFiltration;
    }

    public void setSoundName(String soundName) 
    {
        this.soundName = soundName;
    }

    public String getSoundName() 
    {
        return soundName;
    }

    public void setSoundStatus(String soundStatus) 
    {
        this.soundStatus = soundStatus;
    }

    public String getSoundStatus() 
    {
        return soundStatus;
    }


    public void setSoundTrain(String soundTrain) 
    {
        this.soundTrain = soundTrain;
    }

    public String getSoundTrain() 
    {
        return soundTrain;
    }


    public void setSoundRef(String soundRef) 
    {
        this.soundRef = soundRef;
    }

    public String getSoundRef() 
    {
        return soundRef;
    }

    public void setSoundGpt(String soundGpt) 
    {
        this.soundGpt = soundGpt;
    }

    public String getSoundGpt() 
    {
        return soundGpt;
    }


    public void setSoundSovits(String soundSovits) 
    {
        this.soundSovits = soundSovits;
    }

    public String getSoundSovits() 
    {
        return soundSovits;
    }

    public void setSoundRefText(String soundRefText) 
    {
        this.soundRefText = soundRefText;
    }

    public String getSoundRefText() 
    {
        return soundRefText;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("soundId", getSoundId())
            .append("soundName", getSoundName())
            .append("soundStatus", getSoundStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("soundTrain", getSoundTrain())
            .append("soundRef", getSoundRef())
            .append("soundGpt", getSoundGpt())
            .append("soundSovits", getSoundSovits())
            .append("soundRefText", getSoundRefText())
            .append("deptId", getDeptId())
            .append("filtration", getSoundFiltration())
            .toString();
    }
}
