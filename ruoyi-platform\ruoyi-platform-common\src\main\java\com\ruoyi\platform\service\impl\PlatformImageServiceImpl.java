package com.ruoyi.platform.service.impl;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.hibernate.service.spi.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.mapper.PlatformImageMapper;
import com.ruoyi.platform.service.IPlatformImageService;

@Service
public class PlatformImageServiceImpl implements IPlatformImageService 
{
    private static final Logger log = LoggerFactory.getLogger(PlatformImageServiceImpl.class);

    @Autowired
    private PlatformImageMapper platformImageMapper;
    
    @Override
    public PlatformImage selectPlatformImageByImageId(Long imageId) {
        return platformImageMapper.selectPlatformImageByImageId(imageId);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformImage> selectPlatformImageList(PlatformImage platformImage) {
        return platformImageMapper.selectPlatformImageList(platformImage);
    }

    @Override
    public int deletePlatformImageByImageIds(Long[] imageIds) {
        List<PlatformImage> imagesToDelete = platformImageMapper.selectPlatformImagesByImageIds(imageIds);
        for (PlatformImage image : imagesToDelete) {
            if (StringUtils.isNotEmpty(image.getImageAddress())) {
                try {
                    FileOperateUtils.deleteFile(image.getImageAddress());
                } catch (Exception e) {
                    log.warn("删除文件失败: {}", e.getMessage());
                }
            }
        }
        return platformImageMapper.deletePlatformImageByImageIds(imageIds);
    }

    @Override
    public PlatformImage getImageDetailByAddress(String imageAddress) throws Exception {
        Map<String, Object> condition = new HashMap<>();
        condition.put("imageAddress", imageAddress);
        List<PlatformImage> images = platformImageMapper.selectImagesByCondition(condition);
        if (images == null || images.isEmpty()) {
            throw new ServiceException("未找到对应形象！");
        }
        PlatformImage image = images.get(0);
        String url = FileOperateUtils.getURL(image.getImageAddress());
        image.setUrl(url.toString());
        return image;
    }

    private static final long MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
    // 临时存储uploadId与md5关联
    private final ConcurrentHashMap<String, String> uploadIdToMd5 = new ConcurrentHashMap<>();
    // 临时存储uploadId与imageId的关联
    private final ConcurrentHashMap<String, Long> uploadIdToImageId = new ConcurrentHashMap<>(); 
    
    @Override
    public Object initOssUpload(String imageName, String fileName, long fileSize) throws Exception {
        if (fileSize > MAX_FILE_SIZE) throw new ServiceException("文件不能超过500MB");
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestamp = String.valueOf(System.currentTimeMillis());
        timestamp = timestamp.substring(timestamp.length() - 6);
        String objectName = String.format("%s/%s/%s_%s", "video/image", currentDate, timestamp, fileName);
        String uploadId = FileOperateUtils.initMultipartUpload(objectName);
        return Map.of("uploadId", uploadId, "filePath", objectName, "imageName", imageName);
    }

    @Override
    public Object uploadOssChunk(String uploadId, String filePath, int chunkIndex, MultipartFile chunk) throws Exception{
        boolean isFirstChunk = (chunkIndex + 1) == 1;
        if (isFirstChunk) {
            String md5 = Md5Utils.getMd5(chunk);
            uploadIdToMd5.put(uploadId, md5);
        }
        String etag = FileOperateUtils.uploadPart(filePath, uploadId, chunkIndex + 1, chunk.getSize(), chunk.getInputStream());
        if (StringUtils.isEmpty(etag)) {
            throw new ServiceException("上传分片失败：未获取到ETag");
        }
        return Map.of("etag", etag, "chunkIndex", chunkIndex, "partNumber", chunkIndex + 1);
    }

    @Override
    public Object completeOssUpload(String imageName, String uploadId, String filePath, List<SysFilePartETag> partETags) throws Exception {
        List<SysFilePartETag> validParts = partETags.stream()
            .filter(Objects::nonNull)
            .filter(part -> part.getPartNumber() != null && StringUtils.isNotEmpty(part.getETag()))
            .collect(Collectors.toList());
        validParts.sort(Comparator.comparingInt(SysFilePartETag::getPartNumber));
        String finalPath = FileOperateUtils.completeMultipartUpload(filePath, uploadId, validParts);
        if (StringUtils.isEmpty(finalPath)) {
            throw new ServiceException("合并分片失败");
        }
        String md5 = uploadIdToMd5.remove(uploadId);
        PlatformImage image = createImageRecord(imageName, finalPath);
        image.setMd5(md5);
        platformImageMapper.insertPlatformImage(image);
        return Map.of("imageId", image.getImageId(), "imageName", imageName, "imagePath", finalPath,
            "partCount", validParts.size(), "fastUpload", false);
    }

    @Override
    public Object startEditImage(Long imageId, String fileName, long fileSize) throws Exception {
        if (fileSize > MAX_FILE_SIZE) throw new ServiceException("文件不能超过500MB");
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestamp = String.valueOf(System.currentTimeMillis());
        timestamp = timestamp.substring(timestamp.length() - 6);
        String objectName = String.format("%s/%s/%s_%s", "video/image", currentDate, timestamp, fileName);
        String uploadId = FileOperateUtils.initMultipartUpload(objectName);
        uploadIdToImageId.put(uploadId, imageId);
        return Map.of("uploadId", uploadId, "filePath", objectName, "imageId", imageId);
    }

    @Override
    public Object finishEditImage(Long imageId, String uploadId, String filePath, List<SysFilePartETag> partETags, String imageName) throws Exception {
        PlatformImage image = platformImageMapper.selectPlatformImageByImageId(imageId);
        String oldPath = image.getImageAddress();
        String oldMd5 = image.getMd5();
        if (uploadId != null && !uploadId.isEmpty()) {
            String newPath = FileOperateUtils.completeMultipartUpload(filePath, uploadId, partETags);
            if (newPath == null) {
                throw new ServiceException("合并分片失败");
            }
            image.setImageAddress(newPath);
            String newMd5 = uploadIdToMd5.remove(uploadId);
            if (newMd5 != null && !newMd5.equals(oldMd5) && oldPath != null) {
                FileOperateUtils.deleteFile(oldPath);
            }
            image.setMd5(newMd5);
        }
        image.setImageName(imageName);
        image.setUpdateBy(SecurityUtils.getUsername());
        image.setUpdateTime(DateUtils.getNowDate());
        platformImageMapper.updatePlatformImage(image);
        if(uploadId!=null && !uploadId.isEmpty()){
            uploadIdToImageId.remove(uploadId);
        }
        return Map.of("imageId", image.getImageId(), "imageName", image.getImageName(), "imageAddress", image.getImageAddress());
    }

    private PlatformImage createImageRecord(String imageName, String imageAddress) {
        PlatformImage image = new PlatformImage();
        image.setImageName(imageName);
        image.setImageAddress(imageAddress);
        image.setImageStatus("2"); // 状态设置为已上传
        image.setCreateBy(SecurityUtils.getUsername());
        image.setCreateTime(DateUtils.getNowDate());
        image.setUpdateBy(SecurityUtils.getUsername());
        image.setUpdateTime(DateUtils.getNowDate());
        return image;
    }
}
