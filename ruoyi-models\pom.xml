<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-models</artifactId>

    <properties>

    </properties>

    <description>
        中间件
    </description>

    <dependencyManagement>
        <dependencies>
            <!-- 代码生成-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-models-starter</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 手机号认证-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-tfa-phone</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 邮箱认证 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-tfa-email</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 消息模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-message</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- ruoyi-coze模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-coze</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- ruoyi-tingwu模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-tingwu</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

             <!-- ruoyi-video模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-video</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-message</module>
        <module>ruoyi-models-starter</module>
        <module>ruoyi-coze</module>
        <module>ruoyi-video</module>
        <module>ruoyi-tingwu</module>
    </modules>
    <packaging>pom</packaging>

</project>