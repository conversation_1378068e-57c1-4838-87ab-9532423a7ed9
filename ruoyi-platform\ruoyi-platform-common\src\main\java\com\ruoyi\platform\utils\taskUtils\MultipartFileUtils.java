package com.ruoyi.platform.utils.taskUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * MultipartFile工具类
 * 提供创建和处理MultipartFile的实用方法
 */
public class MultipartFileUtils {

    /**
     * 从字节数组创建MultipartFile实例
     * 
     * @param content 文件内容字节数组
     * @param name 表单字段名称
     * @param originalFilename 原始文件名
     * @param contentType 内容类型
     * @return 创建的MultipartFile实例
     */
    public static MultipartFile createFromBytes(byte[] content, String name, String originalFilename, String contentType) {
        return new ByteArrayMultipartFile(content, name, originalFilename, contentType);
    }
    
    /**
     * 创建具有指定Content-Type的MultipartFile包装器
     * 
     * @param file 原始MultipartFile实例
     * @param contentType 要设置的新Content-Type
     * @return 包装后的MultipartFile实例
     */
    public static MultipartFile createWithContentType(MultipartFile file, String contentType) {
        return new ContentTypeMultipartFile(file, contentType);
    }
    
    /**
     * 从文件创建MultipartFile实例，适用于大文件
     */
    public static MultipartFile createFromFile(File file, String name, String originalFilename, String contentType) {
        return new FileMultipartFile(file, name, originalFilename, contentType);
    }
    
    /**
     * 从字节数组创建MultipartFile的实现
     * 用于替代CustomMultipartFile
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String name, String originalFilename, String contentType) {
            this.content = content;
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content != null ? content.length : 0;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
    
    /**
     * 具有自定义ContentType的MultipartFile包装器
     * 用于替代ContentTypeMultipartFile
     */
    private static class ContentTypeMultipartFile implements MultipartFile {
        private final MultipartFile originalFile;
        private final String contentType;

        public ContentTypeMultipartFile(MultipartFile file, String contentType) {
            this.originalFile = file;
            this.contentType = contentType;
        }

        @Override
        public String getContentType() {
            return this.contentType;
        }

        // 委托方法
        @Override
        public String getName() { return originalFile.getName(); }

        @Override
        public String getOriginalFilename() { return originalFile.getOriginalFilename(); }

        @Override
        public boolean isEmpty() { return originalFile.isEmpty(); }

        @Override
        public long getSize() { return originalFile.getSize(); }

        @Override
        public byte[] getBytes() throws IOException { return originalFile.getBytes(); }

        @Override
        public InputStream getInputStream() throws IOException { return originalFile.getInputStream(); }

        @Override
        public void transferTo(File dest) throws IOException { originalFile.transferTo(dest); }
    }
    
    /**
     * 从文件创建MultipartFile的实现，适用于大视频文件
     */
    private static class FileMultipartFile implements MultipartFile {
        private final File file;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public FileMultipartFile(File file, String name, String originalFilename, String contentType) {
            this.file = file;
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return file == null || file.length() == 0;
        }

        @Override
        public long getSize() {
            return file != null ? file.length() : 0;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return FileUtils.readFileToByteArray(file);
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new FileInputStream(file);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            FileUtils.copyFile(file, dest);
        }
    }
}
