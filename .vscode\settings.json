{
    "java.configuration.updateBuildConfiguration": "interactive",
    "java.compile.nullAnalysis.mode": "disabled",
    "maven.view": "hierarchical",
    "maven.executable.options": "-T 4 -s ./conf/settings.xml",
    "java.configuration.maven.userSettings": "./conf/settings.xml",
    "maven.pomfile.autoUpdateEffectivePOM": true,
    "java.debug.settings.hotCodeReplace": "auto",
    "spring-boot.ls.java.home": "",
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=9 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms4G -Xlog:disable",
    "maven.excludedFolders": [
        "**/.vscode",
        "**/.idea",
        "**/target",
        "**/.*",
        "**/node_modules",
        "**/target",
        "**/bin",
        "**/archetype-resources"
    ],
    "boot-java.rewrite.refactorings.on": true,
    "maven.executable.preferMavenWrapper": true,
    "java.import.maven.enabled": true,
    "java.dependency.packagePresentation": "hierarchical",
    "git.autofetch": true,
    "git.autofetchPeriod": 60,
    "workbench.activityBar.orientation": "vertical",
    "commentTranslate.hover.enabled": true,
}