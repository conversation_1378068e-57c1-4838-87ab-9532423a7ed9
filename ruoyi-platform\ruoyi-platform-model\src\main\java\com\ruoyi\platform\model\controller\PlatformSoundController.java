package com.ruoyi.platform.model.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.model.domain.PlatformSound;
import com.ruoyi.platform.model.domain.PlatformSound.AddGroup;
import com.ruoyi.platform.model.domain.PlatformSound.UpdateGroup;
import com.ruoyi.platform.model.service.IPlatformSoundService;
import com.ruoyi.platform.model.utils.ModelException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 声音管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/platform/sound")
@Tag(name = "【声音管理】管理")
public class PlatformSoundController extends BaseController {
    
    @Autowired
    private IPlatformSoundService platformSoundService;

    /**
     * 查询声音管理列表
     */
    @Operation(summary = "查询声音管理列表")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformSound platformSound) {
        startPage();
        List<PlatformSound> list = platformSoundService.selectPlatformSoundList(platformSound);
        return getDataTable(list);
    }

    /**
     * 导出声音管理列表
     */
    @Operation(summary = "导出声音管理列表")
    @PreAuthorize("@ss.hasPermi('platform:sound:export')")
    @Log(title = "声音管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformSound platformSound) {
        List<PlatformSound> list = platformSoundService.selectPlatformSoundList(platformSound);
        ExcelUtil<PlatformSound> util = new ExcelUtil<PlatformSound>(PlatformSound.class);
        util.exportExcel(response, list, "声音管理数据");
    }

    /**
     * 获取声音管理详细信息
     */
    @Operation(summary = "获取声音管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:sound:query')")
    @GetMapping(value = "/{soundId}")
    public AjaxResult getInfo(@PathVariable("soundId") Long soundId) {
        return success(platformSoundService.selectPlatformSoundBySoundId(soundId));
    }

    /**
     * 新增声音管理
     */
    @Operation(summary = "新增声音管理")
    //@PreAuthorize("@ss.hasPermi('platform:sound:add')")
    @Log(title = "声音管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(value =  AddGroup.class) @RequestBody PlatformSound platformSound) {
        platformSound.setCreateBy(getUsername());
        platformSound.setUpdateBy(getUsername());
        return toAjax(platformSoundService.insertPlatformSound(platformSound));
    }

    /**
     * 修改声音管理
     */
    @Operation(summary = "修改声音管理")
    //@PreAuthorize("@ss.hasPermi('platform:sound:edit')")
    @Log(title = "声音管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated(value= UpdateGroup.class) @RequestBody PlatformSound platformSound) {
        return toAjax(platformSoundService.updatePlatformSound(platformSound));
    }

    /**
     * 删除声音管理
     */
    @Operation(summary = "删除声音管理")
    //@PreAuthorize("@ss.hasPermi('platform:sound:remove')")
    @Log(title = "声音管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{soundIds}")
    public AjaxResult remove(@PathVariable(name = "soundIds") Long[] soundIds) {
        if (soundIds == null || soundIds.length == 0) {
            throw new ModelException("声音的Id不能为空");
        }
        // 删除之前检查声音状态是否有正在训练
        for (Long soundId : soundIds) {
            PlatformSound platformSound = platformSoundService.selectPlatformSoundBySoundId(soundId);
            if (platformSound != null && "1".equals(platformSound.getSoundStatus())) {
                throw new ModelException("当前声音文件正在训练中，无法删除！");
            }
        }
        int totalDeletedCount = platformSoundService.deletePlatformSoundBySoundIds(soundIds);
        return totalDeletedCount > 0  ? AjaxResult.success("成功删除声音总数: " + totalDeletedCount) : AjaxResult.error("未删除任何声音文件");
    }


    /**
     * 上传声音文件训练接口
     */
    @Operation(summary = "上传声音文件训练接口")
    @PostMapping("/uploadSoundtrain")
    public R<String> uploadSoundtrain(@RequestParam("file") MultipartFile file,
                                      @RequestParam(value = "filePath", required = false) String filePath) throws Exception {
        String sound = platformSoundService.uploadSoundtrain(file, filePath);
        return R.ok(sound);
    }

    /**
     * 声音参考音频通用上传接口
     */
    @Operation(summary = "声音参考音频通用上传接口")
    @Deprecated(since = "1.1.0", forRemoval = true)
    @PostMapping("/uploadSoundref")
    public R<String> uploadSoundref(@RequestParam("file") MultipartFile file,
                                    @RequestParam(value = "filePath", required = false) String filePath) throws Exception {
        String sound = platformSoundService.uploadSoundref(file, filePath);
        return R.ok(sound);
    }

    /**
     * 添加待训练声音
     */
    @Operation(summary = "添加待训练声音")
    @Log(title = "添加待训练声音", businessType = BusinessType.INSERT)
    @PostMapping("/uploadSoundtrainAndRef")
    public R<Object> uploadSoundtrainAndRef(@Validated(value = AddGroup.class) @RequestParam("soundName") String soundName,
            @RequestParam("trainAudio") MultipartFile trainAudio, @RequestParam("refAudio") MultipartFile refAudio,
            @RequestParam("refText") String refText, @RequestParam(value = "deptId", required = false) Long deptId,
             @RequestParam(value = "soundFiltration", required = false) String soundFiltration) throws Exception {
        Object sound = platformSoundService.uploadSoundtrainAndRef(soundName, trainAudio, refAudio, refText, deptId, soundFiltration);
        return R.ok(sound);
    }

    /**
     * 添加待训练声音（第一步：上传训练音频）
     */
    @Operation(summary = "添加待训练声音-第一步")
    @Log(title = "上传训练音频", businessType = BusinessType.INSERT)
    @PostMapping("/uploadSoundtrainStepOne")
    public R<Object> uploadSoundtrainStepOne(@RequestParam("trainAudio") MultipartFile trainAudio) throws Exception {
        Object result =  platformSoundService.uploadSoundtrainStepOne(trainAudio);
        return R.ok(result);
    }
    /**
     * 添加待训练声音（第二步：上传参考音频并完成）
     */
    @Operation(summary = "添加待训练声音-第二步")
    @Log(title = "上传参考音频并完成", businessType = BusinessType.INSERT)
    @PostMapping("/uploadSoundrefStepTwo")
    public R<Object> uploadSoundrefStepTwo(@Validated(value = AddGroup.class) 
    @RequestParam("tempId")String tempId,@RequestParam("soundName") String soundName,
        @RequestParam("refAudio") MultipartFile refAudio,
        @RequestParam("refText") String refText, @RequestParam(value = "deptId", required = false) Long deptId,
        @RequestParam(value = "soundFiltration", required = false) String soundFiltration) throws Exception {
    Object sound = platformSoundService.uploadSoundrefStepTwo(tempId,soundName, refAudio, refText, deptId, soundFiltration);
    return R.ok(sound);
    }

    /**
     * 根据声音Id下载参考音频
     */
    @Operation(summary = "根据声音Id下载参考音频")
    @Deprecated(since = "1.1.0", forRemoval = true)
    @GetMapping("/downloadSoundRefBySoundId")
    public void downloadSoundRefBySoundId(@RequestParam("soundId") Long soundId, HttpServletResponse response) throws Exception {
        platformSoundService.downloadAudioId(soundId, response);
    }

    /**
     * 重新训练声音模型  暂时没有重新训练  后续有可能删除
     */
    @Operation(summary = "重新训练声音模型")
    @PostMapping("/retrainSoundModel/{soundId}")
    public AjaxResult retrainSoundModel(@PathVariable Long soundId) {
        int model = platformSoundService.retrainAndResetStatus(soundId);
        return success(model);
    }

    /**
     * 根据声音Id、模型类型下载模型
     */
    @Operation(summary = "根据声音Id、模型类型下载模型")
    @Deprecated(since = "1.1.0", forRemoval = true)
    @Parameters({
            @Parameter(name = "soundId", description = "声音ID", required = true),
            @Parameter(name = "model", description = "模型类型  gpt/sovits", required = true)
    })
    @GetMapping("/downloadModelBySoundId/{soundId}/{model}")
    public void downloadModelBySoundId(@PathVariable("soundId") Long soundId, @PathVariable("model") String model, HttpServletResponse response) throws Exception {
        platformSoundService.downloadModelBySoundId(soundId, model, response);
    }

    /**
     * 审核声音模型
     */
    @Operation(summary = "审核声音模型")
    @PostMapping("/soundAudit/{soundId}")
    public AjaxResult soundAudit(@PathVariable Long soundId,@RequestParam boolean isApproved) {
        return success(platformSoundService.soundAudit(soundId,isApproved));
    }

    /**
     * 根据创建人查询声音
     */
    @Operation(summary = "根据创建人查询声音")
    @GetMapping("/soundCreateBy")
    public TableDataInfo soundCreateBy() {
        return getDataTable(platformSoundService.selectSoundList());
    }

    /**
     * 根据声音Id下载参考音频、模型生成临时凭证
     */
    @Operation(summary = "根据声音Id下载参考音频、模型生成临时凭证")
    @GetMapping("/getSoundUrls/{soundId}")
    @Anonymous
    public AjaxResult getSoundUrls(@PathVariable("soundId") Long soundId) throws Exception {
        Map<String, Map<String, String>> urls = platformSoundService.getSoundFileUrls(soundId);
        return success(urls);
    }

    /**
     * 初始化声音模型分片上传
     */
    @Operation(summary = "初始化声音模型分片上传")
    @PostMapping("/initUpload")
    public AjaxResult initSoundUpload(@RequestParam("soundName") String soundName,
           @RequestParam("gptFileName") String gptFileName, @RequestParam("sovitsFileName") String sovitsFileName,
           @RequestParam("refAudioFileName") String refAudioFileName, @RequestParam("gptFileSize") long gptFileSize,
           @RequestParam("sovitsFileSize") long sovitsFileSize,
           @RequestParam("refAudioFileSize") long refAudioFileSize, @RequestParam("refText") String refText,
           @RequestParam(value = "deptId", required = false) Long deptId,
           @RequestParam("soundFiltration") String soundFiltration) throws Exception {
        return AjaxResult.success(platformSoundService.initSoundUpload(soundName, gptFileName, sovitsFileName,      refAudioFileName, gptFileSize, sovitsFileSize, refAudioFileSize, refText, deptId, soundFiltration));
    }

    /**
     * 上传声音模型分片
     */
    @Operation(summary = "上传声音模型分片")
    @PostMapping("/uploadChunk")
    public AjaxResult uploadSoundChunk(@RequestParam("uploadId") String uploadId,
           @RequestParam("fileType") String fileType, @RequestParam("filePath") String filePath,
           @RequestParam("chunkIndex") int chunkIndex, @RequestParam("chunk") MultipartFile chunk) throws Exception {
        return AjaxResult.success(platformSoundService.uploadSoundChunk( uploadId, fileType, filePath, 
            chunkIndex, chunk ));
    }

    /**
     * 完成声音模型分片上传
     */
    @Operation(summary = "完成声音模型分片上传")
    @PostMapping("/completeUpload")
    public AjaxResult completeSoundUpload( @RequestParam("soundName") String soundName,
        @RequestParam("uploadId") String uploadId, @RequestParam("gptFilePath") String gptFilePath,
        @RequestParam("sovitsFilePath") String sovitsFilePath,
        @RequestParam("refAudioFilePath") String refAudioFilePath, @RequestParam("refText") String refText,
        @RequestParam(value = "deptId", required = false) Long deptId,
        @RequestParam("soundFiltration") String soundFiltration ) throws Exception {
        return AjaxResult.success(platformSoundService.completeSoundUpload(soundName, uploadId, gptFilePath, sovitsFilePath, refAudioFilePath, refText, deptId, soundFiltration ));
    }
}
