package com.ruoyi.tingwu.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.tingwu.domain.TingwuPhrase;
import com.ruoyi.tingwu.service.ITingwuPhraseService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 词表信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/tingwu/phrase")
@Tag(name = "【词表信息】管理")
public class TingwuPhraseController extends BaseController {
    @Autowired
    private ITingwuPhraseService tingwuPhraseService;

    /**
     * 查询词表信息列表
     */
    @Operation(summary = "查询词表信息列表")
    @GetMapping("/list")
    public TableDataInfo list(TingwuPhrase tingwuPhrase) {
        startPage();
        List<TingwuPhrase> list = tingwuPhraseService.selectTingwuPhraseList(tingwuPhrase);
        return getDataTable(list);
    }

    /**
     * 获取词表信息详细信息
     */
    @Operation(summary = "获取词表信息详细信息")
    @GetMapping(value = "/{phraseId}")
    public AjaxResult getInfo(@PathVariable("phraseId") String phraseId) {
        try {
            return AjaxResult.success(tingwuPhraseService.selectTingwuPhraseByPhraseId(phraseId));
        } catch (Exception e) {
            return AjaxResult.error("获取词表信息详细信息失败" + e.getMessage());
        }
    }

    /**
     * 新增词表信息
     */
    @Operation(summary = "新增词表信息")
    // @PreAuthorize("@ss.hasPermi('tingwu:phrase:add')")
    @Log(title = "词表信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TingwuPhrase tingwuPhrase) {
        try {
            return AjaxResult.success(tingwuPhraseService.insertTingwuPhrase(tingwuPhrase));
        } catch (Exception e) {
            return AjaxResult.error("新增词表失败" + e.getMessage());
        }

    }

    /**
     * 修改词表信息
     */
    @Operation(summary = "修改词表信息")
    @Log(title = "词表信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TingwuPhrase tingwuPhrase) {
        try {
            return AjaxResult.success(tingwuPhraseService.updateTingwuPhrase(tingwuPhrase));
        } catch (Exception e) {
            return AjaxResult.error("修改词表信息" + e.getMessage());
        }
    }

    /**
     * 删除词表信息
     */
    @Operation(summary = "删除词表信息")
    @Log(title = "词表信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{phraseIds}")
    public AjaxResult remove(@PathVariable(name = "phraseIds") String[] phraseIds) {
        try {
            return AjaxResult.success(tingwuPhraseService.deleteTingwuPhraseByPhraseIdIds(phraseIds));
        } catch (Exception e) {
            return AjaxResult.error("修改词表信息" + e.getMessage());
        }
    }
}
