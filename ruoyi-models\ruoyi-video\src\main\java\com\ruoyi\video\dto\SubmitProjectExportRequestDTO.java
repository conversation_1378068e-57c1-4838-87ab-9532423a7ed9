package com.ruoyi.video.dto;

import lombok.Data;

/**
 * 云剪辑工程导出请求DTO
 * <p>
 * 用于封装提交云剪辑工程导出任务的请求参数。
 * 对应阿里云ICE API的 SubmitProjectExportJob 接口。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
public class SubmitProjectExportRequestDTO {

    /**
     * 云剪辑工程 ID
     * <p>
     * 注意：ProjectId、Timeline 两个参数二选一必填
     * </p>
     */
    private String projectId;

    /**
     * 云剪辑任务时间线
     * <p>
     * 具体结构定义，请参见 Timeline 配置说明。
     * 注意：ProjectId、Timeline 两个参数二选一必填
     * </p>
     * 示例：{"VideoTracks":[{"VideoTrackClips":[{"MediaId":"****4d7cf14dc7b83b0e801c****"},{"MediaId":"****4d7cf14dc7b83b0e801c****"}]}]}
     */
    private String timeline;

    /**
     * 工程导出类型
     * <p>
     * 取值：
     * - BaseTimeline：时间线
     * - AdobePremierePro: Adobe PR 工程（当前仅上海、北京、杭州、深圳区域支持，其余区域不支持）
     * </p>
     * 默认值：BaseTimeline
     */
    private String exportType = "BaseTimeline";

    /**
     * 导出结果以及产生的中间文件输出路径，JSON 格式
     * <p>
     * 目前仅支持输出到用户 OSS 上。包含以下字段：
     * - Bucket：OSS bucket 名称，必填
     * - Prefix：生成文件的路径前缀，非必填，不填时默认为根目录
     * - Width: 目标成片的宽，正整数。非必填，不填时根据传入的剪辑工程或者时间线自动估算
     * - Height: 目标成片的高，正整数。非必填，不填时根据传入的剪辑工程或者时间线自动估算
     * </p>
     * 示例：{"Bucket": "example-bucket", "Prefix": "example_prefix", "Width": 1920, "Height": 1080}
     */
    private String outputMediaConfig;

    /**
     * 用户自定义设置，JSON 格式
     * <p>
     * 示例：{"NotifyAddress":"http://xx.xx.xxx","Key":"Valuexxx"}
     * </p>
     */
    private String userData;

    /**
     * OSS Bucket 名称
     * <p>
     * 用于构建 outputMediaConfig 的便捷字段
     * </p>
     */
    private String bucket;

    /**
     * 生成文件的路径前缀
     * <p>
     * 用于构建 outputMediaConfig 的便捷字段
     * </p>
     */
    private String prefix;

    /**
     * 目标成片的宽度
     * <p>
     * 用于构建 outputMediaConfig 的便捷字段
     * </p>
     */
    private Integer width;

    /**
     * 目标成片的高度
     * <p>
     * 用于构建 outputMediaConfig 的便捷字段
     * </p>
     */
    private Integer height;

    /**
     * 回调通知地址
     * <p>
     * 用于构建 userData 的便捷字段
     * </p>
     */
    private String notifyAddress;
}
