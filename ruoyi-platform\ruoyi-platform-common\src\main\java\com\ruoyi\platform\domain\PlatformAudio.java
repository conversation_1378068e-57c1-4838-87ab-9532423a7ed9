package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.GetMapping;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 音频管理对象 platform_audio
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
@Schema(description = "音频管理对象")
public class PlatformAudio extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long audioId;

    /** 分类id */
    @Schema(title = "分类id")
    @Excel(name = "分类id")
    @NotNull(message = "分类ID不能为空",groups = {GetMapping.class})
    private Long categoryId;

    /** 声音id */
    @Schema(title = "声音id")
    @Excel(name = "声音id")
    private Long soundId;

    /** 文案id */
    @Schema(title = "文案id")
    @Excel(name = "文案id")
    private Long articleId;

    /** 音频地址 */
    @Schema(title = "音频地址")
    @Excel(name = "音频地址")
    private String audioPath;

    /** 音频来源 */
    @Schema(title = "音频来源")
    @Excel(name = "音频来源")
    private String audioFrom;

    /** 音频名称 */
    @Schema(title = "音频名称")
    @Excel(name = "音频名称")
    @NotBlank(message = "音频名称不能为空")
    private String audioName;

    @TableField(exist = false)
    @Schema(title = "文案内容")
    private String content;

     /** 音频名称 */
     @Schema(title = "音频Md5")
     @Excel(name = "音频Md5")
     private String audioMd5;

    public String getAudioMd5() {
        return audioMd5;
    }

     public void setAudioMd5(String audioMd5) {
         this.audioMd5 = audioMd5;
     }

    @TableField(exist = false)
    private String audioUrl;

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setAudioId(Long audioId) {
        this.audioId = audioId;
    }

    public Long getAudioId() {
        return audioId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setSoundId(Long soundId) {
        this.soundId = soundId;
    }

    public Long getSoundId() {
        return soundId;
    }

    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }

    public Long getArticleId() {
        return articleId;
    }

    public void setAudioPath(String audioPath) {
        this.audioPath = audioPath;
    }

    public String getAudioPath() {
        return audioPath;
    }

    public void setAudioFrom(String audioFrom) {
        this.audioFrom = audioFrom;
    }

    public String getAudioFrom() {
        return audioFrom;
    }

    public void setAudioName(String audioName) {
        this.audioName = audioName;
    }

    public String getAudioName() {
        return audioName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("audioId", getAudioId())
                .append("categoryId", getCategoryId())
                .append("soundId", getSoundId())
                .append("articleId", getArticleId())
                .append("audioPath", getAudioPath())
                .append("audioFrom", getAudioFrom())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("audioName", getAudioName())
                .append("audioMd5", getAudioMd5())
                .toString();
    }
}
