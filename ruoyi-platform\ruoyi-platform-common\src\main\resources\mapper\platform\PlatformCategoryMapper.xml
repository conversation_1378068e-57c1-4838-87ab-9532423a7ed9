<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformCategoryMapper">
    
    <resultMap type="PlatformCategory" id="PlatformCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryTitle"    column="category_title"    />
        <result property="projectId"    column="project_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformCategoryVo">
        SELECT pc.category_id, pc.category_title, pp.project_title,pc.project_id, pc.create_by, pc.create_time, pc.update_by, 
        pc.update_time, pc.remark FROM  platform_category pc 
        LEFT JOIN  platform_project pp ON pp.project_id = pc.project_id
        LEFT JOIN sys_user u ON u.user_name = pc.create_by 
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformCategoryList" parameterType="PlatformCategory" resultMap="PlatformCategoryResult">
        <include refid="selectPlatformCategoryVo"/>
        <where>  
            <if test="categoryTitle != null  and categoryTitle != ''"> and category_title like concat('%', #{categoryTitle}, '%')</if>
            <if test="projectId != null  and projectId != ''"> and pc.project_id = #{projectId}</if>
            <if test="projectTitle != null  and projectTitle != ''"> and project_title = #{projectTitle}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformCategoryByCategoryId" parameterType="Long" resultMap="PlatformCategoryResult">
        <include refid="selectPlatformCategoryVo"/>
        where pc.category_id = #{categoryId}
    </select>
        
    <insert id="insertPlatformCategory" parameterType="PlatformCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into platform_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryTitle != null and categoryTitle != ''">category_title,</if>
            <if test="projectId != null">project_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryTitle != null and categoryTitle != ''">#{categoryTitle},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformCategory" parameterType="PlatformCategory">
        update platform_category pc
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryTitle != null and categoryTitle != ''">category_title = #{categoryTitle},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where pc.category_id = #{categoryId}
    </update>

    <delete id="deletePlatformCategoryByCategoryId" parameterType="Long">
        delete from platform_category where category_id = #{categoryId}
    </delete>

    <!--根据传入的数组数据 查找一一对应的数据-->
    <select id="getCategoriesByIds" parameterType="java.util.List" resultMap="PlatformCategoryResult"> 
        SELECT category_id, category_title FROM platform_category WHERE category_id IN
        <foreach item='id' collection='ids' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </select>

    <select id="getCategoriesByLiveId" parameterType="Long" resultMap="PlatformCategoryResult">
        SELECT DISTINCT pc.* FROM platform_live l LEFT JOIN platform_scenecon s ON l.scenecon_id = s.scenecon_id
        LEFT JOIN platform_goods g ON l.goods_id = g.goods_id JOIN platform_category pc ON 
            (s.scenecon_interaction_id IS NOT NULL AND FIND_IN_SET(pc.category_id, REPLACE(REPLACE(s.scenecon_interaction_id,'[',''),']','')) > 0)
            OR (s.scenecon_questions_id IS NOT NULL AND FIND_IN_SET(pc.category_id, REPLACE(REPLACE(s.scenecon_questions_id,'[',''),']','')) > 0)
            OR (g.goods_interaction_id IS NOT NULL AND FIND_IN_SET(pc.category_id, REPLACE(REPLACE(g.goods_interaction_id,'[',''),']','')) > 0)
            OR (g.goods_questions_id IS NOT NULL AND FIND_IN_SET(pc.category_id, REPLACE(REPLACE(g.goods_questions_id,'[',''),']','')) > 0)
        WHERE l.live_id = #{liveId}
    </select>
</mapper>