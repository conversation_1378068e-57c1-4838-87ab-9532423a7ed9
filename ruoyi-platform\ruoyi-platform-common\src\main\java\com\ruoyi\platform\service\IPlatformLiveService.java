package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.domain.vo.PlatformLiveVo;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 直播管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-08
 */
public interface IPlatformLiveService 
{
    /**
     * 查询直播管理
     * 
     * @param liveId 直播管理主键
     * @return 直播管理
     */
    public PlatformLive selectPlatformLiveByLiveId(Long liveId);

    public PlatformLiveVo selectPlatformLiveVoByLiveId(Long liveId);
    
    /**
     * 查询直播相关音频列表
     */
    public Map<Long, List<Long>> selectAudioList(Long liveId);

    /**
     * 查询直播管理列表
     * 
     * @param platformLive 直播管理
     * @return 直播管理集合
     */
    public List<PlatformLive> selectPlatformLiveList(PlatformLive platformLive);

    /**
     * 新增直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    public int insertPlatformLive(PlatformLive platformLive);

    /**
     * 修改直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    public int updatePlatformLive(PlatformLive platformLive);

    /**
     * 批量删除直播管理
     * 
     * @param liveIds 需要删除的直播管理主键集合
     * @return 结果
     */
    public int deletePlatformLiveByLiveIds(Long[] liveIds);

    /**
     * 删除直播管理信息
     * 
     * @param liveId 直播管理主键
     * @return 结果
     */
    public int deletePlatformLiveByLiveId(Long liveId);

    /**
     * 根据创建者查询直播
     * 
     * @param createBy
     * @return 直播管理
     */
    public List<PlatformLive> selectPlatformLiveBycreateBy(String createBy);

    /**
     * 根据创建者查询直播
     * 
     * @param updateBy
     * @return 直播管理
     */
    public List<PlatformLive> selectPlatformLiveByupdateBy(String updateBy);

    /**
     * 检查场控是否被直播使用.
     */
    public boolean isSceneconUsed(Long sceneconId);

    /**
     * 检查产品是否被直播使用.
     */
    public boolean isGoodsUsed(Long goodsId);

    /**
     * 根据直播Id打包分类下音频
     */
    public void downloadAllAudiosByLiveId(Long liveId, HttpServletResponse response, Long[] audios) throws Exception;

    //根据直播id查询信息
    public List<PlatformLive> selectLive(Long liveId);
}
