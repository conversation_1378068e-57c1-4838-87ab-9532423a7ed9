package com.ruoyi.mybatis.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(JoinTables.class)
public @interface JoinTable {

    String sourceName() default "t";

    String targetName();

    String sourceKey();

    String targetKey();

    String targetAlias();

    String type() default "left";

    int num() default 100;

}
