package com.ruoyi.platform.utils.HashrateCostUtils;

/**
 * 算力点类型枚举，用于标记不同操作所需的资源消耗方式。
 */
public enum CostType {

    /**
     * 计算类操作（如算法处理）
     */
    COMPUTE, 

    /**
     * 存储类操作
     */
    STORAGE, 

    /**
     * 网络传输类操作
     */
    NETWORK, 

    /**
     * 固定消耗算力点
     */
    FIXED,

    /**
     * 按视频时长动态计算算力点
     */
    VIDEO_DURATION,

    /**
     * 按文本长度动态计算算力点
     */
    TEXT_LENGTH,

    /**
     * 按图像大小动态计算算力点
     */
    IMAGE_SIZE
}
