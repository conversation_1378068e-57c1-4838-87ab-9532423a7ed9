package com.ruoyi.coze.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.coze.domain.CozeWorkflowLog;
import com.ruoyi.coze.mapper.CozeWorkflowLogMapper;
import com.ruoyi.coze.service.ICozeWorkflowLogService;
import com.ruoyi.coze.utils.OutputJsonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * CozeWorkflowLogService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class CozeWorkflowLogServiceImpl implements ICozeWorkflowLogService {
    @Autowired
    private CozeWorkflowLogMapper cozeWorkflowLogMapper;

    /**
     * 查询CozeWorkflowLog
     * 
     * @param id CozeWorkflowLog主键
     * @return CozeWorkflowLog
     */
    @Override
    public CozeWorkflowLog selectCozeWorkflowLogById(Long id) {
        return cozeWorkflowLogMapper.selectCozeWorkflowLogById(id);
    }

    /**
     * 查询CozeWorkflowLog列表
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return CozeWorkflowLog
     */
    @Override
    public List<CozeWorkflowLog> selectCozeWorkflowLogList(CozeWorkflowLog cozeWorkflowLog) {
        return cozeWorkflowLogMapper.selectCozeWorkflowLogList(cozeWorkflowLog);
    }

    /**
     * 新增CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    @Override
    public int insertCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog) {
        cozeWorkflowLog.setCreateTime(DateUtils.getNowDate());
        cozeWorkflowLog.setCreateBy(Long.toString(SecurityUtils.getUserId()));
        return cozeWorkflowLogMapper.insertCozeWorkflowLog(cozeWorkflowLog);
    }

    /**
     * 修改CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    @Override
    public int updateCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog) {
        cozeWorkflowLog.setUpdateTime(DateUtils.getNowDate());
        cozeWorkflowLog.setUpdateBy(Long.toString(SecurityUtils.getUserId()));
        return cozeWorkflowLogMapper.updateCozeWorkflowLog(cozeWorkflowLog);
    }

    /**
     * 批量删除CozeWorkflowLog
     * 
     * @param ids 需要删除的CozeWorkflowLog主键
     * @return 结果
     */
    @Override
    public int deleteCozeWorkflowLogByIds(Long[] ids) {
        return cozeWorkflowLogMapper.deleteCozeWorkflowLogByIds(ids);
    }

    /**
     * 删除CozeWorkflowLog信息
     * 
     * @param id CozeWorkflowLog主键
     * @return 结果
     */
    @Override
    public int deleteCozeWorkflowLogById(Long id) {
        return cozeWorkflowLogMapper.deleteCozeWorkflowLogById(id);
    }

    /**
     * 保存工作流日志
     */
    @Override
    public Long saveLogWithInput(String workflowId, String executeId, Object request) {
        String requestJson = JSON.toJSONString(request);
        CozeWorkflowLog cozeWorkflowLog = new CozeWorkflowLog();
        cozeWorkflowLog.setWorkflowId(workflowId);
        cozeWorkflowLog.setExecuteId(executeId);
        cozeWorkflowLog.setInputParam(requestJson);
        cozeWorkflowLog.setCreateTime(DateUtils.getNowDate());
        cozeWorkflowLog.setCreateBy(Long.toString(SecurityUtils.getUserId()));
        cozeWorkflowLogMapper.insertCozeWorkflowLog(cozeWorkflowLog);
        return cozeWorkflowLog.getId();
    }

    /**
     * 更新工作流日志输出
     */
    @Override
    public int saveLogWithOutput(String workflowId, String executeId, Map<String, Object> result) {
        // 对result进行处理
        String resultJson = OutputJsonUtil.parseOutputToJsonString(result);
        int rowsAffected = cozeWorkflowLogMapper.updateCozeWorkflowLogOutput(
                workflowId,
                executeId,
                resultJson,
                SecurityUtils.getUserId().toString(),
                DateUtils.getNowDate());
        if (rowsAffected == 0)
            throw new RuntimeException("未找到匹配的工作流日志");
        return rowsAffected;
    }

    /**
     * 根据ID更新工作流日志输出
     */
    @Override
    public int saveLogWithOutputById(Long id, Map<String, Object> result) {
        log.info("更新工作流日志输出, ID: {}, 结果: {}", id, result);
        // 对result进行处理
        String resultJson = OutputJsonUtil.parseOutputToJsonString(result);
        int rowsAffected = cozeWorkflowLogMapper.updateCozeWorkflowLogOutputById(
                id,
                resultJson,
                SecurityUtils.getUserId().toString(),
                DateUtils.getNowDate());
        if (rowsAffected == 0)
            throw new RuntimeException("未找到匹配的工作流日志");
        return rowsAffected;
    }

}
