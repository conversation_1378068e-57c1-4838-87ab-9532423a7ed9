package com.ruoyi.tingwu.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.tingwu.domain.TingWuRequest;
import com.ruoyi.tingwu.service.TingWuService;

import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 演示了通过OpenAPI 创建实时记录的调用方式。
 */
@Slf4j
@RestController
@RequestMapping("/tingwu")
@RequiredArgsConstructor
public class TingWuController {

    private final TingWuService tingWuService;


    /** 离线任务 */
    @PostMapping("/summitOfflineTask")
    public AjaxResult summitOfflineTask(MultipartFile files, @RequestBody TingWuRequest request) {
        try {
            log.info(request.getLanguage());
            return AjaxResult.success(tingWuService.summitOfflineTask(files,request));
        } catch (Exception e) {
            return AjaxResult.error("调用离线任务失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务编号获取任务状态和结果
     */
    @GetMapping("/getStatusAndResultByTaskId")
    public AjaxResult getStatusAndResultByTaskId(@RequestParam String taskId) {
        try {
            return AjaxResult.success(tingWuService.getStatusAndResultByTaskId(taskId));
        } catch (Exception e) {
            return AjaxResult.error("获取任务状态失败 ：" + e.getMessage());
        }
    }

}
