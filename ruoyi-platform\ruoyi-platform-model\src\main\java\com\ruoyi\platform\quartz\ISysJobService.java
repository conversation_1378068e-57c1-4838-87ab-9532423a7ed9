package com.ruoyi.platform.quartz;

import java.util.List;

import org.quartz.SchedulerException;

import com.ruoyi.common.exception.job.TaskException;
import com.ruoyi.platform.quartz.domain.SysJob;

/**
 * 定时任务调度信息信息 服务层
 * 
 * <AUTHOR>
 */
public interface ISysJobService {
    /**
     * 获取quartz调度器的计划任务列表
     */
    public List<SysJob> selectJobList(SysJob job);

    /**
     * 通过调度任务ID查询调度信息
     */
    public SysJob selectJobById(Long jobId);

    /**
     * 暂停任务
     */
    public int pauseJob(SysJob job) throws SchedulerException;

    /**
     * 恢复任务
     */
    public int resumeJob(SysJob job) throws SchedulerException;

    /**
     * 删除任务
     */
    public int deleteJob(SysJob job) throws SchedulerException;

    /**
     * 批量删除调度信息
     */
    public void deleteJobByIds(Long[] jobIds) throws SchedulerException;

    /**
     * 任务调度状态修改
     */
    public int changeStatus(SysJob job) throws SchedulerException;

    /**
     * 立即运行任务
     */
    public boolean run(SysJob job) throws SchedulerException;

    /**
     * 新增任务
     */
    public int insertJob(SysJob job) throws SchedulerException, TaskException;

    /**
     * 更新任务
     */
    public int updateJob(SysJob job) throws SchedulerException, TaskException;

    /**
     * 校验cron表达式是否有效
     */
    public boolean checkCronExpressionIsValid(String cronExpression);
}
