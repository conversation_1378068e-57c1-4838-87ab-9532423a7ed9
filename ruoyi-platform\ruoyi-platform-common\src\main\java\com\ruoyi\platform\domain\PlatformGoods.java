package com.ruoyi.platform.domain;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.GetMapping;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 产品管理对象 platform_goods
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@Schema(description = "产品管理对象")
public class PlatformGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long goodsId;

    /** 项目Id */
    @Schema(title = "项目Id")
    @Excel(name = "项目Id")
    @NotNull(message = "项目Id不能为空",groups = { GetMapping.class })
    private Long projectId;

    /** 产品名称 */
    @Schema(title = "产品名称")
    @Excel(name = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    private String goodsName;

    /** 产品互动分类Ids */
    @Schema(title = "产品互动分类Ids")
    @Excel(name = "产品互动分类Ids")
    private List<Long> goodsInteractionId;

    /** 产品问答分类Ids */
    @Schema(title = "产品问答分类Ids")
    @Excel(name = "产品问答分类Ids")
    private List<Long> goodsQuestionsId;
    public void setGoodsId(Long goodsId) 
    {
        this.goodsId = goodsId;
    }

    public Long getGoodsId() 
    {
        return goodsId;
    }


    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }


    public void setGoodsName(String goodsName) 
    {
        this.goodsName = goodsName;
    }

    public String getGoodsName() 
    {
        return goodsName;
    }


    public void setGoodsInteractionId(List<Long> goodsInteractionId) 
    {
        this.goodsInteractionId = goodsInteractionId;
    }

    public List<Long> getGoodsInteractionId() 
    {
        return goodsInteractionId;
    }


    public void setGoodsQuestionsId(List<Long> goodsQuestionsId) 
    {
        this.goodsQuestionsId = goodsQuestionsId;
    }

    public List<Long> getGoodsQuestionsId() 
    {
        return goodsQuestionsId;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("goodsId", getGoodsId())
            .append("projectId", getProjectId())
            .append("goodsName", getGoodsName())
            .append("goodsInteractionId", getGoodsInteractionId())
            .append("goodsQuestionsId", getGoodsQuestionsId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
