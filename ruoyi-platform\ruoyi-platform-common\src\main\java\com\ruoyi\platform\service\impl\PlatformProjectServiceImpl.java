package com.ruoyi.platform.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformCategory;
import com.ruoyi.platform.domain.PlatformProject;
import com.ruoyi.platform.mapper.PlatformAnchorMapper;
import com.ruoyi.platform.mapper.PlatformArticleMapper;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformCategoryMapper;
import com.ruoyi.platform.mapper.PlatformLiveMapper;
import com.ruoyi.platform.mapper.PlatformProjectMapper;
import com.ruoyi.platform.service.IPlatformProjectService;

/**
 * 项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Service
public class PlatformProjectServiceImpl implements IPlatformProjectService {

    //记录日志
    private static final Logger logger = LoggerFactory.getLogger(PlatformProjectServiceImpl.class);
    
    @Autowired
    private PlatformProjectMapper platformProjectMapper;

    @Autowired
    private PlatformLiveMapper platformLiveMapper;

    @Autowired
    private PlatformCategoryMapper platformCategoryMapper;

    @Autowired
    private PlatformArticleMapper platformArticleMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired 
    private PlatformAnchorMapper platformAnchorMapper;

    /**
     * 查询项目
     * 
     * @param projectId 项目主键
     * @return 项目
     */
    @Override
    public PlatformProject selectPlatformProjectByProjectId(Long projectId) {
        return platformProjectMapper.selectPlatformProjectByProjectId(projectId);
    }

    /**
     * 查询项目列表
     * 
     * @param platformProject 项目
     * @return 项目
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformProject> selectPlatformProjectList(PlatformProject platformProject) {
        platformProject.setCreateBy(SecurityUtils.getUsername());
        return platformProjectMapper.selectPlatformProjectList(platformProject);
    }

    /**
     * 新增项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    @Override
    public int insertPlatformProject(PlatformProject platformProject) {
        platformProject.setCreateTime(DateUtils.getNowDate());
        platformProject.setUpdateTime(DateUtils.getNowDate());
        return platformProjectMapper.insertPlatformProject(platformProject);
    }

    /**
     * 修改项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    @Override
    public int updatePlatformProject(PlatformProject platformProject) {
        //更新操作时，设置更新人
        platformProject.setUpdateBy(SecurityUtils.getUsername());
        platformProject.setUpdateTime(DateUtils.getNowDate());
        return platformProjectMapper.updatePlatformProject(platformProject);
    }


    /**
     * 删除项目信息
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    @Override
    public int deletePlatformProjectByProjectId(Long projectId) {
        if (checkProjectHasCategory(projectId))
            return -1;
        return platformProjectMapper.deletePlatformProjectByProjectId(projectId);
    }

    /**
     * 检查是否还有分类
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    public boolean checkProjectHasCategory(Long projectId) {
        return platformProjectMapper.checkProjectHasCategory(projectId) > 0;
    }

    //强制删除项目及下面所有的关联数据
    @Transactional
    @Override
    public boolean deleteConstraintProjectId(Long projectId) throws Exception {
        PlatformProject project = platformProjectMapper.selectPlatformProjectByProjectId(projectId);
        if (project == null) {
            throw new ServiceException("未找到要删除的项目！");
        }
        //查询项目下的分类
        List<PlatformCategory> categories = platformCategoryMapper.selectCategoryListByProjectId(projectId); 
        // 删除分类及其关联资源
        if (categories != null && !categories.isEmpty()) {
            for (PlatformCategory category : categories) {
                deleteCategory(category.getCategoryId());
            }
        }
        // 删除与项目相关的直播和智能主播数据
        platformLiveMapper.deleteConstraintLiveProjectId(projectId);
        platformAnchorMapper.deleteConstraintAnchorProjectId(projectId);
        // 最后删除当前项目
        int deletedRows = platformProjectMapper.deletePlatformProjectByProjectId(projectId);
        return deletedRows > 0;
    }

    private void deleteCategory(Long categoryId) throws Exception {
        // 删除与分类相关的音频数据
        List<PlatformAudio> audioList = platformAudioMapper.selectConstraintAudioCategoryId(categoryId);
        if (audioList != null && !audioList.isEmpty()) {
            for (PlatformAudio audio : audioList) {
                String fileUrl = audio.getAudioPath();
                if (StringUtils.isNotEmpty(fileUrl)) {
                    boolean deleteSuccess = FileOperateUtils.deleteFile(fileUrl);
                    if (!deleteSuccess) {
                        logger.error("删除 oss 文件失败，路径: {}", fileUrl);
                    } else {
                        logger.info("删除oss文件成功: {}", fileUrl);
                    }
                } else {
                    logger.warn("音频路径为空，无法删除 oss 文件。");
                }
            }
            platformAudioMapper.deleteConstraintAudioCategoryId(categoryId);
        }
        // 删除与分类相关的文案和关键词数据
        platformArticleMapper.deleteConstraintArticleAndKeywordCategoryId(categoryId);
        // 删除分类
        platformCategoryMapper.deletePlatformCategoryByCategoryId(categoryId);
    }

    //根据直播Id获取项目信息
    @Override
    public PlatformProject getProjectByLiveId(Long liveId){
        return platformProjectMapper.selectProjectByLiveId(liveId);
    }
}
