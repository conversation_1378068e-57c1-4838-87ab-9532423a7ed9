package com.ruoyi.video.service.impl;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.video.common.DtoToMapUtil;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.dto.MediaDTO;
import com.ruoyi.video.service.IMediaService;
import com.ruoyi.video.utils.MediaUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能媒体服务(ICE)媒资管理 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class MediaServiceImpl implements IMediaService {

    @Autowired
    private IAcsClient acsClient;

    @Autowired
    private IceClientAKConfig iceConfig;

    @Value("${oss.bucket-name:szb-pc}")
    private String ossBucketName;
    
    // 服务版本常量
    private static final String API_VERSION = "2020-11-09";

    @Override
    public String getMediaInfo(String mediaId, String inputURL, String outputType, String returnDetailedInfo) throws Exception {
        validateAcsClient();
        if ((mediaId == null || mediaId.trim().isEmpty()) && (inputURL == null || inputURL.trim().isEmpty())) {
            throw new ServiceException("参数错误，MediaId 和 InputURL 不能同时为空。");
        }
        
        CommonRequest request = createCommonRequest("GetMediaInfo");
        addParameterIfNotNull(request, "MediaId", mediaId);
        addParameterIfNotNull(request, "InputURL", inputURL);
        addParameterIfNotNull(request, "OutputType", outputType);
        addParameterIfNotNull(request, "ReturnDetailedInfo", returnDetailedInfo);
        
        return executeRequest(request);
    }

    @Override
    public String batchGetMediaInfos(String mediaIds, String additionType) throws Exception {
        validateAcsClient();
        if (mediaIds == null || mediaIds.trim().isEmpty()) {
            throw new ServiceException("参数错误，MediaIds 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("BatchGetMediaInfos");
        request.putQueryParameter("MediaIds", mediaIds);
        addParameterIfNotNull(request, "AdditionType", additionType);
        
        try {
            return executeRequest(request);
        } catch (ClientException e) {
            log.error("批量获取媒资信息失败，错误码: {}, 错误信息: {}", e.getErrCode(), e.getErrMsg());
            if ("InvalidParameter".equals(e.getErrCode())) {
                throw new Exception("参数无效，请检查媒资ID格式是否正确: " + mediaIds);
            }
            throw new Exception("批量获取媒资信息失败: " + e.getErrMsg() + " (错误码: " + e.getErrCode() + ")");
        }
    }

    @Override
    public String getEditingProjectMaterials(String projectId) throws Exception {
        validateAcsClient();
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new ServiceException("要查询的工程ID (ProjectId) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("GetEditingProjectMaterials");
        request.putQueryParameter("ProjectId", projectId);
        
        try {
            return executeRequest(request);
        } catch (ClientException e) {
            log.error("获取剪辑工程关联素材失败，错误码: {}, 错误信息: {}", e.getErrCode(), e.getErrMsg());
            if ("ProjectNotFound".equals(e.getErrCode())) {
                throw new Exception("指定的云剪辑工程不存在，请检查工程ID是否正确: " + projectId);
            } else if ("InvalidParameter".equals(e.getErrCode())) {
                throw new Exception("参数无效，请检查工程ID格式: " + projectId);
            }
            throw new Exception("获取剪辑工程关联素材失败: " + e.getErrMsg() + " (错误码: " + e.getErrCode() + ")");
        }
    }

    @Override
    public String addEditingProjectMaterials(String projectId, String materialMaps) throws Exception {
        validateAcsClient();
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new ServiceException("工程ID (ProjectId) 不能为空。");
        }
        if (materialMaps == null || materialMaps.trim().isEmpty()) {
            throw new ServiceException("素材映射 (MaterialMaps) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("AddEditingProjectMaterials");
        request.putQueryParameter("ProjectId", projectId);
        request.putQueryParameter("MaterialMaps", materialMaps);
        
        return executeRequest(request);
    }

    @Override
    public String registerMediaInfo(MediaDTO requestDTO) throws Exception {
        validateAcsClient();
        if (requestDTO == null) {
            throw new ServiceException("媒资注册请求数据不能为空。");
        }
        
        CommonRequest request = createCommonRequest("RegisterMediaInfo");
        Map<String, Object> params = DtoToMapUtil.convertDtoToMap(requestDTO);
        params.forEach((key, value) -> addParameterIfNotNull(request, key, value));
        
        return executeRequest(request);
    }

    @Override
    public Object listMediaBasicInfo(MediaDTO dto) throws Exception {
        validateAcsClient();
        if (dto == null) {
            throw new ServiceException("媒资查询参数不能为空。");
        }
        
        CommonRequest request = createCommonRequest("ListMediaBasicInfos");
        Map<String, Object> params = DtoToMapUtil.convertDtoToMap(dto);
        params.forEach((key, value) -> addParameterIfNotNull(request, key, value));
        
        String data = executeRequest(request);
        return JSON.parse(data);
    }
    
    /**
     * 构建媒资注册请求对象
     */
    private MediaDTO buildMediaRegisterRequest(MultipartFile file, String ossUrl) {
        MediaDTO request = new MediaDTO();
        request.setInputURL(ossUrl);
        
        String ext = MediaUtils.getFileExtension(file.getOriginalFilename());
        String mediaType = MediaUtils.determineFileType(ext);
        
        request.setMediaType(mediaType);
        request.setTitle(file.getOriginalFilename());
        request.setBussinessType("general");
        
        MediaDTO.FileInfoDTO fileInfo = new MediaDTO.FileInfoDTO();
        fileInfo.setName(file.getOriginalFilename());
        fileInfo.setExt(ext);
        fileInfo.setType(mediaType);
        request.setFileInfo(fileInfo);
        
        return request;
    }
    
    /**
     * 从上传后的URL中提取实际文件路径
     */
    private String extractFilePathFromUrl(String uploadedUrl) {
        if (uploadedUrl == null || uploadedUrl.isEmpty()) {
            return "";
        }
        
        try {
            if (uploadedUrl.startsWith("http")) {
                int firstSlashAfterDomain = uploadedUrl.indexOf("/", 8);
                if (firstSlashAfterDomain != -1) {
                    String pathPart = uploadedUrl.substring(firstSlashAfterDomain + 1);
                    int questionMarkIndex = pathPart.indexOf("?");
                    if (questionMarkIndex != -1) {
                        pathPart = pathPart.substring(0, questionMarkIndex);
                    }
                    return pathPart;
                }
            }
        } catch (Exception e) {
            log.warn("提取文件路径失败，使用原始URL: {}", uploadedUrl, e);
        }
        
        return uploadedUrl;
    }
    
    /**
     * 验证AcsClient是否初始化
     */
    private void validateAcsClient() {
        if (acsClient == null) {
            throw new IllegalStateException("IAcsClient 未初始化，请检查配置中的阿里云访问密钥(AccessKey ID/Secret)和地区(Region)设置。");
        }
    }
    
    /**
     * 创建通用请求对象
     */
    private CommonRequest createCommonRequest(String action) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(iceConfig.getEndpoint());
        request.setSysVersion(API_VERSION);
        request.setSysAction(action);
        return request;
    }
    
    /**
     * 添加非空参数到请求
     */
    private void addParameterIfNotNull(CommonRequest request, String key, Object value) {
        if (value != null) {
            request.putQueryParameter(key, value.toString());
        }
    }
    
    /**
     * 执行请求并处理响应
     */
    private String executeRequest(CommonRequest request) throws Exception {
        CommonResponse response = acsClient.getCommonResponse(request);
        String data = response.getData();
        
        if (response.getHttpStatus() != 200) {
            log.error("请求失败，HTTP状态码: {}, 响应数据: {}", response.getHttpStatus(), data);
            throw new Exception("请求失败: " + data);
        }
        
        return data;
    }

    // 分片上传相关常量
    private static final long MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB

    @Override
    public Map<String, Object> initMultipartUpload(String fileName, Long fileSize, String category) throws Exception {
        if (StringUtils.isEmpty(fileName) || fileSize == null || fileSize <= 0) {
            throw new ServiceException("文件名或文件大小不能为空");
        }
        if (fileSize > MAX_FILE_SIZE) {
            throw new ServiceException("文件不能超过500MB");
        }

        // 设置默认分类
        if (category == null || category.trim().isEmpty()) {
            category = "general";
        }

        String customFilePath = MediaUtils.buildIceMediaLibraryPath(category, fileName);
        String uploadId = FileOperateUtils.initMultipartUpload(customFilePath);

        Map<String, Object> result = new HashMap<>();
        result.put("uploadId", uploadId);
        result.put("filePath", customFilePath);
        result.put("fileName", fileName);
        result.put("category", category);
        return result;
    }

    @Override
    public Map<String, Object> uploadFileChunk(String uploadId, String filePath, int chunkIndex, MultipartFile chunk) throws Exception {
        if (chunk == null || chunk.isEmpty()) {
            throw new ServiceException("分片数据不能为空");
        }

        String etag = FileOperateUtils.uploadPart(filePath, uploadId, chunkIndex + 1, chunk.getSize(), chunk.getInputStream());
        if (StringUtils.isEmpty(etag)) {
            throw new ServiceException("上传分片失败：未获取到ETag");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("etag", etag);
        result.put("partNumber", chunkIndex + 1);

        // 如果是第一个分片，计算并返回MD5值
        if (chunkIndex == 0) {
            String md5 = Md5Utils.getMd5(chunk);
            result.put("md5", md5);
        }

        return result;
    }

    @Override
    public Map<String, Object> completeMultipartUploadAndRegister(String uploadId, String filePath, Long fileSize,
            String fileName, String category, List<SysFilePartETag> partETags) throws Exception {
        if (partETags == null || partETags.isEmpty()) {
            throw new ServiceException("分片信息不能为空");
        }

        // 验证并排序分片信息
        List<SysFilePartETag> validParts = partETags.stream()
                .filter(part -> part != null && part.getPartNumber() != null && part.getETag() != null)
                .filter(part -> part.getPartNumber() > 0 && !StringUtils.isEmpty(part.getETag()))
                .sorted(Comparator.comparingInt(SysFilePartETag::getPartNumber))
                .collect(Collectors.toList());

        if (validParts.size() != partETags.size()) {
            throw new ServiceException("分片信息格式不正确");
        }

        String finalPath = FileOperateUtils.completeMultipartUpload(filePath, uploadId, validParts);
        if (StringUtils.isEmpty(finalPath)) {
            throw new ServiceException("合并分片失败：未获取到最终文件路径");
        }

        // 构建OSS URL用于媒资注册
        String actualFilePath = extractFilePathFromUrl(finalPath);
        String ossUrl = MediaUtils.buildOssUrl(ossBucketName, actualFilePath);

        // 获取第一个分片的 MD5
        String md5 = partETags.stream()
                .map(SysFilePartETag::getMd5)
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElse(null);

        // 构建媒资注册请求
        MediaDTO registerRequest = buildMediaRegisterRequestForChunked(fileName, fileSize, ossUrl, category);
        String result = registerMediaInfo(registerRequest);

        // 构建返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("mediaInfo", JSON.parse(result));
        response.put("ossUrl", ossUrl);
        response.put("filePath", actualFilePath);
        response.put("uploadedPath", finalPath);
        response.put("fileName", fileName);
        response.put("fileSize", fileSize);
        response.put("category", category);
        response.put("md5", md5);

        return response;
    }

    /**
     * 为分片上传构建媒资注册请求对象
     */
    private MediaDTO buildMediaRegisterRequestForChunked(String fileName, Long fileSize, String ossUrl, String category) {
        MediaDTO request = new MediaDTO();
        request.setInputURL(ossUrl);

        String ext = MediaUtils.getFileExtension(fileName);
        String mediaType = MediaUtils.determineFileType(ext);

        request.setMediaType(mediaType);
        request.setTitle(fileName);
        request.setBussinessType(category);

        MediaDTO.FileInfoDTO fileInfo = new MediaDTO.FileInfoDTO();
        fileInfo.setName(fileName);
        fileInfo.setExt(ext);
        fileInfo.setType(mediaType);
        fileInfo.setSize(fileSize);
        request.setFileInfo(fileInfo);

        return request;
    }
}