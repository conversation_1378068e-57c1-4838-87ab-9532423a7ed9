package com.ruoyi.tingwu.utils;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;

public class RequestUtils {
    /**
     * 创建通用请求对象
     *
     * @param domain       服务域名
     * @param version      API版本
     * @param protocolType 协议类型
     * @param method       HTTP方法
     * @param uri          请求路径
     * @return 配置好的CommonRequest对象
     */
    public static CommonRequest createCommonRequest(String domain, String version, ProtocolType protocolType,
            MethodType method, String uri) {
        CommonRequest request = new CommonRequest();
        request.setSysDomain(domain);
        request.setSysVersion(version);
        request.setSysProtocol(protocolType);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    /**
     * 创建热词请求
     *
     * @return
     */
    public static CommonRequest createPhraseRequest() {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.POST,
                "/openapi/tingwu/v2/resources/phrases");
    }

    /**
     * 列举热词列表
     *
     * @return
     */
    public static CommonRequest listPhraseRequest() {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.GET,
                "/openapi/tingwu/v2/resources/phrases");
    }

    /**
     * 查询热词详情
     */
    public static CommonRequest phraseDetailRequest(String phraseId) {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS,
                MethodType.GET, String.format("/openapi/tingwu/v2/resources/phrases/%s", phraseId));
    }

    /**
     * 更新热词
     */
    public static CommonRequest updatePhraseRequest(String phraseId) {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS,
                MethodType.PUT, String.format("/openapi/tingwu/v2/resources/phrases/%s", phraseId));
    }

    /**
     * 删除热词
     */
    public static CommonRequest delPhraseRequest(String phraseId) {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS,
                MethodType.DELETE, String.format("/openapi/tingwu/v2/resources/phrases/%s", phraseId));
    }

    public static CommonRequest createTaskRequest() {
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.PUT,
                "/openapi/tingwu/v2/tasks");
    }

    public static CommonRequest createResultRequest(String taskId) {
        String queryUrl = String.format("/openapi/tingwu/v2/tasks" + "/%s", taskId);
        return createCommonRequest("tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.GET,
                queryUrl);
    }

}
