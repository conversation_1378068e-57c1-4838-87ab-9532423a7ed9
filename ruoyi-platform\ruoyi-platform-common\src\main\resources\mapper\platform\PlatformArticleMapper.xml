<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformArticleMapper">

    <resultMap type="PlatformArticle" id="PlatformArticleResult">
        <result property="articleId" column="article_id" />
        <result property="categoryId" column="category_id" />
        <result property="content" column="content" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectPlatformArticleVo">
        select article_id,category_id, content, create_by, create_time, update_by, update_time, remark from platform_article 
    </sql>

    <!-- 强制删除关联的文案和关键词数据 -->
    <delete id="deleteConstraintArticleAndKeywordCategoryId" parameterType="Long">
        DELETE platform_article, platform_keyword FROM platform_article, platform_keyword 
        WHERE platform_article.category_id = #{categoryId} AND platform_keyword.category_id = #{categoryId}
    </delete>

    <!-- 导出文案数据 -->
    <select id="selectByCategoryIdList" parameterType="java.util.List">
        SELECT * FROM platform_article
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            WHERE category_id IN
            <foreach item="item" index="index" collection="categoryIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 批量新增文案 -->
    <insert id="batchInsertPlatformArticle" parameterType="java.util.List">
        INSERT INTO platform_article (category_id, content, create_by, create_time, update_by, update_time, remark) VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.categoryId}, #{item.content}, #{item.createBy}, NOW(), #{item.updateBy}, NOW(), #{item.remark})
            </foreach>
    </insert>

    <insert id="insertArticle" parameterType="PlatformArticle" useGeneratedKeys="true" keyProperty="articleId">
        INSERT INTO platform_article (category_id, content, create_by, create_time, update_by, update_time)
        VALUES (#{categoryId}, #{content}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <!-- 根据文案Ids查询文案信息 -->
    <select id="getArticleIds" parameterType="java.util.List" resultMap="PlatformArticleResult">
        select article_id,content from platform_article where article_id in
        <foreach item='id' index='index' collection='articleIds' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </select>

    <!-- 根据直播id查询文案信息 -->
    <select id="getArticlesByLiveId" parameterType="Long" resultMap="PlatformArticleResult">
        SELECT DISTINCT a.* FROM platform_live l LEFT JOIN platform_scenecon s ON l.scenecon_id = s.scenecon_id
        LEFT JOIN platform_goods g ON l.goods_id = g.goods_id JOIN platform_article a ON 
            (s.scenecon_interaction_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(s.scenecon_interaction_id,'[',''),']','')) > 0)
            OR (s.scenecon_questions_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(s.scenecon_questions_id,'[',''),']','')) > 0)
            OR (g.goods_interaction_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(g.goods_interaction_id,'[',''),']','')) > 0)
            OR (g.goods_questions_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(g.goods_questions_id,'[',''),']','')) > 0)
        WHERE l.live_id = #{liveId}
    </select>
</mapper>