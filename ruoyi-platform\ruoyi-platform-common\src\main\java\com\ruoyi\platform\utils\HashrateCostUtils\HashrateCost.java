package com.ruoyi.platform.utils.HashrateCostUtils;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义算力点花费记录注解
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HashrateCost
{
    /**
     * 消耗算力点数的操作描述
     */
    String description() default "";

    /**
     * 预计消耗的算力点数
     */
    int expectedPoints() default 0;

    /**
     * 是否立即扣除算力点数
     */
    boolean immediateDeduction() default true;

    /**
     * 操作类型
     */
    CostType costType() default CostType.COMPUTE;

    /**
     * 指定扣除算力点数的用户ID，默认为空，意味着从当前操作者扣除
     */
    String userId() default "";
    
    /**
     * 是否为动态计费（例如根据视频时长）
     */
    boolean dynamicCost() default false;
    
    /**
     * 动态计费的单位费用（如每分钟的算力点数）
     */
    int unitCost() default 0;
    
    /**
     * 动态计费的计算方法
     */
    CostType calculationType() default CostType.FIXED;
    
    /**
     * 是否仅基于结果视频计算费用
     * 当设为true时，将只计算结果视频的时长
     */
    boolean resultVideoOnly() default false;
}
