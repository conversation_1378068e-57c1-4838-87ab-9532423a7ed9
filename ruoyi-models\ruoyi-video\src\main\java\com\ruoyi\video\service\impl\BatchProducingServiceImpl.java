package com.ruoyi.video.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.service.IBatchProducingService;

import lombok.extern.slf4j.Slf4j;

/**
 * 批量智能一键成片服务实现类
 * <p>
 * 实现阿里云ICE批量智能一键成片相关的业务逻辑。
 * 提供任务提交、状态查询、列表获取等功能的具体实现。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Slf4j
@Service
public class BatchProducingServiceImpl implements IBatchProducingService {

    @Autowired
    private IAcsClient acsClient;

    @Autowired
    private IceClientAKConfig iceConfig;
    
    public final String DEFAULT_OUTPUT_MEDIA_URL = "https://szb-pc.oss-cn-beijing.aliyuncs.com/ice/output/output_{index}.mp4";
    public final String DEFAULT_CALLBACK_URL = "http://e56d67b4.natappfree.cc/video/BatchProducing/callback";

    @Override
    public String submitBatchMediaProducingJob(String inputConfig, String editingConfig, String outputConfig,
                                              String userData, String templateConfig, String clientToken) throws Exception {

        validateNotBlank(inputConfig, "输入配置(InputConfig)");
        validateNotBlank(editingConfig, "剪辑配置(EditingConfig)");
        validateNotBlank(outputConfig, "输出配置(OutputConfig)");

        // 设置默认输出 URL
        JSONObject outputCfg = parseOutputConfig(outputConfig);
        outputConfig = outputCfg.toJSONString();

        // 设置默认回调地址
        String callbackUrl = getCallbackUrl();
        userData = buildUserData(userData, callbackUrl);

        // 构建请求
        CommonRequest request = buildCommonRequest("SubmitBatchMediaProducingJob");
        request.putQueryParameter("InputConfig", inputConfig);
        request.putQueryParameter("EditingConfig", editingConfig);
        request.putQueryParameter("OutputConfig", outputConfig);
        request.putQueryParameter("UserData", userData);

        if (templateConfig != null && !templateConfig.trim().isEmpty()) {
            request.putQueryParameter("TemplateConfig", templateConfig);
        }
        if (clientToken != null && !clientToken.trim().isEmpty()) {
            request.putQueryParameter("ClientToken", clientToken);
        }

        log.info("提交批量任务请求参数: {}", request.getSysQueryParameters());
        return executeRequest(request, "提交批量智能一键成片任务失败");
    }

    @Override
    public String getBatchMediaProducingJob(String jobId) throws Exception {
        validateNotBlank(jobId, "作业ID(JobId)");

        CommonRequest request = buildCommonRequest("GetBatchMediaProducingJob");
        request.putQueryParameter("JobId", jobId);

        log.info("查询任务详情请求参数: {}", request.getSysQueryParameters());
        return executeRequest(request, "获取批量智能一键成片任务详情失败");
    }

    @Override
    public String listBatchMediaProducingJobs(String startTime, String endTime, String status,
                                             Integer pageSize, String nextToken, String jobType) throws Exception {

        CommonRequest request = buildCommonRequest("ListBatchMediaProducingJobs");

        addIfNotBlank(request, "StartTime", startTime);
        addIfNotBlank(request, "EndTime", endTime);
        addIfNotBlank(request, "Status", status);
        if (pageSize != null && pageSize > 0) {
            request.putQueryParameter("PageSize", pageSize.toString());
        }
        addIfNotBlank(request, "NextToken", nextToken);
        addIfNotBlank(request, "JobType", jobType);

        log.info("获取任务列表请求参数: {}", request.getSysQueryParameters());
        return executeRequest(request, "获取批量智能一键成片任务列表失败");
    }

    // ------------------------- 私有方法 -------------------------

    private void validateNotBlank(String value, String name) throws ServiceException {
        if (value == null || value.trim().isEmpty()) {
            throw new ServiceException(name + "不能为空。");
        }
    }

    private JSONObject parseOutputConfig(String outputConfig) {
        JSONObject outputCfg = JSONObject.parse(outputConfig);
        if (!outputCfg.containsKey("MediaURL") || outputCfg.getString("MediaURL").isEmpty()) {
            outputCfg.put("MediaURL", DEFAULT_OUTPUT_MEDIA_URL);
        }
        return outputCfg;
    }

    private String getCallbackUrl() {
        return iceConfig.getCallback() != null && iceConfig.getCallback().getNotifyUrl() != null
                ? iceConfig.getCallback().getNotifyUrl()
                : DEFAULT_CALLBACK_URL;
    }

    private String buildUserData(String userData, String callbackUrl) {
        if (userData == null || userData.trim().isEmpty()) {
            return new JSONObject().fluentPut("NotifyAddress", callbackUrl).toJSONString();
        }
        try {
            JSONObject userDataJson = JSONObject.parse(userData);
            userDataJson.put("NotifyAddress", callbackUrl);
            return userDataJson.toJSONString();
        } catch (Exception e) {
            JSONObject userDataJson = new JSONObject();
            userDataJson.put("NotifyAddress", callbackUrl);
            userDataJson.put("originalUserData", userData);
            return userDataJson.toJSONString();
        }
    }

    private CommonRequest buildCommonRequest(String action) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysProtocol(ProtocolType.HTTPS);
        request.setSysDomain(iceConfig.getEndpoint());
        request.setSysVersion("2020-11-09");
        request.setSysAction(action);
        return request;
    }

    private void addIfNotBlank(CommonRequest request, String key, String value) {
        if (value != null && !value.trim().isEmpty()) {
            request.putQueryParameter(key, value);
        }
    }

    private String executeRequest(CommonRequest request, String errorMsg) throws Exception {
        try {
            CommonResponse response = acsClient.getCommonResponse(request);
            String data = response.getData();
            if (response.getHttpStatus() != 200) {
                log.error("{} 响应失败: {}", errorMsg, data);
                throw new Exception(errorMsg + ": " + data);
            }
            log.info("响应结果: {}", data);
            return data;
        } catch (ClientException e) {
            handleClientException(e, errorMsg);
            return null; // 不会走到这里，只是语法需要
        }
    }

    private void handleClientException(ClientException e, String errorMsg) throws Exception {
        String errorCode = e.getErrCode();
        String errorMessage = e.getErrMsg();

        if ("InvalidParameter".equals(errorCode)) {
            throw new Exception("参数无效，请检查输入配置、剪辑配置和输出配置的JSON格式是否正确");
        } else if ("QuotaExceeded".equals(errorCode)) {
            throw new Exception("任务配额已用完，请联系管理员或稍后重试");
        } else if ("JobNotFound".equals(errorCode)) {
            throw new Exception(errorMsg + "，指定的作业不存在，请检查作业ID是否正确");
        } else {
            throw new Exception(errorMsg + ": " + errorMessage + " (错误码: " + errorCode + ")");
        }
    }
}