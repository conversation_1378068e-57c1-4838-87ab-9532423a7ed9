package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformAudio;

/**
 * 音频管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface PlatformAudioMapper 
{
    /**
     * 查询音频管理
     * 
     * @param audioId 音频管理主键
     * @return 音频管理
     */
    public PlatformAudio selectPlatformAudioByAudioId(Long audioId);

    /**
     * 查询音频管理列表
     * 
     * @param platformAudio 音频管理
     * @return 音频管理集合
     */
    public List<PlatformAudio> selectPlatformAudioList(PlatformAudio platformAudio);

    public List<PlatformAudio> selectPlatformAudioListFroCategoryIds(List<Long> categoryIds);

    /**
     * 新增音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    public int insertPlatformAudio(PlatformAudio platformAudio);

    /**
     * 修改音频管理
     * 
     * @param platformAudio 音频管理
     * @return 结果
     */
    public int updatePlatformAudio(PlatformAudio platformAudio);

    /**
     * 删除音频管理
     * 
     * @param audioId 音频管理主键
     * @return 结果
     */
    public int deletePlatformAudioByAudioId(Long audioId);

    /**
     * 批量删除音频管理
     * 
     * @param audioId 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformAudioByAudioIds(Long[] audioId);
    
    //根据分类Id删除音频
    @Delete("delete from platform_audio where category_id=#{categoryId}")
    public int deleteConstraintAudioCategoryId(Long categoryId);

    //根据分类的Id去查询数据
    @Select("select * from platform_audio where category_id =#{categoryId}")
    public List<PlatformAudio> selectConstraintAudioCategoryId(Long categoryId);

    //根据直播id查询音频信息
    public List<PlatformAudio> getAudiosByLiveId(Long liveId);

    // 根据分类ID查询文本转音频数据
    public List<PlatformAudio> audioAndText(PlatformAudio platformAudio);

    @Select("SELECT * FROM platform_audio WHERE audio_path=#{audioPath}")
    public PlatformAudio getAudioDetailByAddress(String audioPath);

    @Select("SELECT * FROM platform_audio WHERE audio_md5=#{audioMd5}")
    public PlatformAudio getAudioMd5(String audioMd5);
}
