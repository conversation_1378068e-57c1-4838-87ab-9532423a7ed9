<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformAudioMapper">

    <resultMap type="PlatformAudio" id="PlatformAudioResult">
        <result property="audioId" column="audio_id" />
        <result property="categoryId" column="category_id" />
        <result property="soundId" column="sound_id" />
        <result property="articleId" column="article_id" />
        <result property="audioPath" column="audio_path" />
        <result property="audioFrom" column="audio_from" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="audioName" column="audio_name" />
        <result property="audioMd5" column="audio_md5" />
    </resultMap>

    <sql id="selectPlatformAudioVo">
        select a.audio_id, a.category_id, a.sound_id, a.article_id, a.audio_path, a.audio_from, a.create_by, a.create_time, a.update_by,a.audio_md5,
        a.update_time, a.remark, a.audio_name, p.content from platform_audio a left join platform_article p on a.article_id=p.article_id 
        left join sys_user u on u.user_name = a.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformAudioList" parameterType="PlatformAudio" resultMap="PlatformAudioResult">
        <include refid="selectPlatformAudioVo"/>
        <where>
            <if test="categoryId != null "> and a.category_id = #{categoryId}</if>
            <if test="soundId != null "> and sound_id = #{soundId}</if>
            <if test="articleId != null "> and a.article_id = #{articleId}</if>
            <if test="audioPath != null  and audioPath != ''"> and audio_path = #{audioPath}</if>
            <if test="audioFrom != null  and audioFrom != ''"> and audio_from = #{audioFrom}</if>
            <if test="audioName != null  and audioName != ''"> and audio_name like concat('%', #{audioName}, '%')</if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectPlatformAudioListFroCategoryIds" parameterType="PlatformAudio" resultMap="PlatformAudioResult">
        select audio_id,category_id from platform_audio
        <where>
            <if test="categoryIds != null and categoryIds.size()>0">
                AND category_id IN
                <foreach item="item" index="index" collection="categoryIds" open="(" close=")" separator=",">  
                  #{item}   
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectPlatformAudioByAudioId" parameterType="Long" resultMap="PlatformAudioResult">
        <include refid="selectPlatformAudioVo"/>
        where audio_id = #{audioId}
    </select>

    <insert id="insertPlatformAudio" parameterType="PlatformAudio" useGeneratedKeys="true" keyProperty="audioId">
        insert into platform_audio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="soundId != null">sound_id,</if>
            <if test="articleId != null">article_id,</if>
            <if test="audioPath != null">audio_path,</if>
            <if test="audioFrom != null">audio_from,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="audioName != null">audio_name,</if>
            <if test="audioMd5 != null">audio_md5,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="soundId != null">#{soundId},</if>
            <if test="articleId != null">#{articleId},</if>
            <if test="audioPath != null">#{audioPath},</if>
            <if test="audioFrom != null">#{audioFrom},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="audioName != null">#{audioName},</if>
            <if test="audioMd5 != null">#{audioMd5},</if>
        </trim>
    </insert>

    <update id="updatePlatformAudio" parameterType="PlatformAudio">
        update platform_audio
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="soundId != null">sound_id = #{soundId},</if>
            <if test="articleId != null">article_id = #{articleId},</if>
            <if test="audioPath != null">audio_path = #{audioPath},</if>
            <if test="audioFrom != null">audio_from = #{audioFrom},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="audioName != null">audio_name = #{audioName},</if>
            <if test="audioMd5 != null">audio_md5 = #{audioMd5},</if>
        </trim>
        where audio_id = #{audioId}
    </update>

    <delete id="deletePlatformAudioByAudioId" parameterType="Long">
        delete from platform_audio where audio_id = #{audioId}
    </delete>

    <delete id="deletePlatformAudioByAudioIds" parameterType="String">
        delete from platform_audio where audio_id in 
        <foreach item="audioId" collection="array" open="(" separator="," close=")">
            #{audioId}
        </foreach>
    </delete>

    <select id="getAudiosByLiveId" parameterType="Long" resultMap="PlatformAudioResult">
        SELECT DISTINCT a.* FROM platform_live l LEFT JOIN platform_scenecon s ON l.scenecon_id = s.scenecon_id
        LEFT JOIN platform_goods g ON l.goods_id = g.goods_id JOIN platform_audio a ON 
            (s.scenecon_interaction_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(s.scenecon_interaction_id,'[',''),']','')) > 0)
            OR (s.scenecon_questions_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(s.scenecon_questions_id,'[',''),']','')) > 0)
            OR (g.goods_interaction_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(g.goods_interaction_id,'[',''),']','')) > 0)
            OR (g.goods_questions_id IS NOT NULL AND FIND_IN_SET(a.category_id, REPLACE(REPLACE(g.goods_questions_id,'[',''),']','')) > 0)
        WHERE l.live_id = #{liveId}
    </select>

    <select id="audioAndText" parameterType="PlatformAudio" resultMap="PlatformAudioResult">
        <include refid="selectPlatformAudioVo"/>
        <where>
            a.category_id IS NULL
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>
</mapper>