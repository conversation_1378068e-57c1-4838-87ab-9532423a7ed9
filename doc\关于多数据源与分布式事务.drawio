<mxfile host="65bd71144e">
    <diagram id="27K8aLRXgDd5Ry5UGMpc" name="第 1 页">
        <mxGraphModel dx="2298" dy="760" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="数据源1" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="510" y="340" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=none;html=1;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="数据源2" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="340" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=none;html=1;" parent="1" source="4" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="571.4814814814818" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" style="edgeStyle=none;html=1;" parent="1" source="4" target="43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;DataSource1&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="325" y="340" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" style="edgeStyle=none;html=1;" parent="1" source="5" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="556.25" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;" parent="1" source="5" target="43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;DataSource2&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="340" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="edgeStyle=none;html=1;" parent="1" source="11" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="636.25" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;&lt;div style=&quot;line-height: 19px;&quot;&gt;DynamicDataSourceContextHolder&lt;/div&gt;&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="460" y="460" width="270" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;&lt;h1 style=&quot;box-sizing: border-box; outline: 0px; margin: 0px; padding: 0px; font-family: &amp;quot;PingFang SC&amp;quot;, &amp;quot;Microsoft YaHei&amp;quot;, SimHei, Arial, SimSun; font-size: 28px; overflow-wrap: break-word; color: rgb(34, 34, 38); word-break: break-all; font-variant-ligatures: common-ligatures; text-align: start;&quot; id=&quot;articleContentId&quot; class=&quot;title-article&quot;&gt;Spring AbstractRoutingDataSource&lt;br&gt;&lt;br&gt;&lt;/h1&gt;&lt;div&gt;* determineCurrentLookupKey&lt;/div&gt;&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="-380" y="455" width="520" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=none;html=1;" parent="1" source="16" target="18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="方法1" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="880" y="340" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="edgeStyle=none;html=1;" parent="1" source="17" target="18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="方法2" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="880" y="580" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="" style="edgeStyle=none;html=1;" parent="1" source="18" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;DataSourceAspect&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="790" y="460" width="290" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=none;html=1;" parent="1" source="23" target="26" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="数据源1" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="580" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="" style="edgeStyle=none;html=1;" parent="1" source="25" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="数据源2" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="510" y="580" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="29" style="edgeStyle=none;html=1;" parent="1" source="26" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" style="edgeStyle=none;html=1;" parent="1" source="26" target="47" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;&lt;div style=&quot;line-height: 19px;&quot;&gt;AtomikosDataSourceBean1&lt;/div&gt;&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="60" y="580" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=none;html=1;" parent="1" source="27" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="49" style="edgeStyle=none;html=1;" parent="1" source="27" target="48" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;&lt;div style=&quot;line-height: 19px;&quot;&gt;AtomikosDataSourceBean2&lt;/div&gt;&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="285" y="580" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="59" style="edgeStyle=none;html=1;" parent="1" source="28" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;DynamicDataSource&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="170" y="455" width="230" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="edgeStyle=none;html=1;" parent="1" source="43" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;sqlSessionFactory&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="210" width="210" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="51" style="edgeStyle=none;html=1;" parent="1" source="47" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;SqlSessionFactory1&lt;/div&gt;" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="55" y="670" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="52" style="edgeStyle=none;html=1;" parent="1" source="48" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;SqlSessionFactory2&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="670" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="56" style="edgeStyle=none;html=1;" parent="1" source="50" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-593.9655172413791" y="670" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-470" y="815"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;DynamicSqlSessionTemplate&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="790" width="210" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=none;html=1;" parent="1" source="53" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="&lt;div style=&quot;background-color: rgb(255, 255, 255); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px;&quot;&gt;&lt;div style=&quot;line-height: 19px;&quot;&gt;SqlSessionTemplate&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-573" y="210" width="210" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="mybatis" style="html=1;" parent="1" vertex="1">
                    <mxGeometry x="-523" y="465" width="110" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>