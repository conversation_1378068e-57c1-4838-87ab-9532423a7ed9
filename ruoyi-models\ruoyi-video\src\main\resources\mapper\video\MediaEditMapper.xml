<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.video.mapper.MediaEditMapper">

    <resultMap type="MediaEdit" id="MediaEditResult">
        <result property="jobId" column="job_id" />
        <result property="title" column="title" />
        <result property="projectId" column="project_id" />
        <result property="status" column="status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="description" column="description" />
        <result property="coverUrl" column="cover_url" />
        <result property="mediaUrl" column="media_url" />
    </resultMap>

    <sql id="selectMediaEditVo">
        select
            mediaEdit.job_id,
            mediaEdit.title,
            mediaEdit.project_id,
            mediaEdit.status,
            mediaEdit.cover_url,
            mediaEdit.media_url,
            mediaEdit.create_by,
            mediaEdit.create_time,
            mediaEdit.update_by,
            mediaEdit.update_time,
            mediaEdit.remark,
            mediaEdit.description
        from media_edit mediaEdit  left join sys_user u on u.user_name = mediaEdit.create_by
        left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectMediaEditList" parameterType="MediaEdit" resultMap="MediaEditResult">
        <include refid="selectMediaEditVo"/>
        <where>
            <if test="title != null  and title != ''"> and mediaEdit.title = #{title}</if>
            <if test="projectId != null  and projectId != ''"> and mediaEdit.project_id = #{projectId}</if>
            <if test="status != null  and status != ''"> and mediaEdit.status = #{status}</if>
            <if test="description != null  and description != ''"> and mediaEdit.description = #{description}</if>
            <if test="startTime != null and startTime != ''"> and mediaEdit.create_time &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and mediaEdit.create_time &lt;= #{endTime}</if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectMediaEditByJobId" parameterType="String" resultMap="MediaEditResult">
        <include refid="selectMediaEditVo"/>
        where mediaEdit.job_id = #{jobId}
    </select>

    <insert id="insertMediaEdit" parameterType="MediaEdit">
        insert into media_edit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">job_id,</if>
            <if test="title != null">title,</if>
            <if test="projectId != null">project_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="description != null">description,</if>
            <if test="coverUrl != null">cover_url,</if>
            <if test="mediaUrl != null">media_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">#{jobId},</if>
            <if test="title != null">#{title},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="description != null">#{description},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="mediaUrl != null">#{mediaUrl},</if>
        </trim>
    </insert>

    <update id="updateMediaEdit" parameterType="MediaEdit">
        update media_edit
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="description != null">description = #{description},</if>
            <if test="coverUrl != null">cover_url = #{coverUrl},</if>
            <if test="mediaUrl != null">media_url = #{mediaUrl},</if>
        </trim>
        where media_edit.job_id = #{jobId}
    </update>

    <delete id="deleteMediaEditByJobId" parameterType="String">
        delete from media_edit where job_id = #{jobId}
    </delete>

    <delete id="deleteMediaEditByJobIds" parameterType="String">
        delete from media_edit where job_id in
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>

    <delete id="deleteMediaEditByProjectIds" parameterType="String">
        delete from media_edit where project_id in
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>
</mapper>