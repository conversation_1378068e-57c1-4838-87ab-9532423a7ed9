package com.ruoyi.video.service;

/**
 * 批量智能一键成片服务接口
 * <p>
 * 提供阿里云ICE批量智能一键成片相关的业务逻辑处理。
 * 包括任务提交、状态查询、列表获取等功能。
 * </p>
 * 
 * <h3>支持的成片类型：</h3>
 * <ul>
 * <li>脚本化自动成片：基于预设脚本模板进行视频生成</li>
 * <li>智能图文匹配成片：通用场景版、影视集锦版</li>
 * <li>体育赛事集锦成片：针对体育内容的专用模板</li>
 * <li>高燃混剪成片：适合快节奏内容的剪辑模式</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IBatchProducingService {

    /**
     * 提交批量智能一键成片任务
     * <p>
     * 调用阿里云ICE的SubmitBatchMediaProducingJob接口，提交批量成片任务。
     * 支持多种成片解决方案，通过参数配置区分不同的成片类型。
     * </p>
     *
     * @param inputConfig 输入配置，JSON格式字符串，包含媒体组、标题、语音文本等
     * @param editingConfig 剪辑相关配置，JSON格式字符串，包含音量、背景音乐等设置
     * @param outputConfig 输出配置，JSON格式字符串，包含输出URL、数量、时长等参数
     * @param userData 用户业务配置、回调配置，JSON格式字符串（可选）
     * @param templateConfig 模板配置，JSON格式字符串（可选）
     * @param clientToken 客户端幂等性Token（可选）
     * @return 返回阿里云响应的原始JSON字符串，包含作业ID等信息
     * @throws Exception 当API调用失败或参数无效时抛出异常
     */
    String submitBatchMediaProducingJob(String inputConfig, String editingConfig, String outputConfig,
                                      String userData, String templateConfig, String clientToken) throws Exception;

    /**
     * 获取批量智能一键成片任务详情
     * <p>
     * 调用阿里云ICE的GetBatchMediaProducingJob接口，获取指定任务的详细信息。
     * 包括任务状态、合成进度、生成的媒资ID及URL等。
     * </p>
     *
     * @param jobId 批量智能一键成片作业ID
     * @return 返回阿里云响应的原始JSON字符串，包含任务详细信息
     * @throws Exception 当API调用失败或任务不存在时抛出异常
     */
    String getBatchMediaProducingJob(String jobId) throws Exception;

    /**
     * 获取批量智能一键成片任务列表
     * <p>
     * 调用阿里云ICE的ListBatchMediaProducingJob接口，获取符合条件的任务列表。
     * 支持按时间范围、状态等条件筛选，支持分页查询。
     * </p>
     *
     * @param startTime 开始时间，ISO 8601格式（可选）
     * @param endTime 结束时间，ISO 8601格式（可选）
     * @param status 任务状态过滤条件（可选）
     * @param pageSize 分页大小，默认10，最大100
     * @param nextToken 下一页标记，用于分页查询（可选）
     * @return 返回阿里云响应的原始JSON字符串，包含任务列表信息
     * @throws Exception 当API调用失败时抛出异常
     */
    String listBatchMediaProducingJobs(String startTime, String endTime, String status,
                                     Integer pageSize, String nextToken, String jobType) throws Exception;
}
