package com.ruoyi.platform.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformVideo;

/**
 * 视频合成Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PlatformVideoMapper 
{
    /**
     * 查询视频合成
     * 
     * @param id 视频合成主键
     * @return 视频合成
     */
    public PlatformVideo selectPlatformVideoById(Long id);

    /**
     * 查询视频合成列表
     * 
     * @param platformVideo 视频合成
     * @return 视频合成集合
     */
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo);

    /**
     * 新增视频合成
     * 
     * @param platformVideo 视频合成
     * @return 结果
     */
    public int insertPlatformVideo(PlatformVideo platformVideo);

    /**
     * 修改视频合成
     * 
     * @param platformVideo 视频合成
     * @return 结果
     */
    public int updatePlatformVideo(PlatformVideo platformVideo);

    /**
     * 删除视频合成
     * 
     * @param id 视频合成主键
     * @return 结果
     */
    public int deletePlatformVideoById(Long id);

    /**
     * 批量删除视频合成
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformVideoByIds(Long[] ids);

    /**
     * 查询单个任务
     *
     * @param PlatformVideo 查询条件
     * @return 任务信息
     */
    public PlatformVideo selectPlatformVideoOne(PlatformVideo platformVideo);

    // 查询所有一小时前进入处理中状态的任务 1 待处理 2处理中 3 完成 4失败
    @Select(" SELECT * FROM platform_video WHERE status = '2' AND updated_at < #{oneHourAgo}")
    public List<PlatformVideo> selectVideoTasks(Date oneHourAgo);

    // 查询所有已完成的数字人对话合成任务（状态为3且operation包含对话相关信息）
    @Select("SELECT * FROM platform_video WHERE status = '3' AND operation IS NOT NULL AND operation LIKE '%dialogueGroupId%'")
    public List<PlatformVideo> selectCompletedDialogueTasks();
}
