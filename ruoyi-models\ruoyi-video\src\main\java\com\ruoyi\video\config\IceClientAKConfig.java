package com.ruoyi.video.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyun.ice20201109.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云智能媒体服务(ICE)及通用客户端配置类
 * <p>
 * 负责从 application.yml 中读取 "aliyun.ice" 前缀的配置，
 * 并基于这些配置信息创建和初始化两种不同类型的阿里云服务客户端Bean。
 * </p>
 */
@Slf4j
@Data
@Configuration("ice")
@ConfigurationProperties("aliyun.ice")
public class IceClientAKConfig {

    /**
     * 阿里云访问密钥ID (AccessKey ID)。
     */
    private String accessKeyId;

    /**
     * 阿里云访问密钥私钥 (AccessKey Secret)。
     */
    private String accessKeySecret;

    /**
     * 智能媒体服务(ICE)的接入地址 (Endpoint)。
     * 例如: ice.cn-shanghai.aliyuncs.com
     */
    private String endpoint;

    /**
     * 阿里云服务的区域ID (Region ID)。
     * 例如: cn-shanghai
     * 这个配置主要由通用的 AcsClient 使用。
     */
    private String regionId;

    /**
     * 回调配置
     */
    private CallbackConfig callback;

    /**
     * 回调配置内嵌类
     */
    @Data
    public static class CallbackConfig {
        /**
         * 回调通知地址
         */
        private String notifyUrl;

    }

    /**
     * 创建基于新版TeaDSL风格的ICE客户端。
     * <p>
     * 此客户端 ({@link com.aliyun.ice20201109.Client}) 是通过阿里云官方推荐的SDK方式创建，
     * 专门用于调用ICE定义的、结构化的API（例如 GetTemplate, SubmitMediaProducingJob 等）。
     * 它依赖于Endpoint进行服务寻址。
     * </p>
     *
     * @return 配置完成的ICE客户端实例。
     * @throws Exception 初始化过程中可能出现的异常。
     */
    @Bean
    public Client iceClientAK() throws Exception {
        if (accessKeyId == null || accessKeySecret == null || endpoint == null) {
            log.error("ICE Client 初始化失败：accessKeyId、accessKeySecret 或 endpoint 未配置");
            throw new IllegalArgumentException("ICE Client 初始化失败：accessKeyId、accessKeySecret 或 endpoint 未配置");
        }

        log.info("初始化 ICE Client，Endpoint: {}", endpoint);
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint(endpoint);
        return new Client(config);
    }

    /**
     * 创建一个通用的阿里云服务客户端 (IAcsClient)。
     * <p>
     * 此客户端 ({@link com.aliyuncs.IAcsClient}) 是基于旧版的 aliyun-java-sdk-core 创建的，
     * 具有更强的通用性，可以通过构造 {@link com.aliyuncs.CommonRequest} 来调用任意云产品的任意API，
     * 非常适合用于调用SDK尚未覆盖或不想为其引入完整SDK的API（如此次使用的 ListEditingProjects）。
     * 它依赖于RegionID进行服务寻址。
     * </p>
     *
     * @return 配置完成的通用客户端实例，如果缺少必要配置则抛出异常。
     */
    @Bean
    public IAcsClient acsClient() {
        if (regionId == null || accessKeyId == null || accessKeySecret == null) {
            log.warn("AcsClient 初始化失败：regionId、accessKeyId 或 accessKeySecret 未配置");
            throw new IllegalArgumentException("AcsClient 初始化失败：regionId、accessKeyId 或 accessKeySecret 未配置");
        }

        log.info("初始化 AcsClient，RegionId: {}", regionId);
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        return new DefaultAcsClient(profile);
    }

    /**
     * 剪辑合成所需要的client
     *
     * @throws Exception
     */
    @Bean
    public Client mediaClient() throws Exception {
        if (endpoint == null || accessKeyId == null || accessKeySecret == null) {
            log.warn("mediaClient 初始化失败：endpoint、accessKeyId 或 accessKeySecret 未配置");
            throw new IllegalArgumentException("mediaClient 初始化失败：endpoint、accessKeyId 或 accessKeySecret 未配置");
        }
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint(endpoint);
        return new Client(config);

    }
}