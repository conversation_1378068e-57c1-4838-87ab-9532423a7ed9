package com.ruoyi.coze.service;

import com.ruoyi.coze.domain.WorkflowRunRequest;
import java.util.Map;

/**
 * Coze工作流服务接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ICozeWorkflowService {

    /**
     * 运行工作流
     *
     * @param request 工作流运行请求
     * @return 执行结果
     */
    Map<String, Object> runWorkflow(WorkflowRunRequest request);

    /**
     * 获取工作流执行状态
     *
     * @param workflowId 工作流ID
     * @param executeId  执行ID
     * @return 执行状态
     */
    Map<String, Object> getAsyncWorkflowStatus(String workflowId, String executeId);

    /**
     * 轮询工作流执行结果
     *
     * @param workflowId   工作流ID
     * @param executeId    执行ID
     * @param pollInterval 轮询时间间隔（秒）
     * @return 执行结果
     */
    Map<String, Object> pollAsyncWorkflowResult(String workflowId, String executeId, int pollInterval);

}