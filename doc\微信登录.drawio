<mxfile host="65bd71144e">
    <diagram id="X-9zxwZzwVme25TE_fPk" name="第 1 页">
        <mxGraphModel dx="1002" dy="1732" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="微信小程序" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="100" y="130" width="100" height="390" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="2">
                    <mxGeometry x="45" y="140" width="10" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="wx.login" style="edgeStyle=orthogonalEdgeStyle;html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;entryX=1;entryY=0;" edge="1" target="16" parent="2">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="50" y="120" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="80" y="120"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="2">
                    <mxGeometry x="45" y="260" width="10" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="开发者服务器" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="330" y="130" width="100" height="390" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="3">
                    <mxGeometry x="45" y="170" width="10" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="微信接口服务" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" vertex="1" parent="1">
                    <mxGeometry x="590" y="130" width="100" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="html=1;points=[];perimeter=orthogonalPerimeter;" vertex="1" parent="4">
                    <mxGeometry x="45" y="175" width="10" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="html=1;verticalAlign=bottom;endArrow=block;exitX=0.9;exitY=0.89;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="16" target="9">
                    <mxGeometry width="80" relative="1" as="geometry">
                        <mxPoint x="155" y="305" as="sourcePoint"/>
                        <mxPoint x="290" y="340" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="携带code" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8">
                    <mxGeometry x="-0.1218" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="携带appid、appsecret、code" style="html=1;verticalAlign=bottom;endArrow=block;entryX=-0.02;entryY=0.01;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="9" target="11">
                    <mxGeometry width="80" relative="1" as="geometry">
                        <mxPoint x="460" y="330" as="sourcePoint"/>
                        <mxPoint x="550" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="返回session_key、openid" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;entryX=1.02;entryY=0.567;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="11" target="9">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="574" y="360" as="sourcePoint"/>
                        <mxPoint x="414" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="返回token" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;entryX=0.98;entryY=0.235;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="9" target="20">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="290" y="400" as="sourcePoint"/>
                        <mxPoint x="210" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="code：临时登录凭证，只能使用一次&lt;br&gt;appid：开发者id&lt;br&gt;appsecret：开发者密码&lt;br&gt;openid：用户唯一标识&lt;br&gt;session_key：会话密钥，对用户数据进行加密的签名" style="html=1;dropTarget=0;align=left;" vertex="1" parent="1">
                    <mxGeometry x="90" y="-80" width="300" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="shape=module;jettyWidth=8;jettyHeight=4;" vertex="1" parent="21">
                    <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
                        <mxPoint x="-27" y="7" as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>