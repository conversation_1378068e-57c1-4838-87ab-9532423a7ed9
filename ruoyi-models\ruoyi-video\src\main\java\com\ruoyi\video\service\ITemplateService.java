package com.ruoyi.video.service;

import com.ruoyi.video.dto.TemplateRequestDTO;

/**
 * 智能媒体服务(ICE)模板管理 服务层
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface ITemplateService {

    /**
     * 新增模板
     */
    String addTemplate(TemplateRequestDTO requestDTO) throws Exception;

    /**
     * 获取模板列表
     */
    String listTemplates(Long pageNo, Long pageSize, String type, String status, String createSource, String keyword, String sortType) throws Exception;

    /**
     * 获取单个模板详细信息
     */
    String getTemplate(String templateId, String relatedMediaidFlag) throws Exception;

    /**
     * 修改模板
     */
    String updateTemplate(TemplateRequestDTO requestDTO) throws Exception;

    /**
     * 删除模板
     */
    String deleteTemplate(String templateIds) throws Exception;

    /**
     * 获取模板素材地址
     * <p>
     * 返回高级模板包中的素材地址，供高级模板编辑器使用，素材链接30分钟过期。
     * FileList为所需素材数组，不填则默认返回全部素材地址，最多返回400个。
     * </p>
     *
     * @param templateId 模板ID
     * @param fileList 可选。所需文件列表，JSON格式字符串，如：["music.mp3","config.json","assets/1.jpg"]
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了素材地址映射信息
     * @throws Exception 当API调用失败或发生其他异常时抛出
     */
    String getTemplateMaterials(String templateId, String fileList) throws Exception;
}