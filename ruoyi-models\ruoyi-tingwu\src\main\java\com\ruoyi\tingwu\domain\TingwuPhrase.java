package com.ruoyi.tingwu.domain;



import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 词表信息对象 tingwu_phrase
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@Schema(description = "词表信息对象")
public class TingwuPhrase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 词表编号 */
    @Schema(title = "词表编号")
    private String phraseId;

    /** 词表表名 */
    @Schema(title = "词表表名")
    @Excel(name = "词表表名")
    private String name;

    /** 词表描述 */
    @Schema(title = "词表描述")
    @Excel(name = "词表描述")
    private String description;

    /** 词表权重 */
    @Schema(title = "词表权重")
    @Excel(name = "词表权重")
    private String wordWeights;


}
