package com.ruoyi.platform.model.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.model.domain.PlatformTask;
import com.ruoyi.platform.model.domain.TtsRequest;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 任务管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
public interface IPlatformTaskService 
{
    /**
     * 查询任务管理
     * 
     * @param taskId 任务管理主键
     * @return 任务管理
     */
    public PlatformTask selectPlatformTaskByTaskId(Long taskId);

    /**
     * 查询任务管理列表
     * 
     * @param platformTask 任务管理
     * @return 任务管理集合
     */
    public List<PlatformTask> selectPlatformTaskList(PlatformTask platformTask);

    /**
     * 新增任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    public int insertPlatformTask(PlatformTask platformTask);

    /**
     * 修改任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    public int updatePlatformTask(PlatformTask platformTask);

    /**
     * 批量删除任务管理
     * 
     * @param taskIds 需要删除的任务管理主键集合
     * @return 结果
     */
    public int deletePlatformTaskByTaskIds(Long[] taskIds);

    /**
     * 删除任务管理信息
     * 
     * @param taskId 任务管理主键
     * @return 结果
     */
    public int deletePlatformTaskByTaskId(Long taskId);

    // 根据任务的机器码
    public PlatformTask selectPlatformmachineCode(String machineCode);

    // 创建一个声音推理任务
    public Long createTTSAudio(PlatformArticle[] platformArticle, Long modelName);

    // 处理声音推理任务回调
    public int ttsCallback(PlatformTask platformTask);

    // 创建一个训练声音任务
    public Long createTaskSAudio(Long modelName,String trainPath);

    // 处理训练任务回调
    public int taskCallback(PlatformTask platformTask);
    
    //模型服务上传训练后的声音模型文件: ModelCheckpoint/sound/xxxx.ckpt
    public Object uploadModelSoundFile(MultipartFile gptModel, MultipartFile sovitsModel, Long taskId, Long soundId, String machineCode) throws Exception;

    //通用下载音频文件
    public void downloadAudio(String machineCode, String fileUrl, HttpServletResponse response) throws Exception;

    //通过机器码下载音频
    public void downloadAudioByMachineCode(String machineCode, String fileUrl, HttpServletResponse response) throws Exception;

    //模型上传服务后的推理音频文件
    public Object uploadModelAudioFile( MultipartFile audioFile, Long taskId, String machineCode) throws Exception;

    //推理音频上传
    public String uploadTask(MultipartFile file, Long taskId,String machineCode) throws Exception;

    // 创建文本转音频任务
    public Long createTextToAudio(String text, Long modelName, Long categoryId);

    // 接入阿里云语音合成
    public String synthesize(TtsRequest request);
}
