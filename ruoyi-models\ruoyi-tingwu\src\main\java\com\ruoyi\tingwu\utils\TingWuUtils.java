package com.ruoyi.tingwu.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.tingwu.domain.TingWuRequest;
import com.ruoyi.tingwu.domain.TingWuResult;
import com.ruoyi.tingwu.domain.TingwuTrans;
import com.ruoyi.tingwu.enums.AudioVideoStatus;
import com.ruoyi.tingwu.enums.SpeakerType;
import com.ruoyi.tingwu.enums.TranslateOption;

/**
 * 听悟工具类
 *
 * <AUTHOR>
 */
public class TingWuUtils {

    /**
     * 初始化听悟实时任务请求参数
     *
     * @return 配置好的参数JSON对象
     */
    public static JSONObject initRequestParameters(TingWuRequest tingWuRequest) {
        JSONObject parameters = new JSONObject();

        // 语音识别
        JSONObject transcription = new JSONObject();

        // 判断是否需要声音分离
        String speakerType = tingWuRequest.getSpeakerType();
        boolean needSeparate = speakerType.equals(SpeakerType.NONE.getCode());
        if (!needSeparate) {// 需要分离
            transcription.put("DiarizationEnabled", true);
            // 这里需要设置是不定人数还是2个人，1个人不用设置 speakerCount
            if (speakerType.equals(SpeakerType.TWO_PEOPLE.getCode())
                    || speakerType.equals(SpeakerType.MULTIPLE.getCode())) {
                JSONObject speakerCount = new JSONObject();
                speakerCount.put("SpeakerCount", SpeakerType.fromCode(speakerType).getDescription());
                transcription.put("Diarization", speakerCount);
            }

        }
        if (StringUtils.isNotEmpty(tingWuRequest.getPhraseId())) {
            transcription.put("PhraseId", tingWuRequest.getPhraseId());
        }
        parameters.put("Transcription", transcription);

        // 翻译： 可选
        String translate = tingWuRequest.getTranslate();
        if (!TranslateOption.NONE.getCode().equals(translate)) {
            JSONObject translation = new JSONObject();
            JSONArray langArry = new JSONArray();
            String lanType = TranslateOption.fromCode(translate).getDescription();
            langArry.add(lanType);
            translation.put("TargetLanguages", langArry);
            parameters.put("Translation", translation);
            parameters.put("TranslationEnabled", true);

        }

        // 章节速览： 可选
        parameters.put("AutoChaptersEnabled", true);

        // 智能纪要： 可选 （包括：关键词）
        parameters.put("MeetingAssistanceEnabled", true);
        JSONObject meetingAssistance = new JSONObject();
        JSONArray mTypes = new JSONArray().fluentAdd("Actions").fluentAdd("KeyInformation");
        meetingAssistance.put("Types", mTypes);
        parameters.put("MeetingAssistance", meetingAssistance);

        // 摘要相关： 可选 (包含： 全文摘要、发言总结、问答回顾)
        parameters.put("SummarizationEnabled", true);
        JSONObject summarization = new JSONObject();
        JSONArray sTypes = new JSONArray().fluentAdd("Paragraph").fluentAdd("Conversational")
                .fluentAdd("QuestionsAnswering");
        summarization.put("Types", sTypes);
        parameters.put("Summarization", summarization);

        return parameters;
    }

    /** 获取文件的全路径 */
    public static String getRecordFileFullPath(MultipartFile file) {
        String basePath = "video/record";
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6));
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp));
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        return fullPath;
    }

    /**
     * 获取JSON内容
     */
    public static JSONObject fetchJsonContent(String url) {
        try {
            // 创建HTTP客户端
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);

            // 执行请求
            CloseableHttpResponse httpResponse = httpClient.execute(httpGet);

            // 获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity);
                httpClient.close();
                return JSONObject.parseObject(content);
            }
            httpClient.close();
        } catch (Exception e) {
            throw new RuntimeException("HTTP请求失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 处理听悟转录结果并保存到数据库
     *
     * @param result 听悟结果对象
     * @param taskId 任务ID
     */
    public static TingwuTrans processTingwuResult(TingWuResult result, String taskId) {
        try {
            // 初始化变量
            String keywords = "";
            String overview = "";
            String summary = "";
            String statements = "";
            String qaReview = "";
            Long duration = 0L;

            // 关键词
            JSONObject ma = fetchJsonContent(result.getMeetingAssistance());
            // 处理关键词
            if (ma.containsKey("MeetingAssistance")) {
                JSONObject meetingAssistance = ma.getJSONObject("MeetingAssistance");
                if (meetingAssistance.containsKey("Keywords")) {
                    JSONArray keywordsArray = meetingAssistance.getJSONArray("Keywords");
                    if (keywordsArray != null && !keywordsArray.isEmpty()) {
                        keywords = keywordsArray.toJSONString();
                    }
                }
            }

            // 处理章节速览
            JSONObject autoChJsonObject = fetchJsonContent(result.getAutoChapters());
            if (autoChJsonObject.containsKey("AutoChapters")) {
                JSONArray autoChapters = autoChJsonObject.getJSONArray("AutoChapters");
                if (autoChapters != null && autoChapters.size() > 0) {
                    JSONObject firstChapter = autoChapters.getJSONObject(0);
                    if (firstChapter.containsKey("Summary")) {
                        overview = firstChapter.getString("Summary");
                    }
                }
            }

            // 处理全文概要、发言总结、问答回顾
            JSONObject summerJsonObject = fetchJsonContent(result.getSummarization());
            if (summerJsonObject.containsKey("Summarization")) {
                JSONObject summarization = summerJsonObject.getJSONObject("Summarization");
                // 全文概要
                if (summarization != null && summarization.containsKey("ParagraphSummary")) {
                    summary = summarization.getString("ParagraphSummary");
                }
                // 发言总结
                if (summarization != null && summarization.containsKey("ConversationalSummary")) {
                    JSONArray conversationalSummary = summarization.getJSONArray("ConversationalSummary");
                    if (conversationalSummary != null && !conversationalSummary.isEmpty()) {
                        statements = conversationalSummary.toJSONString();
                    }
                }
                // 问答回顾
                if (summarization != null && summarization.containsKey("QuestionsAnsweringSummary")) {
                    JSONArray qaSummary = summarization.getJSONArray("QuestionsAnsweringSummary");
                    if (qaSummary != null && !qaSummary.isEmpty()) {
                        qaReview = qaSummary.toJSONString();
                    }
                }
            }

            // 翻译
            JSONObject tranJsonObject = fetchJsonContent(result.getTranscription());

            // 处理格式化对话数据
            JSONArray conversationsArray = processConversations(tranJsonObject);
            String conversations = conversationsArray.toJSONString();
            // 获取视频的时长，单位毫秒
            if (tranJsonObject.containsKey("Transcription")
                    && tranJsonObject.getJSONObject("Transcription").containsKey("AudioInfo")) {
                JSONObject audioInfo = tranJsonObject.getJSONObject("Transcription").getJSONObject("AudioInfo");
                if (audioInfo.containsKey("Duration")) {
                    long durationMs = audioInfo.getLongValue("Duration");
                    duration = durationMs;
                }
            }

            // 保存到数据库
            TingwuTrans tingwuTrans = new TingwuTrans();
            tingwuTrans.setTags(keywords);
            tingwuTrans.setTaskId(taskId);
            tingwuTrans.setDuration(duration);
            tingwuTrans.setSummary(summary);
            tingwuTrans.setOverview(overview);
            tingwuTrans.setStatements(statements);
            tingwuTrans.setQaReview(qaReview);
            tingwuTrans.setParagraphs(conversations);
            tingwuTrans.setVaStatus(AudioVideoStatus.SUCCESS.getCode());
            return tingwuTrans;
        } catch (Exception e) {
            // 更新状态为失败
            TingwuTrans failedTrans = new TingwuTrans();
            failedTrans.setTaskId(taskId);
            failedTrans.setVaStatus(AudioVideoStatus.FAILED.getCode());
            return failedTrans;
        }
    }

    /**
     * 处理对话数据，格式化为前端需要的格式
     */
    private static JSONArray processConversations(JSONObject tranJsonObject) {
        JSONArray conversations = new JSONArray();

        if (!tranJsonObject.containsKey("Transcription"))
            return conversations;
        JSONObject transcription = tranJsonObject.getJSONObject("Transcription");
        if (!transcription.containsKey("Paragraphs"))
            return conversations;

        JSONArray paragraphs = transcription.getJSONArray("Paragraphs");

        for (int i = 0; i < paragraphs.size(); i++) {
            JSONObject paragraph = paragraphs.getJSONObject(i);
            JSONArray words = paragraph.getJSONArray("Words");

            if (words == null || words.isEmpty())
                continue;

            // 组合完整文本
            StringBuilder fullText = new StringBuilder();
            JSONArray wordDetails = new JSONArray();
            for (int j = 0; j < words.size(); j++) {
                JSONObject word = words.getJSONObject(j);
                fullText.append(word.getString("Text"));
                JSONObject wordDetail = new JSONObject();
                wordDetail.put("id", word.getIntValue("Id"));
                wordDetail.put("text", word.getString("Text"));
                wordDetail.put("startTime", word.getIntValue("Start"));
                wordDetail.put("endTime", word.getIntValue("End"));
                wordDetails.add(wordDetail);
            }
            // 获取开始和结束时间
            JSONObject firstWord = words.getJSONObject(0);
            JSONObject lastWord = words.getJSONObject(words.size() - 1);

            String speakerId = paragraph.getString("SpeakerId");

            JSONObject conversation = new JSONObject();
            conversation.put("speakerId", speakerId);

            conversation.put("text", fullText.toString());
            conversation.put("startTime", firstWord.getIntValue("Start"));
            conversation.put("endTime", lastWord.getIntValue("End"));
            conversation.put("wordDetails", wordDetails);

            conversations.add(conversation);
        }

        // 按开始时间排序
        conversations.sort((a, b) -> {
            JSONObject objA = (JSONObject) a;
            JSONObject objB = (JSONObject) b;
            return Integer.compare(objA.getIntValue("startTime"),
                    objB.getIntValue("startTime"));
        });

        return conversations;
    }





}
