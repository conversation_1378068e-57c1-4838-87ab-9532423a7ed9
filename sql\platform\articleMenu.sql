-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案', '2027', '1', 'article', 'platform/article/index', 1, 0, 'C', '0', '0', 'platform:article:list', '#', 'admin', sysdate(), '', null, '文案菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'platform:article:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'platform:article:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'platform:article:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'platform:article:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('文案导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'platform:article:export',       '#', 'admin', sysdate(), '', null, '');