package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.vo.DialogueSynthesisRequest;

/**
 * 视频合成Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPlatformVideoService 
{
    /**
     * 查询视频合成
     * 
     * @param id 视频合成主键
     * @return 视频合成
     */
    public PlatformVideo selectPlatformVideoById(Long id);

    /**
     * 查询视频合成列表
     * 
     * @param platformVideo 视频合成
     * @return 视频合成集合
     */
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo);

    /**
     * 修改视频合成
     * 
     * @param platformVideo 视频合成
     * @return 结果
     */
    public int updatePlatformVideo(PlatformVideo platformVideo);

    /**
     * 批量删除视频合成
     * 
     * @param ids 需要删除的视频合成主键集合
     * @return 结果
     */
    public int deletePlatformVideoByIds(Long[] ids);

    /**
     * 新增任务
     *
     * @param musetalkTask 任务信息
     * @return 影响行数
     */
    public Long add(PlatformVideo platformVideo);

    // 查询单个任务
    public PlatformVideo getOneTask(String version);

    
    // 根据版本号查询单个任务
    public PlatformVideo getOneTaskByVersion(String vsrsion);


     // 根据任务结果动态更新任务状态
    public void updateTaskStatus(Long id, Long status, String resultVideo);

    // 上传合成视频音频素材文件
    public Object uploadAudioFile(MultipartFile file);

    /**
     * 上传媒体文件(视频或音频)并生成临时访问URL
     * @param file 文件
     * @param type 类型(video/audio)
     * @return 
     */
    public Map<String, String> uploadMedia(MultipartFile file, String type) throws Exception;

    /**
     * 创建视频合成任务并返回带临时访问URL的结果
     * @param params 请求参数
     * @return 包含临时访问URL的响应结果,格式:
     */
    public Map<String, Object> createVideoSynthesisWithUrls(Map<String, Object> params) throws Exception;

    /**
     * 查询待处理的视频任务列表
     * 
     * @return 待处理的视频任务列表
     */
    public List<PlatformVideo> selectPendingTasks();

    /**
     * 获取可用的视频合成模型列表
     * @return 模型列表
     */
    public List<PlatformModel> getAvailableModels();

    /**
     * 根据taskNo查询视频合成任务状态
     * @param taskNo 任务编号
     * @return 任务状态和结果
     */
    public Map<String, Object> queryVideoSynthesis(String taskNo);

    public Map<String, Object> createVideoSynthesis(Map<String, Object> params);

    /**
     * 合成H版新增任务
     *
     * @param param 信息
     * @return 影响行数
     */
    public Long synthesisH(PlatformVideo param);

    /**
     * 数字人对话合成（增强版，支持自动合成）
     * @param request 对话合成请求
     * @return 包含任务状态和结果的信息
     */
    public Map<String, Object> createDialogueSynthesisEnhanced(DialogueSynthesisRequest request);

    /**
     * 查询自动合成任务的完整状态（包括云剪辑状态）
     * @param dialogueGroupId 对话组ID
     * @return 包含完整流程状态的信息
     */
    public Map<String, Object> getAutoSynthesisStatus(String dialogueGroupId);
}
