package com.ruoyi.platform.utils.taskUtils;

/**
 * 视频合成任务状态常量
 */
public class PlatformVideoTaskStatusAndVersion {
    // 状态码
    public static final String STATUS_PENDING = "1";
    public static final String STATUS_PROCESSING = "2";
    public static final String STATUS_SUCCESS = "3";
    public static final String STATUS_FAILED = "4";
    
    // 状态描述
    public static final String STATUS_DESC_PENDING = "待处理";
    public static final String STATUS_DESC_PROCESSING = "处理中";
    public static final String STATUS_DESC_SUCCESS = "处理完成";
    public static final String STATUS_DESC_FAILED = "处理失败";

    // 版本编号
    public static final String H_VERSION = "H";
    public static final String M_VERSION = "M";
    public static final String V_VERSION = "V";

    // 版本描述
    public static final String H_DESC_VERSION = "H版本";
    public static final String M_DESC_VERSION = "M版本";
    public static final String V_DESC_VERSION = "V版本";
}
