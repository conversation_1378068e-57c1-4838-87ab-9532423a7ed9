package com.ruoyi.platform.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.platform.domain.PlatformImage;

/**
 * 形象管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface IPlatformImageService 
{
    /**
     * 查询形象管理
     * 
     * @param imageId 形象管理主键
     * @return 形象管理
     */
    public PlatformImage selectPlatformImageByImageId(Long imageId);

    /**
     * 查询形象管理列表
     * 
     * @param platformImage 形象管理
     * @return 形象管理集合
     */
    public List<PlatformImage> selectPlatformImageList(PlatformImage platformImage);

    /**
     * 批量删除形象管理
     * 
     * @param imageIds 需要删除的形象管理主键集合
     * @return 结果
     */
    public int deletePlatformImageByImageIds(Long[] imageIds);

    /**
     * 根据形象地址生成临时凭证
     */
    public PlatformImage getImageDetailByAddress(String imageAddress) throws Exception;

    /**
     * 初始化分片上传
     */
    public Object initOssUpload(String imageName, String fileName, long fileSize) throws Exception;

    /**
     * 上传分片
     */
    public Object uploadOssChunk(String uploadId, String filePath, int chunkIndex, MultipartFile chunk) throws Exception;

    /**
     * 完成分片上传
     */
    public Object completeOssUpload(String imageName, String uploadId, String filePath, List<SysFilePartETag> partETags) throws Exception;
    
    /**
     * 开始编辑形象（准备替换文件）
     */
    public Object startEditImage(Long imageId, String fileName, long fileSize) throws Exception;
    
    /**
     * 完成形象编辑（替换文件完成）
     */
    public Object finishEditImage(Long imageId, String uploadId, String filePath, List<SysFilePartETag> partETags, String imageName) throws Exception;
}
