# 请输入自己的appid和appsecret
oauth:
  wx:
    miniapp:
      open: true
      appId: appId
      appSecret: appSecret
      url: https://api.weixin.qq.com/sns/jscode2session
    pub:
      open: true
      appId: appId
      appSecret: appSecret
      url: https://api.weixin.qq.com/sns/oauth2/access_token

tfa:
  phone:
    dysms:
      # 阿里云 AccessKey ID
      accessKeyId: LTAI5tPwLQ5XjHWaKBM6rrk3
      # 阿里云 AccessKey Secret
      accessKeySecret: ******************************
      # 短信模板
      template:
          VerificationCode:
            # 短信模板编码
            templateCode: SMS_489825204
            # 短信签名
            signName: 山东数智宝数字科技
            # 短信模板必需的数据名称，多个key以逗号分隔，此处配置作为校验
            keys: code 

spring:
  mail:
    host: smtp.163.com
    # 邮箱地址
    username: s<PERSON><PERSON><PERSON><PERSON>@163.com
    # 授权码
    password: YAeuvVx2Rxi5tYR6
