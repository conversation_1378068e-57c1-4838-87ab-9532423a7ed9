package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户算力点数变化记录对象 platform_consumption
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Schema(description = "用户算力点数变化记录对象")
public class PlatformConsumption extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long consumptionId;

    /** 关联用户Id */
    @Schema(title = "用户名称")
    @Excel(name = "用户名称")
    private String userName;

    /** 消费标题 在哪个模块消费 */
    @Schema(title = "消费标题 在哪个模块消费")
    @Excel(name = "消费标题 在哪个模块消费")
    private String consumptionTitle;

    /** 记录 0充值 1消费 */
    @Schema(title = "记录 0充值 1消费")
    @Excel(name = "记录 0充值 1消费")
    private String consumptionStaus;

    /** 消费或充值算力点数 */
    @Schema(title = "消费或充值算力点数")
    @Excel(name = "消费或充值算力点数")
    private String consumptionHashrate;

    /** 剩余算力点数 */
    @Schema(title = "剩余算力点数")
    @Excel(name = "剩余算力点数")
    private String consumptionResidue;
    public void setConsumptionId(Long consumptionId) 
    {
        this.consumptionId = consumptionId;
    }

    public Long getConsumptionId() 
    {
        return consumptionId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setConsumptionTitle(String consumptionTitle) 
    {
        this.consumptionTitle = consumptionTitle;
    }

    public String getConsumptionTitle() 
    {
        return consumptionTitle;
    }

    public void setConsumptionStaus(String consumptionStaus) 
    {
        this.consumptionStaus = consumptionStaus;
    }

    public String getConsumptionStaus() 
    {
        return consumptionStaus;
    }

    public void setConsumptionHashrate(String consumptionHashrate) 
    {
        this.consumptionHashrate = consumptionHashrate;
    }

    public String getConsumptionHashrate() 
    {
        return consumptionHashrate;
    }

    public void setConsumptionResidue(String consumptionResidue) 
    {
        this.consumptionResidue = consumptionResidue;
    }

    public String getConsumptionResidue() 
    {
        return consumptionResidue;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("consumptionId", getConsumptionId())
            .append("userName", getUserName())
            .append("consumptionTitle", getConsumptionTitle())
            .append("consumptionStaus", getConsumptionStaus())
            .append("consumptionHashrate", getConsumptionHashrate())
            .append("consumptionResidue", getConsumptionResidue())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
