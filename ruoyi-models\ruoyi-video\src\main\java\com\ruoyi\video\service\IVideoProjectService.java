package com.ruoyi.video.service;

import java.util.List;

import com.ruoyi.video.domain.MediaEdit;
import com.ruoyi.video.dto.CreateEditingProjectRequestDTO;
import com.ruoyi.video.dto.SubmitProjectExportRequestDTO;

/**
 * 云剪辑工程 服务层接口
 * <p>
 * 定义了与云剪辑工程相关的业务操作。
 * 这是业务逻辑的抽象，具体实现由 VideoProjectServiceImpl 提供。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IVideoProjectService {

    /**
     * 调用阿里云ICE API获取云剪辑工程列表。
     *
     * @param keyword    可选。搜索关键字，用于模糊匹配工程的标题和描述。
     * @param status     可选。筛选特定状态的工程，如 "Producing" (制作中), "Normal" (正常) 等。
     * @param nextToken  可选。请求下一页数据的凭证。首次请求或不分页时，此项为空。
     * @param maxResults 可选。每页返回的最大记录数。
     * @return 阿里云API返回的原始JSON格式字符串。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String listEditingProjects(String keyword, String status, String nextToken, Integer maxResults , String startTime, String endTime) throws Exception;

    /**
     * 调用阿里云ICE API删除云剪辑工程。
     * <p>
     * 支持批量删除，多个ID之间用逗号分隔。
     * </p>
     *
     * @param projectIds 要删除的云剪辑工程ID列表，以逗号分隔的字符串。
     * @return 阿里云API返回的原始JSON格式字符串，其中可能包含删除失败或不存在的ID列表。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String deleteEditingProjects(String projectIds) throws Exception;

    /**
     * 调用阿里云ICE API创建云剪辑工程。
     *
     * @param requestDTO 包含创建工程所需全部参数的数据传输对象。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了新创建的工程信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String createEditingProject(CreateEditingProjectRequestDTO requestDTO) throws Exception;

    /**
     * 调用阿里云ICE API获取单个云剪辑工程的详细信息。
     *
     * @param projectId     要查询的云剪辑工程ID。
     * @param requestSource 可选。请求来源标识，如 "WebSDK"，用于触发特定的服务端行为（如Timeline转换）。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了指定的工程详细信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String getEditingProject(String projectId, String requestSource) throws Exception;

    /**
     * 调用阿里云ICE API提交云剪辑工程导出任务。
     * <p>
     * 支持将云剪辑工程导出为视频文件或Adobe PR工程文件。
     * 导出结果将保存到指定的OSS存储桶中。
     * </p>
     *
     * @param requestDTO 包含导出任务所需全部参数的数据传输对象。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了导出任务的JobId等信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String submitProjectExportJob(SubmitProjectExportRequestDTO requestDTO) throws Exception;

    /**
     * 调用阿里云ICE API查询云剪辑工程导出任务状态和结果。
     * <p>
     * 用于查询通过 SubmitProjectExportJob 提交的导出任务的执行状态、进度和结果。
     * 可以获取任务的详细信息，包括导出文件的下载地址（成功时）或错误信息（失败时）。
     * </p>
     *
     * @param jobId 工程导出任务ID，通过 SubmitProjectExportJob 接口返回的 JobId。
     * @return 阿里云API返回的原始JSON格式字符串，包含任务状态、结果等详细信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String getProjectExportJob(String jobId) throws Exception;

    /**
     * 从数据库中获取全部的数据
     * @param keyword
     * @param status
     * @param startTime
     * @param endTime
     * @return
     */
    List<MediaEdit> listEditingProjectsByDB(MediaEdit mediaEdit);

}