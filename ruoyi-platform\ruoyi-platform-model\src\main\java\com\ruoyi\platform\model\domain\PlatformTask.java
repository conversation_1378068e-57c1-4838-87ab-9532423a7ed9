package com.ruoyi.platform.model.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * 任务管理对象 platform_task
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
@Schema(description = "任务管理对象")
public class PlatformTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long taskId;

    /** 名称 */
    @Schema(title = "名称")
    @Excel(name = "名称")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /** 状态 */
    @Schema(title = "状态")
    @Excel(name = "状态")
    private String taskStatus;

    /** 内容 */
    @Schema(title = "内容")
    @Excel(name = "内容")
    private String taskContent;

    /** 机器码 */
    @Schema(title = "机器码")
    @Excel(name = "机器码")
    private String machineCode;

    /** 任务结果 */
    @Schema(title = "任务结果")
    @Excel(name = "任务结果")
    private String taskResult;

    /** 任务类型 */
    @Schema(title = "任务类型")
    @Excel(name = "任务类型")
    private String type;

    /** 模型名称 */
    @Schema(title = "模型名称")
    @Excel(name = "模型名称")
    @NotBlank(message = "模型名称不能为空")
    private String modelName;
    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }


    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }


    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }


    public void setTaskContent(String taskContent) 
    {
        this.taskContent = taskContent;
    }

    public String getTaskContent() 
    {
        return taskContent;
    }


    public void setMachineCode(String machineCode) 
    {
        this.machineCode = machineCode;
    }

    public String getMachineCode() 
    {
        return machineCode;
    }


    public void setTaskResult(String taskResult) 
    {
        this.taskResult = taskResult;
    }

    public String getTaskResult() 
    {
        return taskResult;
    }


    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }


    public void setModelName(String modelName) 
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("taskStatus", getTaskStatus())
            .append("taskContent", getTaskContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("machineCode", getMachineCode())
            .append("taskResult", getTaskResult())
            .append("type", getType())
            .append("modelName", getModelName())
            .toString();
    }
}
