package com.ruoyi.coze.domain;

import java.util.Map;

/**
 * Coze请求实体
 *
 * <AUTHOR>
 * @date 2025-06-25
 */

public class WorkflowRunRequest {

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 工作流参数
     */
    private Map<String, Object> parameters;

    /**
     * 机器人编号
     */
    private String botId;

    /**
     * 扩展参数
     */
    private Map<String, String> ext;
    
    /**
     * 是否异步运行 true-异步 false-同步
     * 
     */
    private boolean async;

    /**
     * 该工作流关联的应用的ID
     */
    private String appId;

     public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public String getBotId() {
        return botId;
    }

    public void setBotId(String botId) {
        this.botId = botId;
    }

    public Map<String, String> getExt() {
        return ext;
    }

    public void setExt(Map<String, String> ext) {
        this.ext = ext;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean isAsync) {
        this.async = isAsync;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }



}
