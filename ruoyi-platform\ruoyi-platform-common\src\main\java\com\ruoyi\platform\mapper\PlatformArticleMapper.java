package com.ruoyi.platform.mapper;

import java.util.Arrays;
import java.util.List;

import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import com.ruoyi.mybatis.utils.SQLGenerator;
import com.ruoyi.platform.domain.PlatformArticle;

/**
 * 文案Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface PlatformArticleMapper {
    /**
     * 查询文案
     * 
     * @param articleId 文案主键
     * @return 文案
     */
    @SelectProvider(value = SQLGenerator.class, method = "selectById")
    public PlatformArticle selectPlatformArticleByArticleId(Long id, Class<PlatformArticle> clz);

    default public PlatformArticle selectPlatformArticleByArticleId(Long articleId) {
        return selectPlatformArticleByArticleId(articleId, PlatformArticle.class);
    }

    /**
     * 查询文案列表
     * 
     * @param platformArticle 文案
     * @return 文案集合
     */
    @SelectProvider(value = SQLGenerator.class, method = SQLGenerator.Method.LIST)
    public List<PlatformArticle> selectPlatformArticleList(PlatformArticle platformArticle);

    /**
     * 新增文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    @InsertProvider(type = SQLGenerator.class, method = SQLGenerator.Method.INSERT)
    public int insertPlatformArticle(PlatformArticle platformArticle);

    /**
     * 修改文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    @UpdateProvider(type = SQLGenerator.class, method = SQLGenerator.Method.UPDATE)
    public int updatePlatformArticle(PlatformArticle platformArticle);

    /**
     * 删除文案
     * 
     * @param articleId 文案主键
     * @return 结果
     */
    @DeleteProvider(value = SQLGenerator.class, method = SQLGenerator.Method.DELETE_BY_ID)
    public int deletePlatformArticleByArticleId(Long articleId);

    /**
     * 批量删除文案
     * 
     * @param articleIds 需要删除的数据主键集合
     * @return 结果
     */
    @DeleteProvider(value = SQLGenerator.class, method = SQLGenerator.Method.DELETE_BY_IDS)
    public int deletePlatformArticleByArticleIds(List<Long> ids,Class<PlatformArticle> clz);
    default public int deletePlatformArticleByArticleIds(Long[] articleIds){
        return deletePlatformArticleByArticleIds(Arrays.asList(articleIds),PlatformArticle.class);
    };

    /**
     * 强制删除关联的文章和关键词数据。
     *
     * @param categoryId 分类ID
     * @return 影响的行数
     */
    public int deleteConstraintArticleAndKeywordCategoryId(Long categoryId);

    //导出当前项目全部文案
    public List<PlatformArticle> selectByCategoryIdList(List<Long> categoryIdList);

    //批量新增文案
    public int batchInsertPlatformArticle(List<PlatformArticle> list);

    //根据文案Ids查询文案信息
    public List<PlatformArticle> getArticleIds(List<Long> articleIds);

    //根据直播id查询文案信息
    public List<PlatformArticle> getArticlesByLiveId(Long liveId);

    //简单插入方法，支持自动生成主键
    public int insertArticle(PlatformArticle platformArticle);
}
