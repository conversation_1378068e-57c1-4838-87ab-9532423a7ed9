package com.ruoyi.platform.domain.vo;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.platform.domain.PlatformGoods;
import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.domain.PlatformScenecon;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 直播管理对象 platform_live
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Schema(description = "直播管理对象")
public class PlatformLiveVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long liveId;

    /** 项目Id */
    @Schema(title = "项目Id")
    @Excel(name = "项目Id")
    private Long projectId;

    /** 直播类型 0实景无人播 1数字人直播 */
    @Schema(title = "直播类型 0实景无人播 1数字人直播")
    @Excel(name = "直播类型 0实景无人播 1数字人直播")
    private String liveType;

    /** 场控Id */
    @Schema(title = "场控")
    @Excel(name = "场控")
    private PlatformScenecon scenecon;

    /** 产品Ids */
    @Schema(title = "产品列表")
    @Excel(name = "产品列表")
    private List<PlatformGoods> goods;

    /** 直播名称 */
    @Schema(title = "直播名称")
    @Excel(name = "直播名称")
    private String liveName;

    public void setLiveId(Long liveId) {
        this.liveId = liveId;
    }

    public Long getLiveId() {
        return liveId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setLiveType(String liveType) {
        this.liveType = liveType;
    }

    public String getLiveType() {
        return liveType;
    }

    public void setScenecon(PlatformScenecon scenecon) {
        this.scenecon = scenecon;
    }

    public PlatformScenecon getScenecon() {
        return scenecon;
    }

    public void setGoods(List<PlatformGoods> goods) {
        this.goods = goods;
    }

    public List<PlatformGoods> getGoods() {
        return goods;
    }

    public void setLiveName(String liveName) {
        this.liveName = liveName;
    }

    public String getLiveName() {
        return liveName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("liveId", getLiveId())
                .append("projectId", getProjectId())
                .append("liveType", getLiveType())
                .append("scenecon", getScenecon())
                .append("goods", getGoods())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("liveName", getLiveName())
                .toString();
    }

    public static PlatformLiveVo valueOf(PlatformLive platformLive) {
		PlatformLiveVo platformLiveVo = new PlatformLiveVo();
		platformLiveVo.setLiveId(platformLive.getLiveId());
		platformLiveVo.setProjectId(platformLive.getProjectId());
		platformLiveVo.setLiveType(platformLive.getLiveType());
        platformLiveVo.setLiveName(platformLive.getLiveName());
        platformLiveVo.setCreateBy(platformLive.getCreateBy());
        platformLiveVo.setCreateTime(platformLive.getCreateTime());
        platformLiveVo.setUpdateBy(platformLive.getUpdateBy());
        platformLiveVo.setUpdateTime(platformLive.getUpdateTime());
        platformLiveVo.setRemark(platformLive.getRemark());
        return platformLiveVo;
    }
}
