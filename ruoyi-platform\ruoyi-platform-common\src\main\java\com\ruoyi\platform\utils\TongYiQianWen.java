package com.ruoyi.platform.utils;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;

import io.reactivex.Flowable;

public class TongYiQianWen {

	public final static Logger logger = LoggerFactory.getLogger(TongYiQianWen.class.getName());

	//测试
	public static GenerationResult callWithMessage(String message)
			throws ApiException, NoApiKeyException, InputRequiredException {
		Generation gen = new Generation();
		Message systemMsg = Message.builder()
				.role(Role.SYSTEM.getValue())
				.content("你是一个乐于助人的助手")
				.build();
		Message userMsg = Message.builder()
				.role(Role.USER.getValue())
				.content(message)
				.build();
		GenerationParam param = GenerationParam.builder()
				.model("qwen-turbo")
				.apiKey("sk-056af90ca7074de58dea4cf343361b90")
				.messages(Arrays.asList(systemMsg, userMsg))
				.resultFormat(GenerationParam.ResultFormat.MESSAGE)
				.build();
		return gen.call(param);
	}

	// 测试
	public static Flowable<GenerationResult> callWithMessageAsync(String message)
			throws ApiException, NoApiKeyException, InputRequiredException {
		Generation gen = new Generation();
		Message systemMsg = Message.builder()
				.role(Role.SYSTEM.getValue())
				.content("你是一个乐于助人的助手")
				.build();
		Message userMsg = Message.builder()
				.role(Role.USER.getValue())
				.content(message)
				.build();
		GenerationParam param = GenerationParam.builder()
				.model("qwen-turbo")
				.apiKey("sk-056af90ca7074de58dea4cf343361b90")
				.messages(Arrays.asList(systemMsg, userMsg))
				.resultFormat(GenerationParam.ResultFormat.MESSAGE)
				.build();
		return gen.streamCall(param);
	}
 
	// 智能文案仿写  旧版 sk-056af90ca7074de58dea4cf343361b90
	public static List<String> callAgentApp(List<String> context)
			throws ApiException, NoApiKeyException, InputRequiredException {
		ApplicationParam param = ApplicationParam.builder()
				.apiKey("sk-d8ba4f561bbd49b9b938d7127f8e8bcb")
				.appId("d20f36dd412f45b096ed1aa8c150d988")
				.prompt(JSON.toJSONString(context))
				.build();

		Application application = new Application();
		ApplicationResult result = application.call(param);
		String resultMd = result.getOutput().getText();
		try {
			int start = resultMd.indexOf("[");
			String resultText = start > 0 ? resultMd.substring(start) : resultMd;

			int end = resultText.lastIndexOf("]");
			if (end >= 0) {
				resultText = resultText.substring(0, end + 1);
			}

			return JSON.parseArray(resultText, String.class);
		} catch (Exception e) {
			throw new ServiceException(resultMd);
		}

	}

	/**
	 * 形成智能主播的知识库   智能商品摘要
	 * 
	 * @return 知识库JSON
	 */
	public static JSONObject productDesc(String desc) throws Exception {
		ApplicationParam param = ApplicationParam.builder()
				.apiKey("sk-d8ba4f561bbd49b9b938d7127f8e8bcb")
				.appId("6f6e2f2da3be4272a12e6b0df5fad448")
				.prompt(desc)
				.build();
		Application application = new Application();
		ApplicationResult result = application.call(param);
		String resultMd = result.getOutput().getText();
		int start = resultMd.indexOf("```json");
		String resultText = start >= 0 ? resultMd.substring(start + 7) : resultMd;

		int end = resultText.lastIndexOf("```");
		if (end >= 0) {
			resultText = resultText.substring(0, end - 1);
		}
		// 解析返回的 JSON 数据
		JSONObject finalKnowledgeBase = JSON.parseObject(resultText);
		// 返回解析后的 JSON 对象
		return finalKnowledgeBase;
	}

	/**
	 * 根据商品简介回复问题
	 * 
	 * @param desc     知识库JSON
	 * @param question 弹幕列表
	 * @return 回复文本   智能主播带货  794141c4dc4a44fea26ef588bdd0886d
	 */
	public static String productDescQa(JSONObject desc, List<String> question, List<String> historyReples)
			throws Exception {
		desc.put("当前时间", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
		desc.put("弹幕列表", question);
		desc.put("历史回复", historyReples);
		ApplicationParam param = ApplicationParam.builder()
				.apiKey("sk-d8ba4f561bbd49b9b938d7127f8e8bcb")
				.appId("a42262e5db00425eb944749019ff64f9")
				.prompt(desc.toString())
				.build();
		Application application = new Application();
		ApplicationResult result = application.call(param);
		String resultMd = result.getOutput().getText();
		return resultMd;
	}

	/**
	 * 根据商品简介回复问题（异步）
	 * 
	 * @param desc     知识库JSON
	 * @param question 弹幕列表
	 * @return 回复文本  智能主播带货  794141c4dc4a44fea26ef588bdd0886d
	 */
	public static Flowable<String> productDescQaAsync(JSONObject desc, List<String> question,
			List<String> historyReples) throws Exception {
		desc.put("当前时间", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
		desc.put("弹幕列表", question);
		desc.put("历史回复", historyReples);
		ApplicationParam param = ApplicationParam.builder()
				.apiKey("sk-d8ba4f561bbd49b9b938d7127f8e8bcb")
				.appId("a42262e5db00425eb944749019ff64f9")
				.prompt(desc.toString())
				.incrementalOutput(true)
				.build();
		Application application = new Application();
		Flowable<ApplicationResult> result = application.streamCall(param);
		return result.map(item -> item.getOutput().getText());
	}

}
