package com.ruoyi.platform.domain;

import java.util.Date;

import org.springframework.web.bind.annotation.GetMapping;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.mybatis.annotation.Column;
import com.ruoyi.mybatis.annotation.ColumnMap;
import com.ruoyi.mybatis.annotation.JoinTable;
import com.ruoyi.mybatis.annotation.Query;
import com.ruoyi.mybatis.annotation.Table;
import com.ruoyi.mybatis.enums.QueryEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文案对象 platform_article
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Schema(description = "文案对象")
@Table(name = "platform_article", orderBy = "article_id desc", dataScope = true)
@JoinTable(sourceName = "u", sourceKey = "dept_id", targetKey = "dept_id", targetName = "sys_dept", targetAlias = "d")
@JoinTable(sourceKey = "create_by", targetKey = "user_name", targetName = "sys_user", targetAlias = "u", num = 1)
@Data
@EqualsAndHashCode(callSuper = true)
public class PlatformArticle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ColumnMap(name = "user_id", target = "u")
    private Long userId;

    @ColumnMap(name = "dept_id", target = "d")
    private Long deptId;

    /** 主键 */
    @Schema(title = "主键")
    @Column(name = "article_id", primaryKey = true)
    private Long articleId;

    /** 分类ID */
    @Schema(title = "分类ID")
    @Excel(name = "分类ID")
    @NotNull(message = "分类ID不能为空", groups = { GetMapping.class })
    @Column(name = "category_id")
    @Query(operation = QueryEnum.eq)
    private Long categoryId;

    /** 内容 */
    @Schema(title = "内容")
    @Excel(name = "内容")
    @NotBlank(message = "文案内容不能为空")
    @Column(name = "content")
    @Query(operation = QueryEnum.like)
    private String content;

    /** 创建者 */
    @Schema(title = "创建者")
    @Column(name = "create_by")
    private String createBy;

    /** 创建时间 */
    @Schema(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private Date createTime;

    /** 更新者 */
    @Schema(title = "更新者")
    @Column(name = "update_by")
    private String updateBy;

    /** 更新时间 */
    @Schema(title = "更新时间")
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    @Schema(title = "备注")
    @Column(name = "remark")
    private String remark;
}
