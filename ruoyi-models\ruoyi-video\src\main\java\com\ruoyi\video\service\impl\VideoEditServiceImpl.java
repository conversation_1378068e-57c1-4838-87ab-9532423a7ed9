package com.ruoyi.video.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.service.IVideoEditService;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能媒体服务(ICE)视频编辑 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class VideoEditServiceImpl implements IVideoEditService {

    @Autowired
    private IAcsClient acsClient;

    @Autowired
    private IceClientAKConfig iceConfig;
    
    // 服务版本常量
    private static final String API_VERSION = "2020-11-09";

    @Override
    public String ListPublicMediaBasicInfos(String mediaTagId, String nextToken, Integer maxResults, Integer pageNO,
        Integer pageSize, Boolean includeFileBasicInfo, String businessType) throws Exception {
        validateAcsClient();
        CommonRequest request = createCommonRequest("ListPublicMediaBasicInfos", MethodType.GET);
        addParameterIfNotNull(request, "MediaTagId", mediaTagId);
        addParameterIfNotNull(request, "NextToken", nextToken);
        addParameterIfNotNull(request, "MaxResults", maxResults);
        addParameterIfNotNull(request, "PageNO", pageNO);
        addParameterIfNotNull(request, "PageSize", pageSize);
        addParameterIfNotNull(request, "BusinessType", businessType);
        addParameterIfNotNull(request, "IncludeFileBasicInfo", includeFileBasicInfo);
        return executeRequest(request);
    }

    @Override
    public String ListAllPublicMediaTags(String businessType, String entityId) throws Exception {
        validateAcsClient();
        
        CommonRequest request = createCommonRequest("ListAllPublicMediaTags", MethodType.GET);
        addParameterIfNotNull(request, "BusinessType", businessType);
        addParameterIfNotNull(request, "EntityId", entityId);
        
        return executeRequest(request);
    }
    
    /**
     * 验证AcsClient是否初始化
     */
    private void validateAcsClient() {
        if (acsClient == null) {
            log.error("IAcsClient 未初始化，请检查您的配置。");
            throw new IllegalStateException("IAcsClient 未初始化，请检查配置中的阿里云访问密钥(AccessKey ID/Secret)和地区(Region)设置。");
        }
    }
    
    /**
     * 创建通用请求对象
     */
    private CommonRequest createCommonRequest(String action, MethodType method) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(method);
        request.setSysDomain(iceConfig.getEndpoint());
        request.setSysVersion(API_VERSION);
        request.setSysAction(action);
        return request;
    }
    
    /**
     * 添加非空参数到请求
     */
    private void addParameterIfNotNull(CommonRequest request, String key, Object value) {
        if (value != null) {
            if (value instanceof String && ((String) value).isEmpty()) {
                return;
            }
            request.putQueryParameter(key, value.toString());
        }
    }
    
    /**
     * 执行请求并处理响应
     */
    private String executeRequest(CommonRequest request) throws Exception {
        CommonResponse response = acsClient.getCommonResponse(request);
        String data = response.getData();
        
        if (response.getHttpStatus() != 200) {
            log.error("请求失败，HTTP状态码: {}, 响应数据: {}", response.getHttpStatus(), data);
            throw new Exception("请求失败: " + data);
        }
        
        return data;
    }
}