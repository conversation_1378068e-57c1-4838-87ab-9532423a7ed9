package com.ruoyi.platform.mapper;

import java.util.List;

import com.ruoyi.platform.domain.PlatformKeyword;

/**
 * 关键词管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface PlatformKeywordMapper 
{
    /**
     * 查询关键词管理
     * 
     * @param keywordId 关键词管理主键
     * @return 关键词管理
     */
    public PlatformKeyword selectPlatformKeywordByKeywordId(Long keywordId);
    public List<PlatformKeyword> selectPlatformKeywordByCategoryIds(List<Long> categoryIds);
    /**
     * 查询关键词管理列表
     * 
     * @param platformKeyword 关键词管理
     * @return 关键词管理集合
     */
    public List<PlatformKeyword> selectPlatformKeywordList(PlatformKeyword platformKeyword);

    /**
     * 新增关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    public int insertPlatformKeyword(PlatformKeyword platformKeyword);

    /**
     * 修改关键词管理
     * 
     * @param platformKeyword 关键词管理
     * @return 结果
     */
    public int updatePlatformKeyword(PlatformKeyword platformKeyword);

    /**
     * 删除关键词管理
     * 
     * @param keywordId 关键词管理主键
     * @return 结果
     */
    public int deletePlatformKeywordByKeywordId(Long keywordId);

    /**
     * 批量删除关键词管理
     * 
     * @param keywordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformKeywordByKeywordIds(Long[] keywordIds);

    //根据直播Id获取关键词列表
    public List<PlatformKeyword> getKeywordsByLiveId(Long liveId);
}
