package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户算力点数对象 platform_hashrate
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Schema(description = "用户算力点数对象")
public class PlatformHashrate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long hashrateId;

    /** 关联用户Id */
    @Schema(title = "用户名称")
    @Excel(name = "用户名称")
    private String userName;

    /** 算力余额 */
    @Schema(title = "算力余额")
    @Excel(name = "算力余额")
    private String hashrateBalance;

    /** 算力卡号 */
    @Schema(title = "算力卡号")
    @Excel(name = "算力卡号")
    private String hashrateCard;

    /** 算力卡状态 */
    @Schema(title = "算力卡状态")
    @Excel(name = "算力卡状态")
    private String hashrateStatus; 

    /** 充值余额 */
    @TableField(exist = false)
    private String rechargedHashrate;
    
    public String getRechargedHashrate() {
        return rechargedHashrate;
    }

    public void setRechargedHashrate(String rechargedHashrate) {
        this.rechargedHashrate = rechargedHashrate;
    }

    public void setHashrateId(Long hashrateId) 
    {
        this.hashrateId = hashrateId;
    }

    public Long getHashrateId() 
    {
        return hashrateId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setHashrateBalance(String hashrateBalance) 
    {
        this.hashrateBalance = hashrateBalance;
    }

    public String getHashrateBalance() 
    {
        return hashrateBalance;
    }

    public String getHashrateCard() {
        return hashrateCard;
    }

    public void setHashrateCard(String hashrateCard) {
        this.hashrateCard = hashrateCard;
    }

    public String getHashrateStatus() {
        return hashrateStatus;
    }

    public void setHashrateStatus(String hashrateStatus) {
        this.hashrateStatus = hashrateStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("hashrateId", getHashrateId())
            .append("userName", getUserName())
            .append("hashrateBalance", getHashrateBalance())
            .append("getHashrateCard", getHashrateCard())
            .append("getHashrateStatus", getHashrateStatus())
            .append("rechargedHashrate", getRechargedHashrate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
