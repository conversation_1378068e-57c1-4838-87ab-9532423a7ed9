package com.ruoyi.platform.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformCategory;
import com.ruoyi.platform.mapper.PlatformArticleMapper;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformCategoryMapper;
import com.ruoyi.platform.service.IPlatformCategoryService;

/**
 * 分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Service
public class PlatformCategoryServiceImpl implements IPlatformCategoryService {
    //记录日志
    private static final Logger logger = LoggerFactory.getLogger(PlatformCategoryServiceImpl.class);
    
    @Autowired
    private PlatformCategoryMapper platformCategoryMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private PlatformArticleMapper platformArticleMapper;

    /**
     * 查询分类
     * 
     * @param categoryId 分类主键
     * @return 分类
     */
    @Override
    public PlatformCategory selectPlatformCategoryByCategoryId(Long categoryId) {
        return platformCategoryMapper.selectPlatformCategoryByCategoryId(categoryId);
    }

    /**
     * 查询分类列表
     * 
     * @param platformCategory 分类
     * @return 分类
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformCategory> selectPlatformCategoryList(PlatformCategory platformCategory) {
        return platformCategoryMapper.selectPlatformCategoryList(platformCategory);
    }

    /**
     * 新增分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    @Override
    public int insertPlatformCategory(PlatformCategory platformCategory) {
        platformCategory.setCreateTime(DateUtils.getNowDate());
        platformCategory.setUpdateTime(DateUtils.getNowDate());
        return platformCategoryMapper.insertPlatformCategory(platformCategory);
    }

    /**
     * 修改分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    @Override
    public int updatePlatformCategory(PlatformCategory platformCategory) {
        platformCategory.setUpdateTime(DateUtils.getNowDate());
        return platformCategoryMapper.updatePlatformCategory(platformCategory);
    }

    /**
     * 删除分类信息
     * 
     * @param categoryId 分类主键
     * @return 结果
     * @throws Exception 
    */
    @Transactional
    @Override
    public int deletePlatformCategoryByCategoryId(Long categoryId, Map<String, Object> params) throws Exception {
        // 获取是否强制删除的标志，默认为false
        boolean isForceDelete = Boolean.parseBoolean(String.valueOf(params.getOrDefault("isForceDelete", "false")));
        // 查找一下删除当前的数据
        PlatformCategory category = platformCategoryMapper.selectPlatformCategoryByCategoryId(categoryId);
        if (category == null) {
            throw new ServiceException("未找到要删除的分类！");
        }
        // 如果不是强制删除，检查分类下是否有相关数据
        if (!isForceDelete) {
            if (checkProjectHasCategory(categoryId)) {
                throw new ServiceException("该分类下还有文案，无法删除，请先删除文案数据！");
            }
            if (checkProjectKeyWord(categoryId)) {
                throw new ServiceException("该分类下还有关键词，无法删除，请先删除关键词数据！");
            }
            if (checkProjectAudio(categoryId)) {
                throw new ServiceException("该分类下还有音频，无法删除，请先删除音频数据！");
            }
        }
        // 强制删除时，先删除和分类相互关联的数据
        if (isForceDelete) {
            // 查询与分类相关的音频数据
            List<PlatformAudio> audioList = platformAudioMapper.selectConstraintAudioCategoryId(categoryId);
            if (audioList != null && !audioList.isEmpty()) {
                for (PlatformAudio audio : audioList) {
                    String fileUrl = audio.getAudioPath(); // 获取存储的音频数据
                    if (StringUtils.isNotEmpty(fileUrl)) {
                        // 尝试删除MinIO文件
                        boolean deleteSuccess = FileOperateUtils.deleteFile(fileUrl); // 删除 oss 文件
                        if (deleteSuccess) {
                            logger.info("删除音频文件成功: {}", fileUrl); // 输出成功信息
                        } else {
                            logger.error("删除音频文件失败，路径: {}", fileUrl);
                        }
                    } else {
                        logger.warn("音频路径为空，无法删除音频文件。");
                    }
                }
                // 删除与分类相关的音频
                platformAudioMapper.deleteConstraintAudioCategoryId(categoryId);
            }
            // 删除与分类相关的文案和关键词数据
            platformArticleMapper.deleteConstraintArticleAndKeywordCategoryId(categoryId);
        }
        // 分类相关的数据删除完成之后再删除分类
        return platformCategoryMapper.deletePlatformCategoryByCategoryId(categoryId);
    }

    /**
     * 检查分类是否还有文案
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public boolean checkProjectHasCategory(Long categoryId) {
        return platformCategoryMapper.checkProjectHasCategory(categoryId) > 0;
    }

    /**
     * 检查分类是否还有关键字
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    @Override
    public boolean checkProjectKeyWord(Long categoryId) {
        return platformCategoryMapper.checkProjectKeyWord(categoryId)>0;
    }

    /**
     * 检查分类是否还有音频
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    @Override
    public boolean checkProjectAudio(Long categoryId) {
        return platformCategoryMapper.checkProjectAudio(categoryId)>0;
    }

    //根据分类的ID查找多个分类名称
    @Override
    public Map<Long, String> getCategoryTitlesByIds(int[] ids) {
        List<PlatformCategory> categories = platformCategoryMapper.getCategoriesByIds(ids);
        Map<Long, String> resultMap = new HashMap<>();
        for (PlatformCategory category : categories) {
            resultMap.put(category.getCategoryId(), category.getCategoryTitle());
        }
        return resultMap;
    }

    /**
     * 根据直播ID获取分类信息
     *
     * @param liveId 直播ID
     * @return 分类信息列表
     */
    @Override
    public List<PlatformCategory> getCategoriesByLiveId(Long liveId){
        return platformCategoryMapper.getCategoriesByLiveId(liveId);
    }
}

