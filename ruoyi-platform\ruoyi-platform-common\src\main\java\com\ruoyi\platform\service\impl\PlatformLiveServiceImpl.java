package com.ruoyi.platform.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformGoods;
import com.ruoyi.platform.domain.PlatformLive;
import com.ruoyi.platform.domain.PlatformScenecon;
import com.ruoyi.platform.domain.vo.PlatformLiveVo;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformGoodsMapper;
import com.ruoyi.platform.mapper.PlatformLiveMapper;
import com.ruoyi.platform.mapper.PlatformSceneconMapper;
import com.ruoyi.platform.service.IPlatformLiveService;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 直播管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-08
 */
@Service
public class PlatformLiveServiceImpl implements IPlatformLiveService {
    @Autowired
    private PlatformLiveMapper platformLiveMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private PlatformGoodsMapper platformGoodsMapper;

    @Autowired
    private PlatformSceneconMapper platformSceneconMapper;

    /**
     * 查询直播管理
     * 
     * @param liveId 直播管理主键
     * @return 直播管理
     */
    @Override
    public PlatformLive selectPlatformLiveByLiveId(Long liveId) {
        return platformLiveMapper.selectPlatformLiveByLiveId(liveId);
    }

    @Override
    public PlatformLiveVo selectPlatformLiveVoByLiveId(Long liveId) {
        PlatformLive platformLive = platformLiveMapper.selectPlatformLiveByLiveId(liveId);
        PlatformLiveVo platformLiveVo = PlatformLiveVo.valueOf(platformLive);
        if (platformLive.getSceneconId() != null) {
            platformLiveVo.setScenecon(
                    platformSceneconMapper.selectPlatformSceneconBySceneconId(platformLive.getSceneconId()));
        }
        if (platformLive.getGoodsId() != null) {
            platformLiveVo.setGoods(platformGoodsMapper.selectPlatformGoodsByGoodsIds(platformLive.getGoodsId()));
        }

        return platformLiveVo;
    }

    /**
     * 查询直播管理列表
     * 
     * @param platformLive 直播管理
     * @return 直播管理
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformLive> selectPlatformLiveList(PlatformLive platformLive) {
        return platformLiveMapper.selectPlatformLiveList(platformLive);
    }

    /**
     * 新增直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    @Override
    public int insertPlatformLive(PlatformLive platformLive) {
        platformLive.setCreateTime(DateUtils.getNowDate());
        platformLive.setUpdateTime(DateUtils.getNowDate());
        return platformLiveMapper.insertPlatformLive(platformLive);
    }

    /**
     * 修改直播管理
     * 
     * @param platformLive 直播管理
     * @return 结果
     */
    @Override
    public int updatePlatformLive(PlatformLive platformLive) {
        platformLive.setUpdateTime(DateUtils.getNowDate());
        return platformLiveMapper.updatePlatformLive(platformLive);
    }

    /**
     * 批量删除直播管理
     * 
     * @param liveIds 需要删除的直播管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformLiveByLiveIds(Long[] liveIds) {
        return platformLiveMapper.deletePlatformLiveByLiveIds(liveIds);
    }

    /**
     * 删除直播管理信息
     * 
     * @param liveId 直播管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformLiveByLiveId(Long liveId) {
        return platformLiveMapper.deletePlatformLiveByLiveId(liveId);
    }

    /**
     * 查询直播相关音频列表
     */
    @Override
    public Map<Long, List<Long>> selectAudioList(Long liveId) {
        PlatformLive platformLive = platformLiveMapper.selectPlatformLiveByLiveId(liveId);
        PlatformScenecon platformScenecon = platformSceneconMapper
                .selectPlatformSceneconBySceneconId(platformLive.getSceneconId());
        List<PlatformGoods> platformGoods = platformGoodsMapper.getGoodsByIds(platformLive.getGoodsId());
        List<List<Long>> categoryIdsList = new ArrayList<>();
        categoryIdsList.add(platformScenecon.getSceneconInteractionId());
        categoryIdsList.add(platformScenecon.getSceneconQuestionsId());
        platformGoods.forEach(i -> {
            categoryIdsList.add(i.getGoodsQuestionsId());
            categoryIdsList.add(i.getGoodsInteractionId());
        });
        List<Long> categoryIds = categoryIdsList.stream()
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        List<PlatformAudio> platformAudios = platformAudioMapper
                .selectPlatformAudioListFroCategoryIds(categoryIds);

        Map<Long, List<Long>> mapList = new HashMap<>();
        categoryIds.forEach(i -> mapList.put(i, new ArrayList<>()));
        platformAudios.forEach(i -> mapList.get(i.getCategoryId()).add(i.getAudioId()));
        return mapList;
    }

    /**
     * 根据创建者查询直播
     * 
     * @param createBy 直播管理主键
     * @return 直播管理
     */
    @Override
    public List<PlatformLive> selectPlatformLiveBycreateBy(String createBy) {
        return platformLiveMapper.selectPlatformLiveBycreateBy(createBy);
    }

    //修改人
    @Override
    public List<PlatformLive> selectPlatformLiveByupdateBy(String updateBy) {
        return platformLiveMapper.selectPlatformLiveByupdateBy(updateBy);
    }

    /**
     * 检查场控是否被直播使用.
     */
    @Override
    public boolean isSceneconUsed(Long sceneconId) {
        return platformLiveMapper.countLiveBySceneconId(sceneconId) > 0;
    }

    /**
     * 检查产品是否被直播使用.
     */
    @Override
    public boolean isGoodsUsed(Long goodsId) {
        return platformLiveMapper.countLiveByGoodsId(goodsId) > 0;
    }

    /**
     * 根据直播Id打包分类下音频，支持传递特定音频ID数组以限制打包范围。
     */
    @Override
    public void downloadAllAudiosByLiveId(Long liveId, HttpServletResponse response, Long[] audios) throws Exception {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {
            
            // 获取要下载的音频ID集合
            Map<Long, List<Long>> audioLists = selectAudioList(liveId);
            Set<Long> uniqueAudioIds = audioLists.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
                    
            if (audios != null && audios.length > 0) {
                uniqueAudioIds = new HashSet<>(Arrays.asList(audios));
            }
            
            if (uniqueAudioIds.isEmpty()) {
                throw new ServiceException("未找到可下载的音频文件");
            }

            // 收集不存在的文件ID
            Set<Long> notFoundIds = new HashSet<>();
            // 记录成功下载数量
            int successCount = 0;

            // 首先检查所有文件是否存在
            for (Long audioId : uniqueAudioIds) {
                PlatformAudio audio = platformAudioMapper.selectPlatformAudioByAudioId(audioId);
                if (audio == null || StringUtils.isEmpty(audio.getAudioPath())) {
                    notFoundIds.add(audioId);
                }
            }

            // 如果有文件不存在，直接抛出异常
            if (!notFoundIds.isEmpty()) {
                String ids = notFoundIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(", "));
                throw new ServiceException("以下音频文件在服务器上未找到: " + ids);
            }

            // 开始下载存在的文件
            for (Long audioId : uniqueAudioIds) {
                PlatformAudio audio = platformAudioMapper.selectPlatformAudioByAudioId(audioId);
                try (ByteArrayOutputStream audioBaos = new ByteArrayOutputStream()) {
                    FileOperateUtils.downLoad(audio.getAudioPath(), audioBaos);
                    byte[] audioBytes = audioBaos.toByteArray();
                    if (audioBytes != null && audioBytes.length > 0) {
                        String fileName = audioId + ".mp3";
                        ZipEntry entry = new ZipEntry(fileName);
                        zos.putNextEntry(entry);
                        zos.write(audioBytes);
                        zos.closeEntry();
                        successCount++;
                    }
                } catch (Exception e) {
                    // 下载过程中出现异常，忽略并继续下载其他文件
                    continue;
                }
            }

            if (successCount == 0) {
                throw new ServiceException("所有音频文件下载失败，请稍后重试");
            }

            zos.finish();
            
            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=live_" + liveId + "_audios.zip");
            response.setContentLength(baos.size());
            
            // 写入响应流
            try (OutputStream out = response.getOutputStream()) {
                baos.writeTo(out);
                out.flush();
            }
            
        } catch (IOException e) {
            throw new ServiceException("打包下载失败：" + e.getMessage());
        }
    }

    //根据直播id查询信息
    @Override
    public List<PlatformLive> selectLive(Long liveId){
        return platformLiveMapper.selectLive(liveId);
    }
}
