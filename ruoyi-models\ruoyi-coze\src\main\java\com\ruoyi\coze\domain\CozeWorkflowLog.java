package com.ruoyi.coze.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * CozeWorkflowLog对象 coze_workflow_log
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Schema(description = "CozeWorkflowLog对象")
public class CozeWorkflowLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 工作流主键 */
    @Schema(title = "工作流主键")
    private Long id;

    /** 工作流编号 */
    @Schema(title = "工作流编号")
    @Excel(name = "工作流编号")
    private String workflowId;

    /** 工作流名称 */
    @Schema(title = "工作流名称")
    @Excel(name = "工作流名称")
    private String name;

    /** 执行编号 */
    @Schema(title = "执行编号")
    @Excel(name = "执行编号")
    private String executeId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExecuteId() {
        return executeId;
    }

    public void setExecuteId(String executeId) {
        this.executeId = executeId;
    }

    public String getInputParam() {
        return inputParam;
    }

    public void setInputParam(String inputParam) {
        this.inputParam = inputParam;
    }


    public String getOutputParam() {
        return outputParam;
    }

    public void setOutputParam(String outputParam) {
        this.outputParam = outputParam;
    }

    /** 输入参数 */
    @Schema(title = "输入参数")
    @Excel(name = "输入参数")
    private String inputParam;

    /** 输出数据 */
    @Schema(title = "输出数据")
    @Excel(name = "输出数据")
    private String outputParam;

}
