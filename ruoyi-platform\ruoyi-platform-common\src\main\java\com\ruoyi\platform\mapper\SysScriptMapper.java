package com.ruoyi.platform.mapper;

import java.util.List;

import com.ruoyi.platform.domain.SysScript;

/**
 * 脚本Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
public interface SysScriptMapper 
{
    /**
     * 查询脚本
     * 
     * @param scriptId 脚本主键
     * @return 脚本
     */
    public SysScript selectSysScriptByScriptId(Long scriptId);

    /**
     * 查询脚本列表
     * 
     * @param sysScript 脚本
     * @return 脚本集合
     */
    public List<SysScript> selectSysScriptList(SysScript sysScript);

    /**
     * 新增脚本
     * 
     * @param sysScript 脚本
     * @return 结果
     */
    public int insertSysScript(SysScript sysScript);

    /**
     * 修改脚本
     * 
     * @param sysScript 脚本
     * @return 结果
     */
    public int updateSysScript(SysScript sysScript);

    /**
     * 删除脚本
     * 
     * @param scriptId 脚本主键
     * @return 结果
     */
    public int deleteSysScriptByScriptId(Long scriptId);

    /**
     * 批量删除脚本
     * 
     * @param scriptIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysScriptByScriptIds(Long[] scriptIds);
}
