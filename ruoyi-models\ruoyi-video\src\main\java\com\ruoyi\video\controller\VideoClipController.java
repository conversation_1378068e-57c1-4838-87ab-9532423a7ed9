package com.ruoyi.video.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.aliyun.ice20201109.Client;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.ruoyi.common.annotation.Anonymous;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 处理前端对于openAPI的调用
 * <AUTHOR>
 * @date 2021/6/16
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/open")
public class VideoClipController {

    private static final Logger logger = LoggerFactory.getLogger(VideoClipController.class);

    private Client iceClient;

    @Value("${aliyun.ice.access-key-id:}")
    private String accessKeyId;

    @Value("${aliyun.ice.access-key-secret:}")
    private String accessKeySecret;

    @Value("${aliyun.ice.endpoint:ice.cn-beijing.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.ice.region-id:cn-beijing}")
    private String regionId;

    @PostConstruct
    public void init() {
        if (accessKeyId.isEmpty() || accessKeySecret.isEmpty()) {
            logger.error("阿里云 ICE 配置缺失，请检查 application.yml 中的 aliyun.ice.access-key-id 和 aliyun.ice.access-key-secret 配置");
            throw new IllegalStateException("阿里云 ICE 配置缺失");
        }

        Config config = new Config();
        config.accessKeyId = accessKeyId;
        config.accessKeySecret = accessKeySecret;
        config.endpoint = endpoint;
        config.regionId = regionId;

        try {
            iceClient = new Client(config);
            logger.info("Initialize client for Service: ice, endpoint: {}", endpoint);
        } catch (Exception e) {
            logger.error("初始化阿里云 ICE 客户端失败", e);
            throw new RuntimeException("初始化阿里云 ICE 客户端失败", e);
        }
    }

    /**
     * 处理前端发送的post请求
     * @param httpRequest
     * @return
     * @throws Exception
     */
    @Anonymous
    @RequestMapping("/openApiPost")
    public @ResponseBody Object handlerPostOpenApiRequest(@RequestBody Map<String, Object> httpRequest) throws Exception {
        // 提取 Action 字段
        Object actionObj = httpRequest.remove("Action");
        if (actionObj == null) {
            throw new IllegalArgumentException("请求参数中必须包含 'Action'");
        }
        String action = actionObj.toString();
        // 构造阿里云 OpenAPI 请求对象
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", httpRequest)}));
        Map<String, ?> response = iceClient.doRPCRequest(action, "2020-11-09", "HTTPS", "POST","AK", "json", req, runtime);
        return response.get("body");
    }

    /**
     * 处理前端发送的get请求
     * @param httpRequest
     * @return              
     * @throws Exception
     */
    @Anonymous
    @RequestMapping("/openApi")
    public @ResponseBody Object handlerOpenApiRequest(HttpServletRequest httpRequest) throws Exception {
        String action = httpRequest.getParameter("Action");
        Map<String, String[]> paramMap = httpRequest.getParameterMap();
        Map<String, String> queryMap = new HashMap<>();
        for (String key : paramMap.keySet()) {
            queryMap.put(key, httpRequest.getParameter(key));
        }
        queryMap.remove("Action");
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("query", queryMap)}));
        Map<String, ?> response2 = iceClient.doRPCRequest(action, "2020-11-09", "HTTPS", "GET", "AK",
            "json",req, runtime);
        return response2.get("body");

    }

    /**
     * 健康检查，系统部署需要
     * 请不要删除！！
     */
    @GetMapping("/checkpreload.htm")
    public @ResponseBody String checkPreload() {
        return "success";
    }
}
