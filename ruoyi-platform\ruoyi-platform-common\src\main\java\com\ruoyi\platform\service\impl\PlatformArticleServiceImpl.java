package com.ruoyi.platform.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.domain.PlatformCategory;
import com.ruoyi.platform.mapper.PlatformArticleMapper;
import com.ruoyi.platform.mapper.PlatformCategoryMapper;
import com.ruoyi.platform.mapper.PlatformProjectMapper;
import com.ruoyi.platform.service.IPlatformArticleService;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 文案Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Service
public class PlatformArticleServiceImpl implements IPlatformArticleService 
{
    @Autowired
    private PlatformArticleMapper platformArticleMapper;

    @Autowired
    @Lazy
    private IPlatformArticleService platformArticleService;

    @Autowired
    private PlatformProjectMapper platformProjectMapper;

    @Autowired
    private PlatformCategoryMapper platformCategoryMapper;

    /**
     * 查询文案
     * 
     * @param articleId 文案主键
     * @return 文案
     */
    @Override
    public PlatformArticle selectPlatformArticleByArticleId(Long articleId)
    {
        return platformArticleMapper.selectPlatformArticleByArticleId(articleId);
    }

    /**
     * 查询文案列表
     * 
     * @param platformArticle 文案
     * @return 文案
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformArticle> selectPlatformArticleList(PlatformArticle platformArticle)
    {
        return platformArticleMapper.selectPlatformArticleList(platformArticle);
    }

    /**
     * 新增文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    @Override
    public int insertPlatformArticle(PlatformArticle platformArticle)
    {
        platformArticle.setCreateTime(DateUtils.getNowDate());
        platformArticle.setUpdateTime(DateUtils.getNowDate());
        return platformArticleMapper.insertPlatformArticle(platformArticle);
    }

    /**
     * 修改文案
     * 
     * @param platformArticle 文案
     * @return 结果
     */
    @Override
    public int updatePlatformArticle(PlatformArticle platformArticle)
    {
        platformArticle.setUpdateTime(DateUtils.getNowDate());
        return platformArticleMapper.updatePlatformArticle(platformArticle);
    }

    /**
     * 批量删除文案
     * 
     * @param articleIds 需要删除的文案主键
     * @return 结果
     */
    @Override
    public int deletePlatformArticleByArticleIds(Long[] articleIds)
    {
        return platformArticleMapper.deletePlatformArticleByArticleIds(articleIds);
    }

    /**
     * 删除文案信息
     * 
     * @param articleId 文案主键
     * @return 结果
     */
    @Override
    public int deletePlatformArticleByArticleId(Long articleId)
    {
        return platformArticleMapper.deletePlatformArticleByArticleId(articleId);
    }

    //获取每个分类下的所有文章
    @Override
    public Map<Long, List<PlatformArticle>> getArticlesByCategory(List<Long> categories) {
        List<PlatformArticle> articles = platformArticleMapper.selectByCategoryIdList(categories);
        Map<Long, List<PlatformArticle>> articlesByCategory = new HashMap<>();
        
        for (PlatformArticle article : articles) {
            List<PlatformArticle> list = articlesByCategory.computeIfAbsent(article.getCategoryId(), k -> new ArrayList<>());
            list.add(article);
        }
        return articlesByCategory;
    }

    //导出当前项目文案数据txt格式
    @Override
    public void exportTxt(Long projectId, HttpServletResponse response) {
        try {
            //查询项目下的多个分类数据
            List<Long> categoryIds = platformProjectMapper.findCategoryIdsByProjectId(projectId);
            // 检查是否有有效的分类
            if (categoryIds.isEmpty()) {
                throw new ServiceException("当前项目没有有效的分类，无法导出数据!");
            }
            // 获取每个分类下的所有文章
            Map<Long, List<PlatformArticle>> articlesByCategory = platformArticleService.getArticlesByCategory(categoryIds);
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=文案信息.txt");
            // 创建输出流
            OutputStream outputStream = response.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8));
            // 输出数据
            for (Map.Entry<Long, List<PlatformArticle>> entry : articlesByCategory.entrySet()) {
                Long categoryId = entry.getKey();
                // 查询分类
                PlatformCategory category = platformCategoryMapper.selectPlatformCategoryByCategoryId(categoryId);
                if (category != null) {
                    writer.println("分类名称："+category.getCategoryTitle());
                } else {
                    writer.println("分类名称：未知");
                }
                for (PlatformArticle article : entry.getValue()) {
                    writer.println(article.getContent());
                }
                // 在每个分类数据结束后添加一个换行符
                writer.println("");
            }
            // 刷新并关闭流
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    //批量新增文案
    @Override
    public int batchInsertPlatformArticle(List<PlatformArticle> articles) {
        for (PlatformArticle article : articles) {
            article.setCreateBy(SecurityUtils.getUsername());
            article.setUpdateBy(SecurityUtils.getUsername());
        }
        return platformArticleMapper.batchInsertPlatformArticle(articles);
    }



    /**
     * 根据直播ID获取关键词列表
     *
     * @param liveId 直播ID
     * @return 关键词列表
     */
    @Override
    public List<PlatformArticle> getArticlesByLiveId(Long liveId){
        return platformArticleMapper.getArticlesByLiveId(liveId);
    }

    //根据文案Ids查询文案信息
    @Override
    public Map<Long, String> getArticleByIds(List<Long> articleIds){
        List<PlatformArticle> articles = platformArticleMapper.getArticleIds(articleIds);
        Map<Long, String> resultMap = new HashMap<>();
        for (PlatformArticle a : articles) {
            resultMap.put(a.getArticleId(), a.getContent());
        }
        return resultMap;
    }
}
