package com.ruoyi.tingwu.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyuncs.profile.DefaultProfile;

import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.ice")
public class TingWuConfig {

    private String accessKeyId;

    private String accessKeySecret;

    /** 通义管控台创建的appKey */
    private String appKey;

    private String regionId;

    @Bean
    public DefaultProfile defaultProfile() {
        return DefaultProfile.getProfile(
            regionId,
            accessKeyId,
            accessKeySecret);
    }

}
