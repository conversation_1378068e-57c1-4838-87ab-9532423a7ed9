package com.ruoyi.video.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.dto.TemplateRequestDTO;
import com.ruoyi.video.service.ITemplateService;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能媒体服务(ICE)模板管理 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class TemplateServiceImpl implements ITemplateService {

    @Autowired
    private IAcsClient acsClient;

    @Autowired
    private IceClientAKConfig iceConfig;
    
    // 服务版本常量
    private static final String API_VERSION = "2020-11-09";

    @Override
    public String addTemplate(TemplateRequestDTO requestDTO) throws Exception {
        validateAcsClient();
        if (requestDTO == null) {
            throw new ServiceException("模板请求数据不能为空");
        }
        
        CommonRequest request = createCommonRequest("AddTemplate", MethodType.POST);
        addParameterIfNotNull(request, "Name", requestDTO.getName());
        addParameterIfNotNull(request, "Type", requestDTO.getType());
        addParameterIfNotNull(request, "Config", requestDTO.getConfig());
        addParameterIfNotNull(request, "CoverUrl", requestDTO.getCoverUrl());
        addParameterIfNotNull(request, "PreviewMedia", requestDTO.getPreviewMedia());
        addParameterIfNotNull(request, "Status", requestDTO.getStatus());
        addParameterIfNotNull(request, "Source", requestDTO.getSource());
        addParameterIfNotNull(request, "RelatedMediaids", requestDTO.getRelatedMediaids());
        
        return executeRequest(request);
    }

    @Override
    public String listTemplates(Long pageNo, Long pageSize, String type, String status, String createSource, String keyword, String sortType) throws Exception {
        validateAcsClient();
        
        CommonRequest request = createCommonRequest("ListTemplates", MethodType.POST);
        addParameterIfNotNull(request, "PageNo", pageNo);
        addParameterIfNotNull(request, "PageSize", pageSize);
        addParameterIfNotNull(request, "Type", type);
        addParameterIfNotNull(request, "Status", status);
        addParameterIfNotNull(request, "CreateSource", createSource);
        addParameterIfNotNull(request, "Keyword", keyword);
        addParameterIfNotNull(request, "SortType", sortType);
        
        return executeRequest(request);
    }

    @Override
    public String getTemplate(String templateId, String relatedMediaidFlag) throws Exception {
        validateAcsClient();
        if (templateId == null || templateId.trim().isEmpty()) {
            throw new ServiceException("模板ID (TemplateId) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("GetTemplate", MethodType.POST);
        request.putQueryParameter("TemplateId", templateId);
        addParameterIfNotNull(request, "RelatedMediaidFlag", relatedMediaidFlag);
        
        try {
            return executeRequest(request);
        } catch (ClientException e) {
            log.error("获取模板信息失败，错误码: {}, 错误信息: {}", e.getErrCode(), e.getErrMsg());
            switch (e.getErrCode()) {
                case "TemplateNotFound":
                    throw new Exception("指定的模板不存在，请检查模板ID是否正确: " + templateId);
                case "InvalidParameter":
                    throw new Exception("参数无效，请检查模板ID格式是否正确: " + templateId);
                case "Forbidden":
                    throw new Exception("访问被拒绝，请检查阿里云账号权限和区域配置");
                case "InvalidTemplateId":
                    throw new Exception("模板ID格式无效: " + templateId);
                default:
                    throw new Exception("获取模板信息失败: " + e.getErrMsg() + " (错误码: " + e.getErrCode() + ")");
            }
        }
    }

    @Override
    public String updateTemplate(TemplateRequestDTO requestDTO) throws Exception {
        validateAcsClient();
        if (requestDTO == null || requestDTO.getTemplateId() == null || requestDTO.getTemplateId().trim().isEmpty()) {
            throw new ServiceException("模板ID (TemplateId) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("UpdateTemplate", MethodType.POST);
        request.putQueryParameter("TemplateId", requestDTO.getTemplateId());
        addParameterIfNotNull(request, "Name", requestDTO.getName());
        addParameterIfNotNull(request, "Config", requestDTO.getConfig());
        addParameterIfNotNull(request, "CoverUrl", requestDTO.getCoverUrl());
        addParameterIfNotNull(request, "PreviewMedia", requestDTO.getPreviewMedia());
        addParameterIfNotNull(request, "Status", requestDTO.getStatus());
        addParameterIfNotNull(request, "Source", requestDTO.getSource());
        addParameterIfNotNull(request, "RelatedMediaids", requestDTO.getRelatedMediaids());
        
        return executeRequest(request);
    }

    @Override
    public String deleteTemplate(String templateIds) throws Exception {
        validateAcsClient();
        
        CommonRequest request = createCommonRequest("DeleteTemplate", MethodType.GET);
        addParameterIfNotNull(request, "TemplateIds", templateIds);
        
        return executeRequest(request);
    }

    @Override
    public String getTemplateMaterials(String templateId, String fileList) throws Exception {
        validateAcsClient();
        if (templateId == null || templateId.trim().isEmpty()) {
            throw new ServiceException("模板ID (TemplateId) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("GetTemplateMaterials", MethodType.POST);
        request.putQueryParameter("TemplateId", templateId);
        addParameterIfNotNull(request, "FileList", fileList);
        
        try {
            return executeRequest(request);
        } catch (ClientException e) {
            log.error("获取模板素材地址失败，错误码: {}, 错误信息: {}", e.getErrCode(), e.getErrMsg());
            switch (e.getErrCode()) {
                case "SignUrlError":
                    throw new Exception("签名URL错误，请检查阿里云AccessKey配置和权限设置");
                case "TemplateNotFound":
                    throw new Exception("指定的模板不存在，请检查模板ID是否正确: " + templateId);
                case "InvalidParameter":
                    throw new Exception("参数无效，请检查模板ID格式或FileList格式: " + templateId);
                case "Forbidden":
                    throw new Exception("访问被拒绝，请检查阿里云账号权限和区域配置");
                default:
                    throw new Exception("获取模板素材地址失败: " + e.getErrMsg() + " (错误码: " + e.getErrCode() + ")");
            }
        }
    }
    
    /**
     * 验证AcsClient是否初始化
     */
    private void validateAcsClient() {
        if (acsClient == null) {
            throw new IllegalStateException("IAcsClient 未初始化，请检查配置中的阿里云访问密钥(AccessKey ID/Secret)和地区(Region)设置。");
        }
    }
    
    /**
     * 创建通用请求对象
     */
    private CommonRequest createCommonRequest(String action, MethodType method) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(method);
        request.setSysDomain(iceConfig.getEndpoint());
        request.setSysVersion(API_VERSION);
        request.setSysAction(action);
        return request;
    }
    
    /**
     * 添加非空参数到请求
     */
    private void addParameterIfNotNull(CommonRequest request, String key, Object value) {
        if (value != null) {
            request.putQueryParameter(key, value.toString());
        }
    }
    
    /**
     * 执行请求并处理响应
     */
    private String executeRequest(CommonRequest request) throws Exception {
        CommonResponse response = acsClient.getCommonResponse(request);
        String data = response.getData();
        
        if (response.getHttpStatus() != 200) {
            log.error("请求失败，HTTP状态码: {}, 响应数据: {}", response.getHttpStatus(), data);
            throw new Exception("请求失败: " + data);
        }
        
        return data;
    }
}