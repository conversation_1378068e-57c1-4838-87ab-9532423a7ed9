package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformScenecon;
import com.ruoyi.platform.service.IPlatformLiveService;
import com.ruoyi.platform.service.IPlatformSceneconService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 场控管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@RestController
@RequestMapping("/platform/scenecon")
@Tag(name = "【场控管理】管理")
public class PlatformSceneconController extends BaseController
{
    @Autowired
    private IPlatformSceneconService platformSceneconService;

    @Autowired
    private IPlatformLiveService platformLiveService;

    /**
     * 查询场控管理列表
     */
    @Operation(summary = "查询场控管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformScenecon platformScenecon)
    {
        List<PlatformScenecon> list = platformSceneconService.selectPlatformSceneconList(platformScenecon);
        return getDataTable(list);
    }

    /**
     * 导出场控管理列表
     */
    @Operation(summary = "导出场控管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:export')")
    @Log(title = "导出场控管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformScenecon platformScenecon)
    {
        List<PlatformScenecon> list = platformSceneconService.selectPlatformSceneconList(platformScenecon);
        ExcelUtil<PlatformScenecon> util = new ExcelUtil<PlatformScenecon>(PlatformScenecon.class);
        util.exportExcel(response, list, "场控管理数据");
    }

    /**
     * 获取场控管理详细信息
     */
    @Operation(summary = "获取场控管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:query')")
    @GetMapping(value = "/{sceneconId}")
    public AjaxResult getInfo(@PathVariable("sceneconId") Long sceneconId)
    {
        return success(platformSceneconService.selectPlatformSceneconBySceneconId(sceneconId));
    }

    /**
     * 新增场控管理
     */
    @Operation(summary = "新增场控管理")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:add')")
    @Log(title = "新增场控管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformScenecon platformScenecon)
    {
        platformScenecon.setCreateBy(getUsername());
        platformScenecon.setUpdateBy(getUsername());
        return toAjax(platformSceneconService.insertPlatformScenecon(platformScenecon));
    }

    /**
     * 修改场控管理
     */
    @Operation(summary = "修改场控管理")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:edit')")
    @Log(title = "修改场控管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformScenecon platformScenecon)
    {
        platformScenecon.setUpdateBy(getUsername());
        return toAjax(platformSceneconService.updatePlatformScenecon(platformScenecon));
    }

    /**
     * 删除场控管理
     */
    @Operation(summary = "删除场控管理")
    //@PreAuthorize("@ss.hasPermi('platform:scenecon:remove')")
    @Log(title = "删除场控管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sceneconIds}")
    public AjaxResult remove(@PathVariable( name = "sceneconIds" ) Long[] sceneconIds) 
    {
        for (Long sceneconId : sceneconIds) {
            if (platformLiveService.isSceneconUsed(sceneconId)) {
                return AjaxResult.error("该场控已被某场直播使用，不能删除！");
            }
        }
        return toAjax(platformSceneconService.deletePlatformSceneconBySceneconIds(sceneconIds));
    }
}
