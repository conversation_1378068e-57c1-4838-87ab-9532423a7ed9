package com.ruoyi.coze.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.coze.openapi.client.workflows.run.RetrieveRunHistoryReq;
import com.coze.openapi.client.workflows.run.RetrieveRunHistoryResp;
import com.coze.openapi.client.workflows.run.RunWorkflowReq;
import com.coze.openapi.client.workflows.run.RunWorkflowResp;
import com.coze.openapi.client.workflows.run.model.WorkflowExecuteStatus;
import com.coze.openapi.client.workflows.run.model.WorkflowRunHistory;
import com.coze.openapi.service.service.CozeAPI;
import com.ruoyi.coze.config.CozeConfig;
import com.ruoyi.coze.domain.WorkflowRunRequest;
import com.ruoyi.coze.service.ICozeWorkflowService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Coze工作流服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CozeWorkflowServiceImpl implements ICozeWorkflowService {

    private final CozeAPI cozeAPI;

    private final CozeConfig cozeConfig;

    private final CozeWorkflowLogServiceImpl cozeWorkflowLogService;

    /**
     * 执行同步工作流任务
     */
    @Override
    public Map<String, Object> runWorkflow(WorkflowRunRequest request) {
        log.info("开始执行工作流: {}", request.isAsync());
        try {
            // 构建工作流运行请求
            RunWorkflowReq req = RunWorkflowReq.builder()
                    .workflowID(request.getWorkflowId())
                    .parameters(request.getParameters())
                    .isAsync(request.isAsync())
                    .build();

            RunWorkflowResp resp = cozeAPI.workflows().runs().create(req);

            log.info("工作流已启动");
            Long id= cozeWorkflowLogService.saveLogWithInput(request.getWorkflowId(), resp.getExecuteID(), request);

            Map<String, Object> result = new HashMap<>();
            result.put("workflowId", request.getWorkflowId());
            result.put("msg", resp.getMsg());
            result.put("debugUrl", resp.getDebugURL());
            if (request.isAsync()) {
                result.put("executeId", resp.getExecuteID());
            } else {
                result.put("data", resp.getData());
                cozeWorkflowLogService.saveLogWithOutputById(id, result);
            }
            return result;
        } catch (Exception e) {
            log.error("启动工作流失败", e);
            throw new RuntimeException("启动工作流失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询工作流执行状态
     */
    @Override
    public Map<String, Object> getAsyncWorkflowStatus(String workflowId, String executeId) {
        try {
            // 查询执行历史
            RetrieveRunHistoryResp historyResp = cozeAPI.workflows()
                    .runs()
                    .histories()
                    .retrieve(RetrieveRunHistoryReq.of(workflowId, executeId));

            if (historyResp.getHistories().isEmpty()) {
                throw new RuntimeException("未找到执行记录");
            }

            WorkflowRunHistory history = historyResp.getHistories().get(0);

            Map<String, Object> result = new HashMap<>();
            result.put("executeId", executeId);
            result.put("workflowId", workflowId);

            if (history.getExecuteStatus().equals(WorkflowExecuteStatus.FAIL)) {
                result.put("errorMessage", history.getErrorMessage());
                result.put("success", false);
            } else if (history.getExecuteStatus().equals(WorkflowExecuteStatus.RUNNING)) {
                result.put("message", "工作流正在运行中");
                result.put("success", true);
            } else {
                result.put("output", history.getOutput());
                result.put("message", "工作流执行成功");
                result.put("success", true);
            }
            cozeWorkflowLogService.saveLogWithOutput(workflowId, executeId, result);

            return result;
        } catch (Exception e) {
            log.error("查询工作流状态失败", e);
            throw new RuntimeException("查询工作流状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 轮询工作流执行结果（直到完成）
     */
    @Override
    public Map<String, Object> pollAsyncWorkflowResult(String workflowId, String executeId, int pollInterval) {
        try {
            long startTime = System.currentTimeMillis();
            long timeoutMillis = cozeConfig.getPollTimeout() * 1000L;

            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                RetrieveRunHistoryResp historyResp = cozeAPI.workflows()
                        .runs()
                        .histories()
                        .retrieve(RetrieveRunHistoryReq.of(workflowId, executeId));

                if (historyResp.getHistories().isEmpty()) {
                    throw new RuntimeException("未找到执行记录");
                }

                WorkflowRunHistory history = historyResp.getHistories().get(0);

                Map<String, Object> result = new HashMap<>();
                result.put("executeId", executeId);
                result.put("workflowId", workflowId);
                result.put("status", history.getExecuteStatus().toString());

                if (history.getExecuteStatus().equals(WorkflowExecuteStatus.FAIL)) {
                    result.put("errorMessage", history.getErrorMessage());
                    result.put("success", false);
                    cozeWorkflowLogService.saveLogWithOutput(workflowId, executeId, result);
                    return result;
                } else if (history.getExecuteStatus().equals(WorkflowExecuteStatus.RUNNING)) {
                    log.info("工作流仍在运行中，等待 {} 秒后继续轮询...", pollInterval);
                    TimeUnit.SECONDS.sleep(pollInterval);
                } else {
                    result.put("output", history.getOutput());
                    result.put("message", "工作流执行成功");
                    result.put("success", true);
                    cozeWorkflowLogService.saveLogWithOutput(workflowId, executeId, result);
                    return result;
                }
            }

            // 工作流执行时间超过配置设定的时间，返回超时结果
            Map<String, Object> result = new HashMap<>();
            result.put("executeId", executeId);
            result.put("workflowId", workflowId);
            result.put("status", "TIMEOUT");
            result.put("message", "轮询超时，请稍后查询状态");
            result.put("success", true);
            return result;

        } catch (Exception e) {
            log.error("轮询工作流结果失败", e);
            throw new RuntimeException("轮询工作流结果失败: " + e.getMessage(), e);
        }
    }
}