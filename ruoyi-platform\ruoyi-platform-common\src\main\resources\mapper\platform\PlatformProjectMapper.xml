<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformProjectMapper">
    
    <resultMap type="PlatformProject" id="PlatformProjectResult">
        <result property="projectId"    column="project_id"    />
        <result property="projectTitle"    column="project_title"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformProjectVo">
        select project_id, project_title, p.create_by, p.create_time, p.update_by, p.update_time, p.remark from platform_project p
        left join  sys_user u on u.user_name = p.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformProjectList" parameterType="PlatformProject" resultMap="PlatformProjectResult">
        <include refid="selectPlatformProjectVo"/>
        <where>  
            <if test="projectTitle != null  and projectTitle != ''"> and project_title like concat('%', #{projectTitle}, '%')</if>
            ${params.dataScope}
        </where>
        <if test="params.createTimeSort == 1">
            ORDER BY create_time DESC
        </if>
        <if test="params.createTimeSort != 1">
            ORDER BY create_time ASC
        </if>
    </select>
    
    <select id="selectPlatformProjectByProjectId" parameterType="Long" resultMap="PlatformProjectResult">
        <include refid="selectPlatformProjectVo"/>
        where project_id = #{projectId}
    </select>
    
    <select id="selectProjectByLiveId" parameterType="Long" resultMap="PlatformProjectResult">
        SELECT * FROM platform_project WHERE project_id = (
            SELECT project_id FROM platform_live WHERE live_id = #{liveId})
    </select>
        
    <insert id="insertPlatformProject" parameterType="PlatformProject" useGeneratedKeys="true" keyProperty="projectId">
        insert into platform_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectTitle != null and projectTitle != ''">project_title,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectTitle != null and projectTitle != ''">#{projectTitle},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformProject" parameterType="PlatformProject">
        update platform_project 
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTitle != null and projectTitle != ''">project_title = #{projectTitle},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deletePlatformProjectByProjectId" parameterType="Long">
        delete from platform_project where project_id = #{projectId}
    </delete>
</mapper>