package com.ruoyi.platform.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.service.IPlatformArticleService;
import com.ruoyi.platform.utils.TongYiQianWen;
import com.ruoyi.platform.utils.HashrateCostUtils.HashrateCost;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 文案Controller
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@RestController
@RequestMapping("/platform/article")
@Tag(name = "【文案】管理")
public class PlatformArticleController extends BaseController {
    
    @Autowired
    private IPlatformArticleService platformArticleService;

    /**
     * 智能生成文案
     */
    @Operation(summary = "智能生成文案")
    @Log(title = "智能生成文案", businessType = BusinessType.OTHER)
    @PostMapping("/generate") 
    @HashrateCost(description = "智能生成文案", expectedPoints = 10)
    public TableDataInfo generate(@RequestBody List<String> content) throws Exception { //@RequestBody接收JSON数组
        return getDataTable(TongYiQianWen.callAgentApp(content));
    }    

    /**
     * 查询文案列表
     */
    @Operation(summary = "查询文案列表")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformArticle platformArticle) {
        startPage();
        List<PlatformArticle> list = platformArticleService.selectPlatformArticleList(platformArticle);
        return getDataTable(list);
    }

    /**
     * 导出文案列表
     */
    @Operation(summary = "导出文案列表")
    @Log(title = "导出文案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformArticle platformArticle) {
        List<PlatformArticle> list = platformArticleService.selectPlatformArticleList(platformArticle);
        ExcelUtil<PlatformArticle> util = new ExcelUtil<PlatformArticle>(PlatformArticle.class);
        util.exportExcel(response, list, "文案数据");
    }

    /**
     * 获取文案详细信息
     */
    @Operation(summary = "获取文案详细信息")
    @GetMapping(value = "/{articleId}")
    public AjaxResult getInfo(@PathVariable("articleId") Long articleId) {
        return success(platformArticleService.selectPlatformArticleByArticleId(articleId));
    }

    /**
     * 新增文案
     */
    @Operation(summary = "新增文案")
    @Log(title = "新增文案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformArticle platformArticle) {
        platformArticle.setCreateBy(getUsername()); //创建人
        platformArticle.setUpdateBy(getUsername()); //修改人
        return toAjax(platformArticleService.insertPlatformArticle(platformArticle));
    }

    /**
     * 修改文案
     */
    @Operation(summary = "修改文案")
    @Log(title = "修改文案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformArticle platformArticle) {
        platformArticle.setUpdateBy(getUsername());
        return toAjax(platformArticleService.updatePlatformArticle(platformArticle));
    } 

    /**
     * 删除文案
     */
    @Operation(summary = "删除文案")
    @Log(title = "删除文案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{articleIds}")
    public AjaxResult remove(@PathVariable(name = "articleIds") Long[] articleIds) {
        return toAjax(platformArticleService.deletePlatformArticleByArticleIds(articleIds));
    }

    /**
     * 导出文案数据
     */
    @Operation(summary = "导出当前项目文案数据")
    @Log(title = "导出当前项目文案数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTxt")
    public void exportTxt(Long projectId, HttpServletResponse response) {
        platformArticleService.exportTxt(projectId,response);
    }

    /**
     * 批量新增文案
     */
    @Operation(summary = "批量新增文案")
    @Log(title = "批量新增文案", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<PlatformArticle> articles) {
        return toAjax(platformArticleService.batchInsertPlatformArticle(articles));
    }

    //根据文案Ids查询文案信息
    @Operation(summary = "根据文案Ids查询文案信息")
    @PostMapping("/getMutipleArticle")
    public R<Map<Long, String>> getArticleByIds(@RequestBody List<Long> articleIds) {
        return R.ok(platformArticleService.getArticleByIds(articleIds));
    }
}
