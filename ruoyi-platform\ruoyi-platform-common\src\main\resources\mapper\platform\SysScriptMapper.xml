<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.SysScriptMapper">
    
    <resultMap type="SysScript" id="SysScriptResult">
        <result property="scriptId"    column="script_id"    />
        <result property="scriptTitle"    column="script_title"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysScriptVo">
        select script_id, script_title, create_by, create_time, update_by, update_time, remark from sys_script
    </sql>

    <select id="selectSysScriptList" parameterType="SysScript" resultMap="SysScriptResult">
        <include refid="selectSysScriptVo"/>
        <where>  
            <if test="scriptTitle != null  and scriptTitle != ''"> and script_title like concat('%', #{scriptTitle}, '%')</if>
        </where>
    </select>
    
    <select id="selectSysScriptByScriptId" parameterType="Long" resultMap="SysScriptResult">
        select script_id, script_title, content, create_by, create_time, update_by, update_time, remark from sys_script
        where sys_script.script_id = #{scriptId}
    </select>
        
    <insert id="insertSysScript" parameterType="SysScript" useGeneratedKeys="true" keyProperty="scriptId">
        insert into sys_script
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scriptTitle != null and scriptTitle != ''">script_title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scriptTitle != null and scriptTitle != ''">#{scriptTitle},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysScript" parameterType="SysScript">
        update sys_script
        <trim prefix="SET" suffixOverrides=",">
            <if test="scriptTitle != null and scriptTitle != ''">script_title = #{scriptTitle},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sys_script.script_id = #{scriptId}
    </update>

    <delete id="deleteSysScriptByScriptId" parameterType="Long">
        delete from sys_script where script_id = #{scriptId}
    </delete>

    <delete id="deleteSysScriptByScriptIds" parameterType="String">
        delete from sys_script where script_id in 
        <foreach item="scriptId" collection="array" open="(" separator="," close=")">
            #{scriptId}
        </foreach>
    </delete>
</mapper>