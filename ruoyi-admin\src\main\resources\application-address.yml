# 配置文件版本
version: '1.0'

# 文件路径配置
file_paths:
  # 训练相关路径
  training:
    # 待训练文件路径
    pending_training: "ModelCheckpoint/sound"
    # 已训练文件路径
    trained: "ModelCheckpoint/sound"
    # 训练声音文件存储地址
    training_sound_storage: "user/${username}/sound/model"
    # 参考声音音频文件存储地址，使用插值表示动态日期路径
    reference_audio_storage: "user/${username}/sound/ref/${date}"

  # 模型服务相关路径
  model_service:
    # 模型服务上传训练后的声音模型文件路径
    upload_model: "ModelCheckpoint/sound"
    # 模型上传服务后的推理音频文件路径，使用插值表示动态任务 ID
    inference_audio_after_upload: "ModelCheckpoint/sound/${taskId}"

  # 推理音频相关路径
  inference_audio:
    # 推理音频上传路径，使用插值表示动态机器码和任务 ID
    upload: "ModelTasks/${machineCode}/${taskId}"

  # 上传文件相关路径
  upload:
    # 上传音频文件路径，使用插值表示动态日期路径
    audio: "user/${username}/audio/${date}"
    # 上传形象素材文件路径，使用插值表示动态日期路径
    image: "video/image/${date}"
    # 上传合成音频素材文件路径，使用插值表示动态日期路径
    ref_audio: "video/audio/${date}"
    # 上传合成视频结果文件路径，使用插值表示动态日期路径
    result_video: "video/result/${date}"