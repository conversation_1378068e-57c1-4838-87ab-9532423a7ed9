package com.ruoyi.video.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 媒资信息DTO
 * <p>
 * 用于媒资注册和查询的请求和响应数据传输对象。
 * 包含媒资的基本信息、业务类型、分类等。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class MediaDTO {
    // 通用参数 无论返回还是请求
    /**
     * 媒资ID
     * 由阿里云生成的唯一标识符，用于标识媒资
     */
    @JsonProperty("MediaId")
    private String MediaId;

    
    // 通用多个API的请求参数
    /**
     * 自定义设置。为 JSON 字符串，支持消息回调等设置。
     */
    @JsonProperty("UserData")
    private UserDataDTO userData;

    @Data
    public static class UserDataDTO {
        /**
         * 消息回调配置
         */
        @JsonProperty("MessageCallback")
        private MessageCallbackDTO messageCallback;

        /**
         * 扩展字段
         */
        @JsonProperty("Extend")
        private java.util.Map<String, String> extend;

        @Data
        public static class MessageCallbackDTO {
            /**
             * 回调地址
             */
            @JsonProperty("CallbackURL")
            private String callbackURL;
        }
    }

    // 通用多个API的返回参数
    /**
     * 请求ID
     */
    @JsonProperty("RequestId")
    private String RequestId;

    // 注册媒资的请求参数
    /**
     * OSS地址 支持两种格式 OSS地址和VOD媒资地址
     */
    @JsonProperty("InputURL")
    private String InputURL;
    /**
     * 媒资媒体类型 取值范围 image、video、audio、text
     */
    @JsonProperty("MediaType")
    private String MediaType;
    /**
     * 媒资业务类型 取值范围 subtitles（字幕）、watermark(水印)、opening（开场）、ending（结尾/片尾）、general（通用）
     */
    @JsonProperty("BussinessType")
    private String BussinessType;
    /**
     * 标题
     */
    @JsonProperty("Title")
    private String Title;
    /**
     * 内容描述
     */
    @JsonProperty("Description")
    private String Description;
    /**
     * 标签
     */
    @JsonProperty("MediaTags")
    private String MediaTags;
    /**
     * 封面地址
     */
    @JsonProperty("CoverURL")
    private String CoverURL;
    /**
     * 是否覆盖已注册媒资 默认false
     */
    @JsonProperty("Overwrite")
    private Boolean Overwrite;
    /**
     * 用户token
     * 用于标识用户身份，防止重复注册
     */
    @JsonProperty("ClientToken")
    private String ClientToken;
    /**
     * 注册配置
     */
    @JsonProperty("RegisterConfig")
    private String RegisterConfig;
    /**
     * 分类ID
     */
    @JsonProperty("CateID")
    private String CateID;
    /**
     * 工作流ID
     */
    @JsonProperty("WorkflowId")
    private String WorkflowId;
    /**
     * 自定义ID 仅支持小写字母大写字母数字横夏下划线长度6-64个字符
     */
    @JsonProperty("ReferenceId")
    private String ReferenceId;
    /**
     * 智能标签模板 取值: S00000101-300080：包含 NLP 内容理解功能的系统模板.S00000103-000001：包含 NLP
     * 内容理解功能和所有标签能力S00000103-000002：包含所有标签能力，不包含 NLP 内容理解功能
     */
    @JsonProperty("SmartTagTemplate")
    private String SmartTagTemplate;

    // 注册媒资的返回参数

    /**
     * 应用ID
     */
    @JsonProperty("AppId")
    private String AppId;
    /**
     * 实体ID 可调用CreateEntity接口创建实体
     */
    @JsonProperty("EntityId")
    private String EntityId;
    /**
     * 文件信息，包含文件的类型、名称、大小和扩展名等信息。
     */
    @JsonProperty("FileInfo")
    private FileInfoDTO fileInfo;

    /**
     * 上传ID（安全标识符）
     * 用于标识上传任务的唯一ID，不暴露实际的OSS路径信息
     * 例如：550e8400-e29b-41d4-a716-446655440000
     */
    @JsonProperty("UploadId")
    private String uniqueObjectKey; // 内部仍使用uniqueObjectKey字段名，但语义已改变

    @Data
    public static class FileInfoDTO {
        /**
         * 文件类型 video、audio、image、text、other
         */
        @JsonProperty("Type")
        private String type;
        /**
         * 文件名称不带扩展名
         */
        @JsonProperty("Name")
        private String name;
        /**
         * 文件大小
         */
        @JsonProperty("Size")
        private Long size;
        /**
         * 文件扩展名
         */
        @JsonProperty("Ext")
        private String ext;
    }

    /**
     * 目标存储地址
     */
    @JsonProperty("UploadTargetConfig")
    private UploadTargetConfigDTO uploadTargetConfig;

    @Data
    public static class UploadTargetConfigDTO {
        /**
         * 存储类型，仅支持 oss
         */
        @JsonProperty("StorageType")
        private String storageType;

        /**
         * 存储位置，仅支持 VOD 点播存储
         */
        @JsonProperty("StorageLocation")
        private String storageLocation;
    }

    /**
     * 上传媒资的元数据
     */
    @JsonProperty("MediaMetaData")
    private MediaMetaDataDTO mediaMetaData;

    @Data
    public static class MediaMetaDataDTO {
        /**
         * 标题
         */
        @JsonProperty("Title")
        private String title;

        /**
         * 描述
         */
        @JsonProperty("Description")
        private String description;

        /**
         * 分类 ID
         */
        @JsonProperty("CateId")
        private String cateId;

        /**
         * 标签
         */
        @JsonProperty("Tags")
        private String tags;

        /**
         * 业务类型
         */
        @JsonProperty("BusinessType")
        private String businessType;

        /**
         * 封面地址
         */
        @JsonProperty("CoverURL")
        private String coverURL;

        /**
         * 动态元数据
         */
        @JsonProperty("DynamicMetaData")
        private String dynamicMetaData;
    }

    /**
     * 有上传后处理动作
     */
    @JsonProperty("PostProcessConfig")
    private PostProcessConfigDTO postProcessConfig;

    @Data
    public static class PostProcessConfigDTO {
        /**
         * 处理类型
         */
        @JsonProperty("ProcessType")
        private String processType;

        /**
         * 处理 ID
         */
        @JsonProperty("ProcessID")
        private String processID;
    }

    // 返回参数
    /**
     * 媒资ID
     */
    @JsonProperty("MediaId")
    private String mediaId;
    /**
     * 媒资URL
     */
    @JsonProperty("MediaURL")
    private String mediaURL;
    /**
     * 文件OSS地址 不带鉴权 需要进行特殊处理
     */
    @JsonProperty("FileURL")
    private String fileURL;
    /**
     * 上传地址 阿里云返回仅仅只带编码需要进行特殊处理
     */
    @JsonProperty("UploadAddress")
    private String uploadAddress;
    /**
     * 上传凭证 阿里云返回仅仅只带编码需要进行特殊处理
     */
    @JsonProperty("UploadAuth")
    private String uploadAuth;

    // 真正返回的字段为 MediaID 和 经过处理的 SignedUploadUrl
    /**
     * 媒资上传地址
     * <p>
     * 经过处理的上传地址，包含了必要的签名信息。
     * </p>
     */
    @JsonProperty("SignedUploadUrl")
    private String signedUploadUrl;

    /**
     * 获取媒资信息的查询参数
     * ListMediaBasicInfos 查询参数对象
     * 对应阿里云 ListMediaBasicInfos API 所有可选参数
     */
    /** utcCreated（创建时间）的开始时间，ISO8601格式，UTC时间 */
    private String startTime;
    /** utcCreated（创建时间）的结束时间，ISO8601格式，UTC时间 */
    private String endTime;
    /** 媒资类型，image,video,audio,text */
    private String mediaType;
    /** 媒资业务类型，subtitles,watermark,opening,ending,general */
    private String businessType;
    /** 媒资来源，oss,vod,live,general */
    private String source;
    /** 媒资状态，Init,Preparing,PrepareFail,Normal */
    private String status;
    /** 用来表示当前调用返回读取到的位置，空代表数据已经读取完毕 */
    private String nextToken;
    /** 本次请求所返回的最大记录条数，最大值100，默认值10 */
    private Integer maxResults = 10;
    /** 排序，desc倒序，asc正序，默认desc */
    private String sortBy = "desc";
    /** 是否返回文件基础信息，true/false，默认false */
    private Boolean includeFileBasicInfo = false;

}
