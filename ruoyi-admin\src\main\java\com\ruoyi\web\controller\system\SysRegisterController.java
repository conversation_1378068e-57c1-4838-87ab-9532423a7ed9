package com.ruoyi.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysRegisterService;
import com.ruoyi.platform.domain.PlatformHashrate;
import com.ruoyi.platform.service.IPlatformHashrateService;
import com.ruoyi.system.service.ISysConfigService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 注册验证
 * 
 * <AUTHOR>
 */
@Tag(name = "注册验证")
@RestController
public class SysRegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IPlatformHashrateService platformHashrateService;

    @Operation(summary = "注册方法")
    @PostMapping("/register")
    public AjaxResult registerWithHashrate(@RequestBody RegisterBody user) {
        if (!"true".equals(configService.selectConfigByKey("sys.account.registerUser"))) {
            return error("当前系统没有开启注册功能！");
        }

        String userName = user.getUsername();  // 获取到注册的用户名称
        String msg = registerService.register(user);
        if (StringUtils.isEmpty(msg)) { // 如果注册成功在算力对象中新增该用户的信息
            //算力对象
            PlatformHashrate platformHashrate = new PlatformHashrate();
            platformHashrate.setUserName(userName); //用户名称
            platformHashrate.setCreateBy(userName); //创建者
            platformHashrate.setHashrateBalance("0"); //初始算力余额都是0

            // 调用服务层方法初始化算力点数
            int isSuccessful = platformHashrateService.insertPlatformHashrate(platformHashrate);
            if (isSuccessful>0) {
                return success("用户注册并初始化算力点数成功！");
            } else {
                return error("初始化算力点数失败，请联系管理员。");
            }
        } else {
            return error(msg);
        }
    }
}
