package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformModel;

/**
 * AI模型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface PlatformModelMapper 
{
    /**
     * 查询AI模型
     * 
     * @param modelId AI模型主键
     * @return AI模型
     */
    public PlatformModel selectWyModelByModelId(Long modelId);

    /**
     * 查询AI模型列表
     * 
     * @param wyModel AI模型
     * @return AI模型集合
     */
    public List<PlatformModel> selectWyModelList(PlatformModel wyModel);

    /**
     * 新增AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    public int insertWyModel(PlatformModel wyModel);

    /**
     * 修改AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    public int updateWyModel(PlatformModel wyModel);

    /**
     * 删除AI模型
     * 
     * @param modelId AI模型主键
     * @return 结果
     */
    public int deleteWyModelByModelId(Long modelId);

    /**
     * 批量删除AI模型
     * 
     * @param modelIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWyModelByModelIds(Long[] modelIds);

    /**
     * 根据模型代码查询模型信息
     * 
     * @param code 模型代码
     * @return 模型信息
     */
    @Select("select * from platform_model where model_code = #{code}")
    public PlatformModel selectWyModelByCode(String code);
}
