package com.ruoyi.platform.model.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.ruoyi.platform.model.domain.PlatformSound;

/**
 * 声音管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface PlatformSoundMapper 
{
    /**
     * 查询声音管理
     * 
     * @param soundId 声音管理主键
     * @return 声音管理
     */
    public PlatformSound selectPlatformSoundBySoundId(Long soundId);

    /**
     * 查询声音管理列表
     * 
     * @param platformSound 声音管理
     * @return 声音管理集合
     */
    public List<PlatformSound> selectPlatformSoundList(PlatformSound platformSound);

    /**
     * 新增声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    public int insertPlatformSound(PlatformSound platformSound);

    /**
     * 修改声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    public int updatePlatformSound(PlatformSound platformSound);

    /**
     * 删除声音管理
     * 
     * @param soundId 声音管理主键
     * @return 结果
     */
    public int deletePlatformSoundBySoundId(Long soundId);

    /**
     * 批量删除声音管理
     * 
     * @param soundIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformSoundBySoundIds(Long[] soundIds);

    
    //根据训练任务声音状态去修改：完成：失败
    @Update("UPDATE platform_sound SET sound_status = #{soundStatus} WHERE sound_id = #{soundId}")
    public int updateSoundStaus(Long soundId,Long soundStatus);

    //修改 根据声音id 去修改模型的文件地址
    @Update("UPDATE platform_sound SET sound_gpt = #{soundGpt},sound_sovits = #{soundSovits},update_by=#{updateBy},update_time=#{updateTime} WHERE sound_id = #{soundId}")
    public int updateModelSoundMapper(PlatformSound platformSound);

    //修改声音状态为待处理
    @Update("UPDATE platform_sound SET sound_status = #{soundStatus} WHERE sound_id = #{soundId}")
    public int updateSoundState(PlatformSound platformSound);

    //创建者查询声音，数据公开 1
    @Select("SELECT * FROM platform_sound WHERE create_by = #{createBy} or sound_filtration=1")
    public List<PlatformSound> selectSoundList(String createBy);
}
