<mxfile host="65bd71144e">
    <diagram id="PzGHVj2JrUCharDApkJk" name="第 1 页">
        <mxGraphModel dx="1496" dy="1393" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" parent="1" source="5" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="调用登录&lt;br&gt;发送验证码&lt;br&gt;接口" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="59" y="60" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" parent="1" source="6" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="不存在账户" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="9" vertex="1" connectable="0">
                    <mxGeometry x="-0.325" y="-1" relative="1" as="geometry">
                        <mxPoint x="13" y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="6" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="存在账户" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="11" vertex="1" connectable="0">
                    <mxGeometry x="0.2667" y="-1" relative="1" as="geometry">
                        <mxPoint x="1" y="-14" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="是否存在账户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="59" y="170" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=none;html=1;" parent="1" source="8" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="18" vertex="1" connectable="0">
                    <mxGeometry x="-0.3667" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="8" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="309" y="340" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="289" y="388"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="20" vertex="1" connectable="0">
                    <mxGeometry x="-0.2857" y="-1" relative="1" as="geometry">
                        <mxPoint x="1" y="-41" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="是否开启&lt;br&gt;自动注册" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="239" y="170" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="" style="edgeStyle=none;html=1;" parent="1" source="10" target="23" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="发送&lt;br&gt;登录验证码" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="59" y="338" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="抛出异常&lt;br&gt;没有该用户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="379" y="190" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="" style="edgeStyle=none;html=1;" parent="1" source="23" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="用户收到验证码&lt;br&gt;携带验证码调用&lt;br&gt;登录验证接口" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="59" y="490" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="27" target="29" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="109" y="1027"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="30" vertex="1" connectable="0">
                    <mxGeometry x="-0.22" y="-2" relative="1" as="geometry">
                        <mxPoint x="2" y="-25" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="" style="edgeStyle=none;html=1;" parent="1" source="27" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="40" vertex="1" connectable="0">
                    <mxGeometry x="-0.3619" y="-4" relative="1" as="geometry">
                        <mxPoint y="-4" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="是否存在账户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="59" y="866.75" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="返回token" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="189" y="996.75" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="42" style="edgeStyle=none;html=1;" parent="1" source="31" target="29" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="379" y="1027"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="创建账户" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="329" y="866.75" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="" style="edgeStyle=none;html=1;" parent="1" source="33" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="36" vertex="1" connectable="0">
                    <mxGeometry x="-0.3619" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="" style="edgeStyle=none;html=1;" parent="1" source="33" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="37" vertex="1" connectable="0">
                    <mxGeometry x="-0.1238" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="验证码&lt;br&gt;是否正确" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="59" y="728" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="抛出异常&lt;br&gt;验证码错误" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="379" y="748" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="" style="edgeStyle=none;html=1;" parent="1" source="43" target="44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="调用注册&lt;br&gt;发送验证码&lt;br&gt;接口" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="669" y="348" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="" style="edgeStyle=none;html=1;" parent="1" source="44" target="47" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="48" vertex="1" connectable="0">
                    <mxGeometry x="-0.4111" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="" style="edgeStyle=none;html=1;" parent="1" source="44" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="51" vertex="1" connectable="0">
                    <mxGeometry x="-0.5067" y="1" relative="1" as="geometry">
                        <mxPoint x="-1" y="10" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="是否存在账户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="669" y="448" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="抛出异常&lt;br&gt;账户已存在" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="509" y="468" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="edgeStyle=none;html=1;" parent="1" source="50" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="发送&lt;br&gt;注册验证码" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="669" y="578" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="" style="edgeStyle=none;html=1;" parent="1" source="53" target="55" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="用户收到验证码&lt;br&gt;携带验证码调用&lt;br&gt;注册验证接口" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="669" y="728" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="60" style="edgeStyle=none;html=1;" parent="1" source="55" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="60" vertex="1" connectable="0">
                    <mxGeometry x="0.2444" y="5" relative="1" as="geometry">
                        <mxPoint x="11" y="-5" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" style="edgeStyle=none;html=1;" parent="1" source="55" target="81" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="709" y="873" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="62" vertex="1" connectable="0">
                    <mxGeometry x="-0.2358" y="4" relative="1" as="geometry">
                        <mxPoint x="-4" y="-7" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="验证码&lt;br&gt;是否正确" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="529" y="728" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="" style="edgeStyle=none;html=1;" parent="1" source="64" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="调用重置密码&lt;br&gt;发送验证码&lt;br&gt;接口" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="516.5" y="60" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="69" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="859" y="193" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="72" vertex="1" connectable="0">
                    <mxGeometry x="-0.2519" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="" style="edgeStyle=none;html=1;" parent="1" source="69" target="74" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="是否存在账户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="516.5" y="170" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="" style="edgeStyle=none;html=1;" parent="1" source="74" target="76" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="74" value="发送&lt;br&gt;重置验证码" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="516.5" y="338" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="79" value="" style="edgeStyle=none;html=1;" parent="1" source="76" target="78" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="76" value="用户收到验证码&lt;br&gt;携带验证码调用&lt;br&gt;重置验证接口" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="379" y="338" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="82" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="78" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="83" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="82" vertex="1" connectable="0">
                    <mxGeometry x="-0.35" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="85" value="" style="edgeStyle=none;html=1;" parent="1" source="78" target="84" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="86" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="85" vertex="1" connectable="0">
                    <mxGeometry x="-0.3905" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="78" value="验证码&lt;br&gt;是否正确" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.decision;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="379" y="518" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="81" value="创建账户" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="529" y="874.75" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="88" value="" style="edgeStyle=none;html=1;" parent="1" source="84" target="87" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="修改原有的密码" style="rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;arcSize=14;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="239" y="518" width="100" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="87" value="删除当前&lt;br&gt;登录用户&lt;br&gt;凭证信息" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="239" y="668" width="100" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>