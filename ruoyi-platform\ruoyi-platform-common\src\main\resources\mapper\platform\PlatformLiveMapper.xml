<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformLiveMapper">
    
    <resultMap type="PlatformLive" id="PlatformLiveResult">
        <result property="liveId"    column="live_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="liveType"    column="live_type"    />
        <result property="sceneconId"    column="scenecon_id"    />
        <result property="goodsId"    column="goods_id"   typeHandler="com.ruoyi.platform.utils.ListTypeHandler" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="liveName" column="live_name"/>
    </resultMap>

    <sql id="selectPlatformLiveVo">
        select live_id, project_id, live_type, scenecon_id, goods_id, l.create_by, l.create_time, l.update_by, l.update_time, l.remark,live_name from platform_live l left join sys_user u on u.user_name = l.create_by 
        left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformLiveList" parameterType="PlatformLive" resultMap="PlatformLiveResult">
        <include refid="selectPlatformLiveVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="liveName != null  and liveName != ''"> and live_name like concat('%', #{liveName}, '%')</if>
            ${params.dataScope}
            <if test="liveType != null  and liveType != ''"> and live_type = #{liveType}</if>
            <if test="sceneconId != null "> and scenecon_id = #{sceneconId}</if>
            <if test="goodsId != null  and goodsId != ''"> and goods_id = #{goodsId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformLiveByLiveId" parameterType="Long" resultMap="PlatformLiveResult">
        <include refid="selectPlatformLiveVo"/>
        where live_id = #{liveId}
    </select>
        
    <insert id="insertPlatformLive" parameterType="PlatformLive" useGeneratedKeys="true" keyProperty="liveId">
        insert into platform_live
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="liveType != null">live_type,</if>
            <if test="sceneconId != null">scenecon_id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="liveName != null">live_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="liveType != null">#{liveType},</if>
            <if test="sceneconId != null">#{sceneconId},</if>
            <if test="goodsId != null">#{goodsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="liveName != null">#{liveName},</if>
         </trim>
    </insert>

    <update id="updatePlatformLive" parameterType="PlatformLive">
        update platform_live
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="liveType != null">live_type = #{liveType},</if>
            <if test="sceneconId != null">scenecon_id = #{sceneconId},</if>
            <if test="goodsId != null">goods_id = #{goodsId,typeHandler=com.ruoyi.platform.utils.ListTypeHandler},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="liveName != null">live_name = #{liveName},</if>
        </trim>
        where live_id = #{liveId}
    </update>

    <delete id="deletePlatformLiveByLiveId" parameterType="Long">
        delete from platform_live where live_id = #{liveId}
    </delete>

    <delete id="deletePlatformLiveByLiveIds" parameterType="String">
        delete from platform_live where live_id in 
        <foreach item="liveId" collection="array" open="(" separator="," close=")">
            #{liveId}
        </foreach>
    </delete>

    <!-- 强制删除关联的直播、场控和产品数据 -->
    <delete id="deleteConstraintLiveProjectId" parameterType="Long">
        DELETE platform_goods, platform_scenecon, platform_live
        FROM platform_goods, platform_scenecon, platform_live
        WHERE platform_goods.project_id = #{projectId} 
        AND platform_scenecon.project_id = #{projectId} 
        AND platform_live.project_id = #{projectId}
    </delete>

    <select id="selectLive" parameterType="Long" resultMap="PlatformLiveResult">
        select * from platform_live where live_id=#{liveId}
    </select>
</mapper>