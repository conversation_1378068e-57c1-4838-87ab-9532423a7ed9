package com.ruoyi.tingwu.mapper;

import java.util.List;

import com.ruoyi.tingwu.domain.TingwuPhrase;

/**
 * 词表信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface TingwuPhraseMapper {

    /**
     * 查询词表信息列表
     * 
     * @param tingwuPhrase 词表信息
     * @return 词表信息集合
     */
    public List<TingwuPhrase> selectTingwuPhraseList(TingwuPhrase tingwuPhrase);

    /**
     * 新增词表信息
     * 
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    public int insertTingwuPhrase(TingwuPhrase tingwuPhrase);

    /**
     * 修改词表信息
     * 
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    public int updateTingwuPhrase(TingwuPhrase tingwuPhrase);



    /**
     * 批量删除词表信息
     * 
     * @param phraseIds 词表编号
     * @return 结果
     */
    public int deleteTingwuPhraseByPhraseIdIds(String[] phraseIds);
}
