<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-platform</artifactId>

    <properties>
        <mybatis-plus.version>3.5.8</mybatis-plus.version>
    </properties>

    <description>
        平台模块
    </description>
    <dependencyManagement>
        <dependencies>

            <!-- platform-common 素材 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-platform-common</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
            
            <!-- platform-model 模型 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-platform-model</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- platform-starter-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-platform-starter</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- mybatis plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-file-starter</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
            
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-platform-common</module>
        <module>ruoyi-platform-starter</module>
        <module>ruoyi-platform-model</module>
    </modules>
    <packaging>pom</packaging>
</project>