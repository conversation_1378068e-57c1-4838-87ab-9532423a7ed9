package com.ruoyi.platform.model.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 机器管理对象 platform_machine_code
 * 
 * <AUTHOR>
 * @date 2024-10-20
 */
@Schema(description = "机器管理对象")
public class PlatformMachineCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long machineCodeId;

    /** 机器码名称 */
    @Schema(title = "机器码名称")
    @Excel(name = "机器码名称")
    private String machineCodeName;

    /** 机器码状态 */
    @Schema(title = "机器码状态")
    @Excel(name = "机器码状态")
    private Long machineCodeStatus;
    public void setMachineCodeId(Long machineCodeId) 
    {
        this.machineCodeId = machineCodeId;
    }

    public Long getMachineCodeId() 
    {
        return machineCodeId;
    }


    public void setMachineCodeName(String machineCodeName) 
    {
        this.machineCodeName = machineCodeName;
    }

    public String getMachineCodeName() 
    {
        return machineCodeName;
    }


    public void setMachineCodeStatus(Long machineCodeStatus) 
    {
        this.machineCodeStatus = machineCodeStatus;
    }

    public Long getMachineCodeStatus() 
    {
        return machineCodeStatus;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("machineCodeId", getMachineCodeId())
            .append("machineCodeName", getMachineCodeName())
            .append("machineCodeStatus", getMachineCodeStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
