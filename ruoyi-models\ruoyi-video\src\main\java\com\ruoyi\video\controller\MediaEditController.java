package com.ruoyi.video.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.video.domain.MediaEdit;
import com.ruoyi.video.service.IMediaEditService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 媒体编辑表Controller
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/video/mediaEdit")
@Tag(name = "【媒体编辑表】管理")
public class MediaEditController extends BaseController
{
    @Autowired
    private IMediaEditService mediaEditService;

    /**
     * 查询媒体编辑表列表
     */
    @Operation(summary = "查询媒体编辑表列表")
    @GetMapping("/list")
    public TableDataInfo list(MediaEdit mediaEdit)
    {
        startPage();
        List<MediaEdit> list = mediaEditService.selectMediaEditList(mediaEdit);
        return getDataTable(list);
    }

    /**
     * 导出媒体编辑表列表
     */
    @Operation(summary = "导出媒体编辑表列表")
    @Log(title = "媒体编辑表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MediaEdit mediaEdit)
    {
        List<MediaEdit> list = mediaEditService.selectMediaEditList(mediaEdit);
        ExcelUtil<MediaEdit> util = new ExcelUtil<MediaEdit>(MediaEdit.class);
        util.exportExcel(response, list, "媒体编辑表数据");
    }

    /**
     * 获取媒体编辑表详细信息
     */
    @Operation(summary = "获取媒体编辑表详细信息")
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") String jobId)
    {
        return success(mediaEditService.selectMediaEditByJobId(jobId));
    }

    /**
     * 新增媒体编辑表
     */
    @Operation(summary = "新增媒体编辑表")
    @Log(title = "媒体编辑表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MediaEdit mediaEdit)
    {
        return toAjax(mediaEditService.insertMediaEdit(mediaEdit));
    }

    /**
     * 修改媒体编辑表
     */
    @Operation(summary = "修改媒体编辑表")
    @Log(title = "媒体编辑表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MediaEdit mediaEdit)
    {
        return toAjax(mediaEditService.updateMediaEdit(mediaEdit));
    }

    /**
     * 删除媒体编辑表
     */
    @Operation(summary = "删除媒体编辑表")
    @Log(title = "媒体编辑表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable( name = "jobIds" ) String[] jobIds)
    {
        return toAjax(mediaEditService.deleteMediaEditByJobIds(jobIds));
    }

    /**
     * 根据项目ID批量删除媒体编辑表
     */
    @Operation(summary = "根据项目ID批量删除媒体编辑表")
    @Log(title = "媒体编辑表", businessType = BusinessType.DELETE)
    @DeleteMapping("/project/{projectIds}")
    public AjaxResult removeByProjectIds(@PathVariable( name = "projectIds" ) String[] projectIds)
    {
        return toAjax(mediaEditService.deleteMediaEditByProjectIds(projectIds));
    }
}
