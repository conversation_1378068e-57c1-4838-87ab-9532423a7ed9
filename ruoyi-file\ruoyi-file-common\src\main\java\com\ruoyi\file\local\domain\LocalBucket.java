package com.ruoyi.file.local.domain;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.channels.FileChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.domain.SysFilePartETag;
import com.ruoyi.file.storage.StorageBucket;
import com.ruoyi.file.storage.StorageEntity;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

@Builder
@Slf4j
public class LocalBucket implements StorageBucket {

    private String clientName;
    private String basePath;
    private String permission;
    private String api;

    @Override
    public void put(String filePath, MultipartFile file) {
        Path dest = Paths.get(basePath, filePath);
        try (InputStream inputStream = file.getInputStream()) {
            Files.createDirectories(dest.getParent());
            Files.copy(inputStream, dest);
        } catch (Exception e) {
            throw new ServiceException("Failed to upload file: " + e.getMessage());
        }
    }

    @Override
    public StorageEntity get(String filePath) throws IOException {
        Path file = Paths.get(basePath, filePath);
        StorageEntity fileEntity = new StorageEntity();
        fileEntity.setFilePath(filePath);
        fileEntity.setInputStream(new FileInputStream(file.toFile()));
        fileEntity.setByteCount(file.toFile().length());
        return fileEntity;
    }

    @Override
    public void remove(String filePath) throws IOException {
        Path file = Paths.get(basePath, filePath);
        Files.deleteIfExists(file);
    }

    @Override
    public URL generatePresignedUrl(String filePath, int expireTime) throws Exception {
        HttpServletRequest request = ServletUtils.getRequest();
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getSession().getServletContext().getContextPath();
        String toHex = Md5Utils.hash(filePath + expireTime);
        StringBuilder sb = new StringBuilder();
        sb.append(url.delete(url.length() - request.getRequestURI().length(), url.length())
                .append(contextPath).toString())
                .append(getApi()).append("?")
                .append("filePath=").append(URLEncoder.encode(filePath, "UTF-8"))
                .append("&toHex=").append(toHex);
        return URI.create(sb.toString()).toURL();
    }

    @Override
    public URL generatePublicURL(String filePath) throws Exception {
        HttpServletRequest request = ServletUtils.getRequest();
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getSession().getServletContext().getContextPath();
        StringBuilder sb = new StringBuilder();
        sb.append(url.delete(url.length() - request.getRequestURI().length(), url.length())
                .append(contextPath).toString()).append(getApi())
                .append(filePath.replace("\\", "/"));
        return new URI(sb.toString()).toURL();
    }

    // 存储分片上传的元数据
    private final ConcurrentHashMap<String, List<Map<String, Object>>> uploadMetadata = new ConcurrentHashMap<>();

    /**
     * 初始化分片
     */
    public String initMultipartUpload(String filePath) throws Exception {
        try {
            String uploadId = UUID.randomUUID().toString();
            uploadMetadata.put(uploadId, new ArrayList<>());

            // 创建临时上传目录
            Path tempDir = Paths.get(basePath, "temp_uploads", uploadId);
            Files.createDirectories(tempDir);
            return uploadId;
        } catch (Exception e) {
            log.error("初始化失败: 文件={}, 错误={}", filePath, e.getMessage());
            throw new ServiceException("初始化分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传单个分片
     */
    public SysFilePartETag uploadPart(String filePath, String uploadId, int partNumber, long partSize,
            InputStream inputStream) throws Exception {
        String fileNameOnly = Paths.get(filePath).getFileName().toString(); // 提取文件名部分
        String partFileName = String.format("%s.%s.part.%d", fileNameOnly, uploadId, partNumber);
        // 构建分片存储路径
        Path tempDir = Paths.get(basePath, "temp_uploads", uploadId);
        Files.createDirectories(tempDir);
        Path partPath = tempDir.resolve(partFileName);
        // 写入分片数据
        try (OutputStream fos = Files.newOutputStream(partPath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytesWritten = 0;
            while ((bytesRead = inputStream.read(buffer)) != -1 && totalBytesWritten < partSize) {
                int writeSize = (int) Math.min(bytesRead, partSize - totalBytesWritten);
                fos.write(buffer, 0, writeSize);
                totalBytesWritten += writeSize;
            }
            if (totalBytesWritten != partSize) {
                throw new ServiceException("分片大小不匹配: 预期=" + partSize + ", 实际=" + totalBytesWritten);
            }
        }
        // 计算 ETag（MD5）
        String etag = Md5Utils.getMd5(partPath.toFile());
        if (etag == null) {
            throw new ServiceException("计算分片 MD5 失败");
        }
        etag = etag.toUpperCase();
        return new SysFilePartETag(partNumber, etag);
    }

    public String completeMultipartUpload(String filePath, String uploadId, List<SysFilePartETag> filePartETags)
        throws Exception {
        if (filePartETags == null || filePartETags.isEmpty()) {
            throw new ServiceException("分片信息不能为空");
        }
        // 构造每个分片的完整路径
        List<Path> partPaths = filePartETags.stream()
                .sorted(Comparator.comparingInt(SysFilePartETag::getPartNumber))
                .map(part -> {
                    String fileNameOnly = Paths.get(filePath).getFileName().toString();
                    String partFileName = String.format("%s.%s.part.%d", fileNameOnly, uploadId, part.getPartNumber());
                    return Paths.get(basePath, "temp_uploads", uploadId, partFileName);
                })
                .collect(Collectors.toList());
        // 创建目标文件路径
        Path destPath = Paths.get(basePath, filePath);
        Files.createDirectories(destPath.getParent());
        // 合并分片到目标文件
        try (WritableByteChannel outChannel = Files.newByteChannel(
                destPath,
                StandardOpenOption.CREATE,
                StandardOpenOption.WRITE,
                StandardOpenOption.TRUNCATE_EXISTING)) {
            for (Path part : partPaths) {
                try (FileChannel inChannel = FileChannel.open(part, StandardOpenOption.READ)) {
                    inChannel.transferTo(0, inChannel.size(), outChannel);
                }
            }
        }
        // 清理临时文件
        Path tempDir = Paths.get(basePath, "temp_uploads", uploadId);
        Files.walk(tempDir).sorted(Comparator.reverseOrder()).map(Path::toFile).forEach(File::delete);
        uploadMetadata.remove(uploadId);
        log.info("分片合并完成: 文件={}, uploadId={}, 分片数={}", filePath, uploadId, filePartETags.size());
        return filePath;
    }

    public String getClientName() {
        return clientName;
    }

    public String getBasePath() {
        return basePath;
    }

    public String getPermission() {
        return permission;
    }

    public String getApi() {
        return api;
    }
}
