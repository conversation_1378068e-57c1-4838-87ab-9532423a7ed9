package com.ruoyi.tingwu.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.tingwu.domain.TingwuTrans;
import com.ruoyi.tingwu.mapper.TingwuTransMapper;
import com.ruoyi.tingwu.service.ITingwuTransService;

/**
 * 音视频任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class TingwuTransServiceImpl implements ITingwuTransService {
    @Autowired
    private TingwuTransMapper tingwuTransMapper;

    /**
     * 查询音视频任务
     *
     * @param vaId 音视频任务主键
     * @return 音视频任务
     */
    @Override
    public TingwuTrans selectTingwuTransByVaId(Long vaId) {
        return tingwuTransMapper.selectTingwuTransByVaId(vaId);
    }

    /**
     * 查询音视频任务列表
     *
     * @param tingwuTrans 音视频任务
     * @return 音视频任务
     */
    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<TingwuTrans> selectTingwuTransList(TingwuTrans tingwuTrans) {
        // 设置排序参数
        return tingwuTransMapper.selectTingwuTransList(tingwuTrans);
    }

    /**
     * 新增音视频任务
     *
     * @param tingwuTrans 音视频任务
     * @return 结果
     */
    @Override
    public int insertTingwuTrans(TingwuTrans tingwuTrans) {
        tingwuTrans.setCreateTime(DateUtils.getNowDate());
        tingwuTrans.setCreateBy(SecurityUtils.getUsername());
        return tingwuTransMapper.insertTingwuTrans(tingwuTrans);
    }

    /**
     * 修改音视频任务
     *
     * @param tingwuTrans 音视频任务
     * @return 结果
     */
    @Override
    public int updateTingwuTrans(TingwuTrans tingwuTrans) {
        tingwuTrans.setUpdateTime(DateUtils.getNowDate());
        return tingwuTransMapper.updateTingwuTrans(tingwuTrans);
    }

    /**
     * 批量删除音视频任务
     *
     * @param vaIds 需要删除的音视频任务主键
     * @return 结果
     */
    @Override
    public int deleteTingwuTransByVaIds(Long[] vaIds) {
        return tingwuTransMapper.deleteTingwuTransByVaIds(vaIds);
    }

    /**
     * 删除音视频任务信息
     *
     * @param vaId 音视频任务主键
     * @return 结果
     */
    @Override
    public int deleteTingwuTransByVaId(Long vaId) {
        return tingwuTransMapper.deleteTingwuTransByVaId(vaId);
    }

    /**
     * 根据编号修改数据
     */
    @Override
    public int updateTingwuTransByTaskId(TingwuTrans tingwuTrans) {
        return tingwuTransMapper.updateTingwuTransByTaskId(tingwuTrans);
    }
}
