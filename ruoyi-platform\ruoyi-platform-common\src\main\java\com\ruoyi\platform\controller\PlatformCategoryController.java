package com.ruoyi.platform.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformCategory;
import com.ruoyi.platform.service.IPlatformCategoryService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 分类Controller
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@RestController
@RequestMapping("/platform/category")
@Tag(name = "【分类】管理")
public class PlatformCategoryController extends BaseController
{
    @Autowired
    private IPlatformCategoryService platformCategoryService;

    /**
     * 查询分类列表
     */
    @Operation(summary = "查询分类列表")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformCategory platformCategory)
    {
        List<PlatformCategory> list = platformCategoryService.selectPlatformCategoryList(platformCategory);
        return getDataTable(list);
    }

    /**
     * 导出分类列表
     */
    @Operation(summary = "导出分类列表")
    @Log(title = "导出分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformCategory platformCategory)
    {
        List<PlatformCategory> list = platformCategoryService.selectPlatformCategoryList(platformCategory);
        ExcelUtil<PlatformCategory> util = new ExcelUtil<PlatformCategory>(PlatformCategory.class);
        util.exportExcel(response, list, "分类数据");
    }

    /**
     * 获取分类详细信息
     */
    @Operation(summary = "获取分类详细信息")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return success(platformCategoryService.selectPlatformCategoryByCategoryId(categoryId));
    }

    /**
     * 新增分类
     */
    @Operation(summary = "新增分类")
    @Log(title = "新增分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformCategory platformCategory)
    {
        platformCategory.setCreateBy(getUsername());
        platformCategory.setUpdateBy(getUsername());
        return toAjax(platformCategoryService.insertPlatformCategory(platformCategory));
    }

    /**
     * 修改分类
     */
    @Operation(summary = "修改分类")
    @Log(title = "修改分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformCategory platformCategory)
    {
        platformCategory.setUpdateBy(getUsername());
        return toAjax(platformCategoryService.updatePlatformCategory(platformCategory));
    }

    /**
     * 删除分类
     */
    @Operation(summary = "删除分类")
    @Log(title = "删除分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryId}")
    public AjaxResult remove(@PathVariable(name = "categoryId") Long categoryId,@RequestParam Map<String, Object> params) throws Exception {
        return toAjax(platformCategoryService.deletePlatformCategoryByCategoryId(categoryId, params));
    }

    /**
     * 根据分类ids查询--分类名称
     */
    @Operation(summary = "根据分类ids查询--分类名称")
    @GetMapping("/listForOption")
    public Map<Long, String> getCategoryTitles(@RequestParam(name="categoryId") int[] categoryId) {
        return platformCategoryService.getCategoryTitlesByIds(categoryId);
    }

    /**
     * 检查分类下的资源
     */
    @Operation(summary = "检查分类下面的资源")
    @GetMapping("/checkResources/{categoryId}")
    public AjaxResult checkCategoryHasResources(@PathVariable Long categoryId) {
        boolean result = platformCategoryService. checkProjectHasCategory(categoryId) ||
        platformCategoryService.checkProjectKeyWord(categoryId) || platformCategoryService.checkProjectAudio(categoryId);
        return success(result);
    }
}
