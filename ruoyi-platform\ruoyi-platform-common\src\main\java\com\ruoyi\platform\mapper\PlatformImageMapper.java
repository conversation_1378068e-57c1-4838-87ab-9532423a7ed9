package com.ruoyi.platform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformImage;

/**
 * 形象管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface PlatformImageMapper 
{
    /**
     * 查询形象管理
     * 
     * @param imageId 形象管理主键
     * @return 形象管理
     */
    public PlatformImage selectPlatformImageByImageId(Long imageId);

    /**
     * 查询形象管理列表
     * 
     * @param platformImage 形象管理
     * @return 形象管理集合
     */
    public List<PlatformImage> selectPlatformImageList(PlatformImage platformImage);

    /**
     * 新增形象管理
     * 
     * @param platformImage 形象管理
     * @return 结果
     */
    public int insertPlatformImage(PlatformImage platformImage);

    /**
     * 修改形象管理
     * 
     * @param platformImage 形象管理
     * @return 结果
     */
    public int updatePlatformImage(PlatformImage platformImage);

    /**
     * 删除形象管理
     * 
     * @param imageId 形象管理主键
     * @return 结果
     */
    public int deletePlatformImageByImageId(Long imageId);

    /**
     * 批量删除形象管理
     * 
     * @param imageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformImageByImageIds(Long[] imageIds);

    /**
     * 根据多个形象管理主键查询形象管理列表
     * 
     * @param imageIds 形象管理主键数组
     * @return 形象管理列表
     */
    public List<PlatformImage> selectPlatformImagesByImageIds(Long[] imageIds);

    /**
     * 根据条件查询形象记录
     * 
     * @param condition 查询条件
     * @return 形象对象列表
     */
    public List<PlatformImage> selectImagesByCondition(Map<String, Object> condition);
}
