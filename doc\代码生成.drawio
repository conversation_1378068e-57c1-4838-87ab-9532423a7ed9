<mxfile host="65bd71144e">
    <diagram id="xy79Wy17eWdTJqY27ViR" name="第 1 页">
        <mxGraphModel dx="892" dy="563" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="代码生成" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="205" y="20" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="预览代码" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="525" y="20" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="GenTableServiceImpl.previewCode" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="150" y="120" width="220" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="GenTableServiceImpl.downloadCode" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="100" width="240" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="GenTableServiceImpl.generatorCode" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="190" width="240" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="17">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="580" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="GenTableMapper.selectGenTableById&amp;nbsp; &amp;nbsp;查询表信息" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="230" y="330" width="310" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="17" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="GenTableServiceImpl.setSubTable&amp;nbsp; 设置主子表信息" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="237.5" y="420" width="297.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="18" target="20">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="GenTableServiceImpl.setPkColumn&amp;nbsp; 设置主键列信息" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="232.5" y="515" width="307.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="20" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="VelocityInitializer.initVelocity&amp;nbsp; &amp;nbsp;初始化vm方法" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="242.5" y="595" width="287.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="22" target="24">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="VelocityUtils.prepareContext&amp;nbsp; &lt;font color=&quot;#ff0000&quot;&gt;设置模板变量信息&lt;/font&gt;" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="242.5" y="685" width="287.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="24" target="26">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="VelocityUtils.getTemplateList&amp;nbsp; &lt;font color=&quot;#ff0000&quot;&gt;获取模板列表&lt;/font&gt;" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="242.5" y="770" width="287.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="26" target="29">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="Velocity.getTemplate 渲染模板" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="242.5" y="860" width="287.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="预览代码 -&amp;gt; 结果储存到dataMap中&lt;br&gt;自定义路径 -&amp;gt; 通过FileUtils写入到指定位置&lt;br&gt;下载代码 -&amp;gt; 将生抽的信息添加到zip然后下载" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="242.5" y="950" width="287.5" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>