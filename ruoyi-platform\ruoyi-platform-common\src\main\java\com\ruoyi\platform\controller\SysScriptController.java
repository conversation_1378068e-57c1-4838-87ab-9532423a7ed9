package com.ruoyi.platform.controller;

import java.util.List;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.SysScript;
import com.ruoyi.platform.service.ISysScriptService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 脚本Controller
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@RestController
@RequestMapping("/system/script")
@Tag(name = "【脚本】管理")
public class SysScriptController extends BaseController {
    @Autowired
    private ISysScriptService sysScriptService;

    /**
     * 查询脚本列表
     */
    @Operation(summary = "查询脚本列表")
    @PreAuthorize("@ss.hasPermi('system:script:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysScript sysScript) {
        startPage();
        List<SysScript> list = sysScriptService.selectSysScriptList(sysScript);
        return getDataTable(list);
    }

    /**
     * 导出脚本列表
     */
    @Operation(summary = "导出脚本列表")
    @PreAuthorize("@ss.hasPermi('system:script:export')")
    @Log(title = "脚本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysScript sysScript) {
        List<SysScript> list = sysScriptService.selectSysScriptList(sysScript);
        ExcelUtil<SysScript> util = new ExcelUtil<SysScript>(SysScript.class);
        util.exportExcel(response, list, "脚本数据");
    }

    /**
     * 获取脚本详细信息
     */
    @Operation(summary = "获取脚本详细信息")
    @PreAuthorize("@ss.hasPermi('system:script:query')")
    @GetMapping(value = "/{scriptId}")
    public AjaxResult getInfo(@PathVariable("scriptId") Long scriptId) {
        return success(sysScriptService.selectSysScriptByScriptId(scriptId));
    }

    /**
     * 获取脚本内容
     */
    @Operation(summary = "获取脚本内容")
    @Anonymous
    @GetMapping(value = "/content/{scriptId}")
    public void getScript(@PathVariable("scriptId") Long scriptId, HttpServletResponse response) throws Exception {
        response.setContentType("text/javascript; charset=UTF-8");
        SysScript sysScript = sysScriptService.selectSysScriptByScriptId(scriptId);
        String eTag = String.valueOf(sysScript.getUpdateTime().getTime());
        response.setHeader("ETag", eTag);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.write(sysScript.getContent().getBytes("UTF-8"), outputStream);
        outputStream.flush();
        outputStream.close();
    }

    /**
     * 新增脚本
     */
    @Operation(summary = "新增脚本")
    @PreAuthorize("@ss.hasPermi('system:script:add')")
    @Log(title = "脚本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysScript sysScript) {
        return toAjax(sysScriptService.insertSysScript(sysScript));
    }

    /**
     * 修改脚本
     */
    @Operation(summary = "修改脚本")
    @PreAuthorize("@ss.hasPermi('system:script:edit')")
    @Log(title = "脚本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysScript sysScript) {
        return toAjax(sysScriptService.updateSysScript(sysScript));
    }

    /**
     * 删除脚本
     */
    @Operation(summary = "删除脚本")
    @PreAuthorize("@ss.hasPermi('system:script:remove')")
    @Log(title = "脚本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scriptIds}")
    public AjaxResult remove(@PathVariable(name = "scriptIds") Long[] scriptIds) {
        return toAjax(sysScriptService.deleteSysScriptByScriptIds(scriptIds));
    }
}
