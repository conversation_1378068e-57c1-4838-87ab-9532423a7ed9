package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformGoods;

/**
 * 产品管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
public interface IPlatformGoodsService 
{
    /**
     * 查询产品管理
     * 
     * @param goodsId 产品管理主键
     * @return 产品管理
     */
    public PlatformGoods selectPlatformGoodsByGoodsId(Long goodsId);

    /**
     * 查询产品管理列表
     * 
     * @param platformGoods 产品管理
     * @return 产品管理集合
     */
    public List<PlatformGoods> selectPlatformGoodsList(PlatformGoods platformGoods);

    /**
     * 新增产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    public int insertPlatformGoods(PlatformGoods platformGoods);

    /**
     * 修改产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    public int updatePlatformGoods(PlatformGoods platformGoods);

    /**
     * 批量删除产品管理
     * 
     * @param goodsIds 需要删除的产品管理主键集合
     * @return 结果
     */
    public int deletePlatformGoodsByGoodsIds(Long[] goodsIds);

    /**
     * 删除产品管理信息
     * 
     * @param goodsId 产品管理主键
     * @return 结果
     */
    public int deletePlatformGoodsByGoodsId(Long goodsId);
    
    //根据产品的Ids查找多个产品名称
    public Map<Long, String> getGoodsIdByNameIds(List<Long> ids);

    /**
     * 根据直播ID获取产品信息
     */
    public List<PlatformGoods> getGoodsByLiveId(Long liveId);

}
