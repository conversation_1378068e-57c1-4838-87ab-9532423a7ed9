package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.service.IPlatformAudioService;

import io.jsonwebtoken.io.IOException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 音频管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-21
 */
@RestController
@RequestMapping("/platform/audio")
@Tag(name = "【音频管理】管理")
public class PlatformAudioController extends BaseController {

    @Autowired
    private IPlatformAudioService platformAudioService;

    /**
     * 查询音频管理列表
     */
    @Operation(summary = "查询音频管理列表")
    // @PreAuthorize("@ss.hasPermi('platform:audio:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformAudio platformAudio) {
        startPage();
        List<PlatformAudio> list = platformAudioService.selectPlatformAudioList(platformAudio);
        return getDataTable(list);
    }

    /**
     * 导出音频管理列表
     */
    @Operation(summary = "导出音频管理列表")
    @PreAuthorize("@ss.hasPermi('platform:audio:export')")
    @Log(title = "导出音频", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformAudio platformAudio) {
        List<PlatformAudio> list = platformAudioService.selectPlatformAudioList(platformAudio);
        ExcelUtil<PlatformAudio> util = new ExcelUtil<PlatformAudio>(PlatformAudio.class);
        util.exportExcel(response, list, "音频管理数据");
    }

    /**
     * 获取音频管理详细信息
     */
    @Operation(summary = "获取音频管理详细信息")
    // @PreAuthorize("@ss.hasPermi('platform:audio:query')")
    @GetMapping(value = "/{audioId}")
    public AjaxResult getInfo(@PathVariable("audioId") Long audioId) {
        return success(platformAudioService.selectPlatformAudioByAudioId(audioId));
    }

    /**
     * 新增音频管理
     */
    @Operation(summary = "新增音频管理")
    // @PreAuthorize("@ss.hasPermi('platform:audio:add')")
    @Log(title = "新增音频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformAudio platformAudio) {
        platformAudio.setCreateBy(getUsername()); // 创建人
        platformAudio.setUpdateBy(getUsername()); // 修改时间
        return toAjax(platformAudioService.insertPlatformAudio(platformAudio));
    }

    /**
     * 修改音频管理
     */
    @Operation(summary = "修改音频管理")
    // @PreAuthorize("@ss.hasPermi('platform:audio:edit')")
    @Log(title = "编辑音频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformAudio platformAudio) {
        platformAudio.setUpdateBy(getUsername());
        return toAjax(platformAudioService.updatePlatformAudio(platformAudio));
    }

    /**
     * 删除音频管理
     */
    @Operation(summary = "删除音频管理")
    // @PreAuthorize("@ss.hasPermi('platform:audio:remove')")
    @Log(title = "删除音频", businessType = BusinessType.DELETE)
    @DeleteMapping("/{audioIds}")
    public AjaxResult remove(@PathVariable(name = "audioIds") Long[] audioIds) {
        if (audioIds == null || audioIds.length == 0) {
            return AjaxResult.error("音频ID不能为空！");
        }
        try {
            int totalDeletedCount = platformAudioService.deletePlatformAudioByAudioIds(audioIds);
            if (totalDeletedCount > 0) {
                return AjaxResult.success("删除成功！");
            } else {
                return AjaxResult.error("未删除任何音频");
            }
        } catch (Exception e) {
            return AjaxResult.error("删除过程中发生错误：" + e.getMessage());
        }
    }

    @Operation(summary = "上传音频文件")
    @PostMapping("/uploadAudio")
    public R<Long> uploadAudio(@RequestParam("file") MultipartFile file, @RequestParam("categoryId") Long categoryId,
            @RequestParam(value = "filePath", required = false) String filePath) throws Exception {
        try {
            Long audioId = platformAudioService.uploadAudio(file, filePath, categoryId);
            return R.ok(audioId);
        } catch (Exception e) {
            return R.fail("上传失败: " + e.getMessage());
        }
    }

    @Operation(summary = "通用下载音频文件")
    @GetMapping("/downloadAudio")
    public void downloadAudio(@RequestParam("fileUrl") String fileUrl, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(fileUrl)) {
            throw new ServiceException("存储的音频文件地址不正确！");
        }
        try {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(fileUrl));
            FileOperateUtils.downLoad(fileUrl, response.getOutputStream());
        } catch (IOException e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("下载失败，请稍后重试: " + e.getMessage());
        }
    }

    @Operation(summary = "根据音频Id下载")
    @Deprecated(since = "1.1.0", forRemoval = true) // 为true在后面版本号里面会弃用
    @GetMapping("downloadAudioId")
    public void downloadAudioId(@RequestParam("audioId") Long audioId, HttpServletResponse response,
            @RequestParam(name = "idToName", required = false, defaultValue = "false") Boolean idToName)
            throws Exception {
        try {
            platformAudioService.downloadAudioId(audioId, idToName, response);
        } catch (Exception e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setContentType("text/plain");
            response.getWriter().write("下载失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取音频临时URL用于在线播放")
    @GetMapping("/getUrlByAudioPath")
    public R<String> getUrlByAudioPath(@RequestParam("audioPath") String audioPath) {
        if (StringUtils.isEmpty(audioPath)) {
            return R.fail("音频路径不能为空");
        }
        try {
            String temporaryUrl = platformAudioService.getTemporaryUrlByPath(audioPath);
            return R.ok(temporaryUrl);
        } catch (Exception e) {
            return R.fail("获取音频URL失败: " + e.getMessage());
        }
    }

    /**
     * 根据分类ID查询文本转音频数据
     */
    @Operation(summary = "根据分类ID查询文本转音频数据")
    @GetMapping("/audioAndText")
    public TableDataInfo audioAndText(PlatformAudio platformAudio) {
        startPage();
        List<PlatformAudio> list = platformAudioService.audioAndText(platformAudio);
        return getDataTable(list);
    }

    /**
     * 根据音频地址生成临时凭证
     */
    @Operation(summary = "根据音频地址生成临时凭证")
    @GetMapping("audioDetail")
    public AjaxResult audioDetail(@RequestParam String audioPath) {
        try {
            return success(platformAudioService.getAudioDetailByAddress(audioPath));
        } catch (Exception e) {
            return error("在当前服务没有找到该音频素材信息！");
        }
    }
}
