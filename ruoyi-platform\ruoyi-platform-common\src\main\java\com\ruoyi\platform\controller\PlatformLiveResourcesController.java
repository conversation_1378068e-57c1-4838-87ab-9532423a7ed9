package com.ruoyi.platform.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.RateLimiter;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.platform.domain.PlatformProject;
import com.ruoyi.platform.domain.vo.PlatformLiveVo;
import com.ruoyi.platform.service.IPlatformAnchorService;
import com.ruoyi.platform.service.IPlatformArticleService;
import com.ruoyi.platform.service.IPlatformAudioService;
import com.ruoyi.platform.service.IPlatformCategoryService;
import com.ruoyi.platform.service.IPlatformGoodsService;
import com.ruoyi.platform.service.IPlatformKeywordService;
import com.ruoyi.platform.service.IPlatformLiveService;
import com.ruoyi.platform.service.IPlatformProjectService;
import com.ruoyi.platform.service.IPlatformSceneconService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/platform/liveResources/{liveId}")
@Tag(name = "【直播资源信息】管理")
public class PlatformLiveResourcesController extends BaseController {

    @Autowired
    private IPlatformLiveService platformLiveService;

    @Autowired
    private IPlatformKeywordService platformKeywordService;

    @Autowired
    private IPlatformArticleService platformArticleService;

    @Autowired
    private IPlatformAudioService platformAudioService;

    @Autowired
    private IPlatformProjectService platformProjectService;

    @Autowired
    private IPlatformAnchorService platformAnchorService;

    @Autowired
    private IPlatformCategoryService platformCategoryService;

    @Autowired
    private IPlatformGoodsService platformGoodsService;
    
    @Autowired
    private IPlatformSceneconService platformSceneconService;

    @Operation(summary = "查询直播相关音频列表")
    @GetMapping("/audioList")
    @RateLimiter(time=60, count=3)
    public R<Map<Long, List<Long>>> audioList(@PathVariable("liveId") Long liveId) {
        return R.ok(platformLiveService.selectAudioList(liveId));
    }

    @Operation(summary = "获取单场直播所有信息")
    @GetMapping(value = "/vo")
    @RateLimiter(time=60, count=3)
    public R<PlatformLiveVo> getAllInfoVo(@PathVariable("liveId") Long liveId) {
        return R.ok(platformLiveService.selectPlatformLiveVoByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询直播信息")
    @GetMapping("/live")
    @RateLimiter(time=60, count=3)
    public TableDataInfo selectLiveByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformLiveService.selectLive(liveId));
    }

    @Operation(summary = "根据直播Id获取关键词列表")
    @GetMapping("/keyword")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getKeywordsByLiveId(@PathVariable("liveId") Long liveId) {
        return getDataTable(platformKeywordService.getKeywordsByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询文案信息")
    @GetMapping("/article")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getArticlesByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformArticleService.getArticlesByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询音频信息")
    @GetMapping("/audio")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getAudiosByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformAudioService.getAudiosByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询项目信息")
    @GetMapping("/project")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getProjectByLiveId(@PathVariable Long liveId) {
        PlatformProject project = platformProjectService.getProjectByLiveId(liveId);
        return getDataTable(project != null ? List.of(project) : Collections.emptyList());
    }

    @Operation(summary = "根据直播id查询智能主播信息")
    @GetMapping("/anchor")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getAnchorsByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformAnchorService.getAnchorsByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询分类信息")
    @GetMapping("/category")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getCategoriesByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformCategoryService.getCategoriesByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询产品信息")
    @GetMapping("/goods")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getGoodsByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformGoodsService.getGoodsByLiveId(liveId));
    }

    @Operation(summary = "根据直播id查询场控信息")
    @GetMapping("/scenecon")
    @RateLimiter(time=60, count=3)
    public TableDataInfo getSceneconByLiveId(@PathVariable Long liveId) {
        return getDataTable(platformSceneconService.getSceneconByLiveId(liveId));
    }
}
