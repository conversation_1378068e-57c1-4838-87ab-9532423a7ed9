package com.ruoyi.platform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.platform.domain.PlatformConsumption;
import com.ruoyi.platform.domain.vo.PlatformConsumptionVo;

/**
 * 用户算力点数变化记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface IPlatformConsumptionService 
{
    /**
     * 查询用户算力点数变化记录
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 用户算力点数变化记录
     */
    public PlatformConsumption selectPlatformConsumptionByConsumptionId(Long consumptionId);

    /**
     * 查询用户算力点数变化记录列表
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 用户算力点数变化记录集合
     */
    public List<PlatformConsumption> selectPlatformConsumptionList(PlatformConsumption platformConsumption);

    /**
     * 新增用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    public int insertPlatformConsumption(PlatformConsumption platformConsumption);

    /**
     * 修改用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    public int updatePlatformConsumption(PlatformConsumption platformConsumption);

    /**
     * 批量删除用户算力点数变化记录
     * 
     * @param consumptionIds 需要删除的用户算力点数变化记录主键集合
     * @return 结果
     */
    public int deletePlatformConsumptionByConsumptionIds(Long[] consumptionIds);

    /**
     * 删除用户算力点数变化记录信息
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 结果
     */
    public int deletePlatformConsumptionByConsumptionId(Long consumptionId);

    /**
     * 查询并汇总某个用户在过去一年内每个业务模块的算力消费情况。
     *
     * @param userName 用户名称
     * @return 用户在各业务模块上的算力消费汇总及占比
     */
     public List<PlatformConsumptionVo> summarizeUserConsumption(String userName);

    /**
     * 获取算力消费统计数据
     * 
     * @return 统计数据，包含总消费量、日消费量、月消费量等信息
     */
    public Map<String, Object> getConsumptionStatistics();
}
