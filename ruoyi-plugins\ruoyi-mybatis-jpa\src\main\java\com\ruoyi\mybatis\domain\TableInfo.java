package com.ruoyi.mybatis.domain;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.core.annotation.AnnotationUtils;

import com.ruoyi.mybatis.annotation.Column;
import com.ruoyi.mybatis.annotation.ColumnMap;
import com.ruoyi.mybatis.annotation.JoinTable;
import com.ruoyi.mybatis.annotation.Table;

/**
 * 数据库表信息
 *
 * <AUTHOR>
 */
public class TableInfo {
    private String tableName;
    private String alias;
    private Table table;
    private boolean hasDataScopeValue;
    private List<ColumnInfo> columns = new ArrayList<>(); // 所有标记column注解的字段
    private List<ColumnInfo> primaryKeys = new ArrayList<>();
    private List<MapColumnInfo> mapColumns = new ArrayList<>();
    private List<String> joinSql = new ArrayList<>();

    public TableInfo(Class<?> cls) {
        this.table = AnnotationUtils.findAnnotation(cls, Table.class);
        if (this.table == null)
            throw new RuntimeException("error , not find tableName");
        this.tableName = this.table.name();
        this.alias = this.table.alias();
        this.hasDataScopeValue = this.table.dataScope();

        // 获取所有字段
        Arrays.stream(cls.getDeclaredFields())
                .filter(field -> AnnotationUtils.findAnnotation(field, Column.class) != null)
                .map(ColumnInfo::new)
                .forEach(this.columns::add);

        // 获取所有引入的字段
        Arrays.stream(cls.getDeclaredFields())
                .filter(field -> AnnotationUtils.findAnnotation(field, ColumnMap.class) != null)
                .map(MapColumnInfo::new)
                .forEach(this.mapColumns::add);

        // 获取所有主键
        this.getColumns().stream()
                .filter(ColumnInfo::isPrimaryKey)
                .forEach(this.primaryKeys::add);

        // 获取所有关联表语句
        Stream.of(cls.getAnnotationsByType(JoinTable.class))
                .sorted(new Comparator<JoinTable>() {
                    @Override
                    public int compare(JoinTable o1, JoinTable o2) {
                        return o1.num() - o2.num();
                    }
                }).map(jt -> String.format(
                        "%s %s on %s.%s = %s.%s",
                        jt.targetName(),
                        jt.targetAlias(),
                        jt.targetAlias(),
                        jt.targetKey(),
                        jt.sourceName(),
                        jt.sourceKey()))
                .forEach(joinSql::add);
    }

    public String[] getOrderBy() {
        return this.table.orderBy();
    }

    public boolean hasDataScope() {
        return this.hasDataScopeValue;
    }

    public List<String> getQueryColumnNames() {
        List<String> columns = Arrays.asList(this.table.columns());
        if (columns.size() <= 0) {
            columns = this.columns.stream()
                    .map(ColumnInfo::getColumnName)
                    .map(column -> String.format("%s.%s", this.alias, column))
                    .collect(Collectors.toList());
        }
        if (this.mapColumns.size() != 0) {
            this.mapColumns.stream()
                    .map(MapColumnInfo::getFullyQualifiedColumnName)
                    .map(column -> String.format(column))
                    .forEach(columns::add);
        }
        return columns;

    }

    public List<String> getColumnNames() {
        List<String> columns = this.columns.stream()
                .map(ColumnInfo::getColumnName)
                .collect(Collectors.toList());

        columns = columns.stream()
                .map(column -> this.alias + "." + column)
                .collect(Collectors.toList());
        this.mapColumns.stream()
                .map(MapColumnInfo::getFullyQualifiedColumnName)
                .forEach(columns::add);

        return columns;
    }

    /**
     * 获取该对象可查询非空字段
     * 
     * @param <T>
     * @param entity
     * @return
     */
    public <T> List<ColumnInfo> getNotNullColumnsForQuery(T entity) {
        return this.columns.stream()
                .filter(column -> column.fieldQueryIsNotNull(entity))
                .collect(Collectors.toList());
    }

    /**
     * 获取该对象非空字段
     * 
     * @param <T>
     * @param entity
     * @return
     */
    public <T> List<ColumnInfo> getNotNullColumns(T entity) {
        return this.columns.stream()
                .filter(column -> column.fieldIsNotNull(entity))
                .collect(Collectors.toList());
    }

    /**
     * 获取该对象可查询非空映射字段
     * 
     * @param <T>
     * @param entity
     * @return
     */
    public <T> List<MapColumnInfo> getNotNullMapColumnsForQuery(T entity) {
        return this.mapColumns.stream()
                .filter(column -> column.fieldQueryIsNotNull(entity))
                .collect(Collectors.toList());
    }

    /**
     * 获取该对象非空映射字段
     * 
     * @param <T>
     * @param entity
     * @return
     */
    public <T> List<MapColumnInfo> getNotNullMapColumns(T entity) {
        return this.mapColumns.stream()
                .filter(column -> column.fieldIsNotNull(entity))
                .collect(Collectors.toList());
    }

    public List<ColumnInfo> getPrimaryKeys() {
        return primaryKeys;
    }

    public String getTableName() {
        return tableName;
    }

    public List<ColumnInfo> getColumns() {
        return columns;
    }

    public List<MapColumnInfo> getMapColumns() {
        return mapColumns;
    }

    public List<String> getJoinSql() {
        return this.joinSql;
    }

    public String getAlias() {
        return this.alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
