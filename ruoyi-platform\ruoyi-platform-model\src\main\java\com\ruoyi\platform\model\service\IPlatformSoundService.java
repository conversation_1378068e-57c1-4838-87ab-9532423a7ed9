package com.ruoyi.platform.model.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.platform.model.domain.PlatformSound;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 声音管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IPlatformSoundService 
{
    /**
     * 查询声音管理
     * 
     * @param soundId 声音管理主键
     * @return 声音管理
     */
    public PlatformSound selectPlatformSoundBySoundId(Long soundId);

    /**
     * 查询声音管理列表
     * 
     * @param platformSound 声音管理
     * @return 声音管理集合
     */
    public List<PlatformSound> selectPlatformSoundList(PlatformSound platformSound);

    /**
     * 新增声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    public int insertPlatformSound(PlatformSound platformSound);

    /**
     * 修改声音管理
     * 
     * @param platformSound 声音管理
     * @return 结果
     */
    public int updatePlatformSound(PlatformSound platformSound);

    /**
     * 批量删除声音管理
     * 
     * @param soundIds 需要删除的声音管理主键集合
     * @return 结果
     */
    public int deletePlatformSoundBySoundIds(Long[] soundIds);

    /**
     * 删除声音管理信息
     * 
     * @param soundId 声音管理主键
     * @return 结果
     */
    public int deletePlatformSoundBySoundId(Long soundId);

    //根据任务的状态去决定是成功还是失败 2：成功 3：失败
    public int updateSoundStaus(Long soundId,Long soundStatus);

    //根据声音id 去修改模型的文件地址
    public int updateModelSoundService(PlatformSound platformSound);

    //添加待训练声音文件-
    public Object uploadSoundtrainAndRef(String soundName, MultipartFile trainAudio, MultipartFile refAudio, String refText, Long deptId, String filtration);

    
    //添加待训练声音文件-第一步
    public Object uploadSoundtrainStepOne( MultipartFile trainAudio);

    //添加待训练声音文件-第二步
    public Object uploadSoundrefStepTwo(String tempId, String soundName,  MultipartFile refAudio, String refText, Long deptId, String filtration);
    
    //上传声音文件训练接口
    public String uploadSoundtrain(MultipartFile file, String filePath) throws Exception;

    //声音参考音频通用上传接口
    public String uploadSoundref(MultipartFile file, String filePath) throws Exception;

    //根据声音Id下载参考音频
    public void downloadAudioId(Long soundId, HttpServletResponse response) throws Exception;

    //声音模型下载
    public void downloadModel(Long soundId, String model, String machineCode, HttpServletResponse response) throws Exception;
    
    //修改声音状态为待处理
    public int updateSoundState(PlatformSound platformSound);

    //重新训练接口
    public int retrainAndResetStatus(Long soundId);

    //根据声音id 模型类型下载模型
    public void downloadModelBySoundId(Long soundId, String model, HttpServletResponse response) throws Exception;

    //训练审核
    public int soundAudit(Long soundId, boolean isApproved);

    //创建者查询声音，数据公开 1
    public List<PlatformSound> selectSoundList();

    /**
     * 获取声音文件的存储地址和临时访问凭证
     * 
     * @param soundId 声音ID
     * @return 包含文件地址和临时凭证的映射
     */
    public Map<String, Map<String, String>> getSoundFileUrls(Long soundId) throws Exception;

    /**
     * 初始化声音模型分片上传
     */
    public Object initSoundUpload(String soundName, String gptFileName, String sovitsFileName, String refAudioFileName, long gptFileSize, long sovitsFileSize, long refAudioFileSize, String refText, Long deptId, String soundFiltration) throws Exception;

    /**
     * 上传声音模型分片
     */
    public Object uploadSoundChunk(String uploadId, String fileType, String filePath, int chunkIndex, MultipartFile chunk) throws Exception;

    /**
     * 完成声音模型分片上传
     */
    public Object completeSoundUpload(String soundName, String uploadId, String gptFilePath, String sovitsFilePath, String refAudioFilePath, String refText, Long deptId, String soundFiltration) throws Exception;
}
