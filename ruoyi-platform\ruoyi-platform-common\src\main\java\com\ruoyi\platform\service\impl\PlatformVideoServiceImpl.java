package com.ruoyi.platform.service.impl;

import static com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatusAndVersion.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimerTask;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.GetMediaProducingJobRequest;
import com.aliyun.ice20201109.models.GetMediaProducingJobResponse;
import com.aliyun.ice20201109.models.GetMediaProducingJobResponseBody;
import com.aliyun.ice20201109.models.RegisterMediaInfoRequest;
import com.aliyun.ice20201109.models.RegisterMediaInfoResponse;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformHashrate;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.vo.DialogueSynthesisRequest;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformImageMapper;
import com.ruoyi.platform.mapper.PlatformModelMapper;
import com.ruoyi.platform.mapper.PlatformVideoMapper;
import com.ruoyi.platform.service.IPlatformHashrateService;
import com.ruoyi.platform.service.IPlatformModelService;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;
import com.ruoyi.platform.utils.taskUtils.HttpClientUtil;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoConfig;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatusAndVersion;

/**
 * 视频合成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class PlatformVideoServiceImpl implements IPlatformVideoService
{
    private static final Logger log = LoggerFactory.getLogger(PlatformVideoServiceImpl.class);
    @Autowired
    private PlatformVideoConfig platformVideoConfig;

    @Autowired
    private PlatformVideoMapper platformVideoMapper;

    @Autowired
    private IPlatformModelService platformModelService;

    @Autowired
    private PlatformModelMapper platformModelMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private PlatformImageMapper platformImageMapper;

    @Autowired
    private Client iceClientAK;

    @Autowired
    private IPlatformHashrateService platformHashrateService;

    private static final String PARAM_MODEL = "model";
    private static final String PARAM_MESSAGES = "messages";
    private static final String PARAM_CONTENT = "content";

    //查询视频合成
    @Override
    public PlatformVideo selectPlatformVideoById(Long id){
        return platformVideoMapper.selectPlatformVideoById(id);
    }

    // 查询视频合成列表
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo) {
        List<PlatformVideo> videoList = platformVideoMapper.selectPlatformVideoList(platformVideo);
        return videoList;
    }

    //修改视频合成
    @Override
    public int updatePlatformVideo(PlatformVideo platformVideo){
        return platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //批量删除视频合成
    @Override
    public int deletePlatformVideoByIds(Long[] ids){
        for (Long id : ids) {
            PlatformVideo video = selectPlatformVideoById(id);
            if (video != null && StringUtils.isNotEmpty(video.getResultVideo())) {
                if ("2".equals(video.getStatus())) {
                    throw new ServiceException("任务正在处理中，不能删除！");  // 为处理中不让删除
                }
                try {
                    FileOperateUtils.deleteFile(video.getResultVideo()); // 只删除结果视频
                } catch (Exception e) {
                    log.error("删除文件失败", e);
                }
            }
        }
        return platformVideoMapper.deletePlatformVideoByIds(ids);
    }

    // 查询状态为可用的模型
    @Override
    public List<PlatformModel> getAvailableModels() {
        PlatformModel queryModel = new PlatformModel();
        queryModel.setModelStatus(1); //0 禁用 1=可用
        return platformModelMapper.selectWyModelList(queryModel);
    }

    //查询待处理和处理中的任务
    @Override
    public List<PlatformVideo> selectPendingTasks() {
        PlatformVideo queryTask = new PlatformVideo();
        queryTask.setStatus(STATUS_PENDING);
        List<PlatformVideo> pendingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        queryTask.setStatus(STATUS_PROCESSING);
        List<PlatformVideo> processingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        pendingTasks.addAll(processingTasks);
        return pendingTasks;
    }


    //创建一条视频合成任务
    @Override
    public Long add(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskStatusAndVersion.M_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;
    }

    //查询视频合成单个任务
    @Override
    public PlatformVideo getOneTask(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo musetalkTask = platformVideoMapper.selectPlatformVideoOne(condition);
        if (musetalkTask == null) {
            log.info("没有待处理M版合成任务");
            return null;
        }

        musetalkTask.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(musetalkTask);
        return musetalkTask;
    }

    //根据版本查询视频合成单个任务
    @Override
    public PlatformVideo getOneTaskByVersion(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo task = platformVideoMapper.selectPlatformVideoOne(condition);
        if (task == null) {
            log.info("没有待处理"+version+"版合成任务");
            return null;
        }

        task.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(task);
        return task;
    }

    //根据动态任务结果调整任务的状态
    @Override
    public void updateTaskStatus(Long id, Long status, String resultVideo) {
        PlatformVideo platformVideo = new PlatformVideo();
        platformVideo.setId(id);
        platformVideo.setStatus(String.valueOf(status));
        platformVideo.setResultVideo(resultVideo);
        if (status == 4) {
            JSONObject operation = new JSONObject(); // 处理失败时的错误信息 - 存入JSON
            operation.put("errorResult", "Task processing failed"); 
            platformVideo.setOperation(operation.toString());
        }
        if (status == 3) {
            platformVideo.setCompleteAt(DateUtils.getNowDate()); //设置视频合成的完成时间
        }
        platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //上传音频文件
    @Override
    public Object uploadAudioFile(MultipartFile file){
        try {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("音频素材不能为空！");
            }
            long maxFileSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxFileSize) {
                throw new ServiceException("音频素材文件不能超过100MB！");
            }
            String documentFileName = file.getOriginalFilename();
            String filePath = "video/audio"; // 音频素材文件存储位置
            long timestamp = System.currentTimeMillis();
            String uniqueFileName = timestamp + "_" + documentFileName;
            String fullPath = filePath + "/" + uniqueFileName;
            fullPath = FileOperateUtils.upload(fullPath, file, null);
            String md5 = Md5Utils.getMd5(file);
            Map<String, String> result = new HashMap<>();
            result.put("url", fullPath);
            result.put("md5", md5);
            JSONObject operation = new JSONObject();
            operation.put("driven_audio_md5", md5);
            result.put("operation", operation.toString());
            return result; 
        } catch (IOException e) {
            throw new ServiceException("上传音频文件失败: " + e.getMessage()); // 返回失败响应queryVideoTask
        }
    }

    //查询视频合成任务(Map参数版本) v版
    @SuppressWarnings("unchecked")
    public PlatformVideo queryVideoTask(String model, Long taskId, Map<String, Object> params) {
        if (params != null) {
            if (!params.containsKey("messages") || !params.containsKey("model")) {
                throw new ServiceException("请求参数不完整");
            }
            List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get("messages");
            if (messages == null || messages.isEmpty()) {
                throw new ServiceException("messages不能为空");
            }
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get("content");
            if (content == null || !content.containsKey("task_id")) {
                throw new ServiceException("task_id不能为空");
            }
            model = params.get("model").toString();
            taskId = Long.valueOf(content.get("task_id").toString());
        }
        PlatformVideo task = selectPlatformVideoById(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }
        return task;
    }

    //上传媒体文件(视频或音频)
    @Override
    public Map<String, String> uploadMedia(MultipartFile file, String type) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }
        if (file.getSize() > 500 * 1024 * 1024) {
            throw new ServiceException("文件不能超过500MB");
        }
        // 根据类型选择不同的存储基础路径
        String basePath;
        if ("video".equals(type)) {
            basePath = "video/result";
        } else if ("audio".equals(type)) {
            basePath = "video/audio";
        } else {
            basePath = "video/other";
        }
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        // 使用时间戳的后6位作为前缀
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6)); // 取后六位
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp)); // 补零保证6位
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        String savedPath = FileOperateUtils.upload(fullPath, file, null);
        // 构建结果
        Map<String, String> result = new HashMap<>();
        result.put("savedPath", fullPath);     // 真实存储路径，用于数据库存储
        result.put("url", savedPath);          // 临时访问URL，用于前端展示
        result.put("type", type);               // 文件类型
        return result;
    }

    //创建数字人视频合成任务 v版
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> createVideoSynthesisWithUrls(Map<String, Object> params) throws Exception {
        Map<String, Object> originalParams = JSON.parseObject(JSON.toJSONString(params));
        if (!params.containsKey(PARAM_MODEL)) {
            throw new ServiceException("缺少模型编码");
        }
        String modelCode = params.get(PARAM_MODEL).toString();
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
        if (modelInfo == null || !modelInfo.getModelStatus().equals(1)) {
            throw new ServiceException("模型不可用");
        }
        List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
        if (messages != null && !messages.isEmpty()) {
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
            if (content != null) {
                if (content.containsKey("live_video_url")) {
                    String videoPath = content.get("live_video_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                    if (StringUtils.isEmpty(originalPath)) {
                        throw new ServiceException("视频路径无效");
                    }
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_video_url", tempUrl);
                    }
                }
                if (content.containsKey("live_sound_url")) {
                    String audioPath = content.get("live_sound_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_sound_url", tempUrl);
                    }
                }
            }
        }
        Map<String, Object> result = createVideoSynthesis(params);
        saveVideoTask(originalParams, modelCode, result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void saveVideoTask(Map<String, Object> params, String modelCode, Map<String, Object> result) {
        try {
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(result.get("task_id").toString());
            task.setNumber(FileUrlUtils.generateVideoName());
            task.setVersion("V");
            task.setStatus(result.getOrDefault("status", STATUS_PENDING).toString());

            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            task.setCreateBy(username);
            task.setUpdateBy(username);
            task.setCreatedAt(nowDate);
            task.setUpdatedAt(nowDate);

            if (params.containsKey(PARAM_MESSAGES)) {
                List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
                if (!messages.isEmpty()) {
                    Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
                    if (content != null) {
                        if (content.containsKey("live_video_url")) {
                            String videoPath = content.get("live_video_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                            task.setDrivenVideo(originalPath);
                        }
                        if (content.containsKey("live_sound_url")) {
                            String audioPath = content.get("live_sound_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                            task.setDrivenAudio(originalPath);
                        }
                        task.setCallbackUrl(content.getOrDefault("callback_url", "").toString());
                    }
                }
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            JSONObject jsonBuilder = new JSONObject();
            jsonBuilder.put("video_priority", "0");
            jsonBuilder.put("code", result.getOrDefault("code", "200"));
            jsonBuilder.put("video_message", result.getOrDefault("msg", "任务创建成功"));
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                jsonBuilder.put("model_price", modelInfo.getModelVersion());
            }
            String jsonStr = jsonBuilder.toJSONString();
            task.setOperation(jsonStr);
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存任务记录失败");
            }
        } catch (Exception e) {
            throw new ServiceException("保存任务记录失败: " + e.getMessage());
        }
    }

    // 从临时凭证URL下载视频并重新上传到对象存储
    public static String downloadAndSaveVideo(String temporaryUrl) throws Exception {
        String tempFileName = UUID.randomUUID().toString() + ".mp4";
        String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + tempFileName;
        File tempFile = new File(tempFilePath);

        // 重试机制
        int maxRetries = 3;
        Exception lastException = null;

        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                URL url = new URI(temporaryUrl).toURL();
                URLConnection conn = url.openConnection();
                conn.setConnectTimeout(30000);  // 30秒连接超时
                conn.setReadTimeout(120000);    // 120秒读取超时
                conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                try (InputStream inputStream = conn.getInputStream();
                    FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }

                // 下载成功，跳出重试
                break;

            } catch (Exception e) {
                lastException = e;
                log.warn("下载视频失败，重试 {}/{}: {}", retry + 1, maxRetries, e.getMessage());
                if (retry < maxRetries - 1) {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("下载被中断", ie);
                    }
                }
            }
        }

        // 检查下载是否成功
        if (!tempFile.exists() || tempFile.length() == 0) {
            throw new Exception("下载视频失败，重试" + maxRetries + "次后仍然失败: " +
                    (lastException != null ? lastException.getMessage() : "未知错误"));
        }

        // 上传到对象存储
        try {
            String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = timestamp + ".mp4";
            String address = "video/result";
            String fullPath = String.format("%s/%s/%s", address, currentDate, fileName);

            MultipartFile multipartFile = MultipartFileUtils.createFromFile(
                    tempFile, "file", fileName, "video/mp4");
            String savedPath = FileOperateUtils.upload(fullPath, multipartFile);
            return savedPath;

        } catch (Exception e) {
            log.error("上传视频文件失败: {}", e.getMessage(), e);
            throw new Exception("上传视频失败", e);
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFilePath);
                }
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createVideoSynthesis(Map<String, Object> params) {
        if (platformVideoConfig == null || StringUtils.isEmpty(platformVideoConfig.getServerUrl())) {
            throw new RuntimeException("视频合成配置不完整");  // 配置检查
        }
        String modelCode = String.valueOf(params.get(PARAM_MODEL));
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode); // 获取并记录模型价位信息
        String url = platformVideoConfig.getServerUrl().trim();
        if (!url.endsWith("/")) {
            url += "/";  // 构建完整URL - 确保正确拼接
        }
        url += platformVideoConfig.getSynthesisPath().trim().replaceFirst("^/", "");
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        try {
            String jsonBody = JSON.toJSONString(params);
            String response = HttpClientUtil.doPost(url, jsonBody, headers);
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();
            if (apiResponse != null) {
                result.put("code", apiResponse.getOrDefault("code", 500));
                result.put("msg", apiResponse.getOrDefault("msg", "未知错误"));
                Object dataObj = apiResponse.get("data"); // 获取data
                if (dataObj instanceof Map) {
                    Map<String, Object> data = (Map<String, Object>) dataObj;
                    Object taskId = data.get("task_id");  // 获取task_id
                    result.put("task_id", taskId != null ? taskId.toString() : "");
                    result.put("status", STATUS_PENDING);
                    // 添加模型价位信息到结果中
                    if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                        result.put("model_price", modelInfo.getModelVersion());
                    }
                } else {
                    result.put("task_id", "");
                    result.put("status", STATUS_FAILED);
                }
            } else {
                result.put("code", 500);
                result.put("msg", "API响应为空");
                result.put("task_id", "");
                result.put("status", STATUS_FAILED);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("创建视频合成任务失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> queryVideoSynthesis(String taskNo) {
        if (StringUtils.isEmpty(platformVideoConfig.getQueryPath())) {
            throw new ServiceException("查询接口路径未配置");
        }
        String url = platformVideoConfig.getServerUrl() + platformVideoConfig.getQueryPath();
        try {
            Map<String, Object> params = new HashMap<>();  // 构造查询参数
            params.put("model", "umi_video_v5");

            Map<String, Object> content = new HashMap<>();
            content.put("type", 2);
            content.put("task_id", taskNo);

            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            params.put("messages", messages);

            String jsonBody = JSON.toJSONString(params);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            String response = HttpClientUtil.doPost(url, jsonBody, headers);  // 发送POST请求
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();  // 构造标准响应
            result.put("taskNo", taskNo);
            result.put("code", apiResponse.getOrDefault("code", 500));
            result.put("message", apiResponse.getOrDefault("msg", "未知错误"));
            
            Map<String, Object> data = (Map<String, Object>) apiResponse.get("data"); // 处理data部分
            if (data != null) {
                Integer status = (Integer) data.get("status");
                PlatformVideo queryTask = new PlatformVideo(); // 查询任务信息
                queryTask.setTaskNo(taskNo);
                List<PlatformVideo> tasks = selectPlatformVideoList(queryTask);
                PlatformVideo task = !tasks.isEmpty() ? tasks.get(0) : null;
                
                // 如果任务存在，添加模型价位信息到结果中
                if (task != null && StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operationJson = JSONObject.parseObject(task.getOperation());
                        if (operationJson.containsKey("model_price")) {
                            String modelPrice = operationJson.getString("model_price");
                            result.put("modelPrice", modelPrice);
                        } else {
                            log.warn("任务[{}]的操作JSON中未找到model_price字段", task.getId());
                        }
                    } catch (Exception e) {
                        log.warn("解析操作JSON失败: {}", e.getMessage());
                    }
                }
                switch (status) {
                    case 1:
                        result.put("status", STATUS_PENDING);
                        result.put("message", STATUS_DESC_PENDING);
                        break;
                    case 2:
                        result.put("status", STATUS_PROCESSING);
                        result.put("message", STATUS_DESC_PROCESSING);
                        break;
                    case 3: // 成功状态
                        String videoUrl = data.get("complete_url").toString();
                        try {
                            if (task != null && StringUtils.isEmpty(task.getResultVideo())) {
                                // 下载并保存视频，返回的是相对路径
                                String savedPath = downloadAndSaveVideo(videoUrl);
                                
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", savedPath);
                                
                                PlatformVideo updateTask = new PlatformVideo();
                                updateTask.setId(task.getId());
                                updateTask.setStatus(STATUS_SUCCESS);
                                updateTask.setVideoMessage(STATUS_DESC_SUCCESS);
                                // 关键修改：确保存储的是相对路径，不是临时URL
                                updateTask.setResultVideo(savedPath); // 这里必须是相对路径
                                Date nowDate = new Date();
                                updateTask.setCompleteAt(nowDate);
                                updateTask.setUpdatedAt(nowDate);
                                updateTask.setUpdateBy(SecurityUtils.getUsername());
                                platformVideoMapper.updatePlatformVideo(updateTask);
                                
                                // 生成临时访问URL仅供前端显示使用
                                String tempUrl = FileOperateUtils.getURL(savedPath);
                                result.put("videoUrl", tempUrl);
                                result.put("realVideoUrl", savedPath); // 真实的相对路径
                            } else if (task != null && StringUtils.isNotEmpty(task.getResultVideo())) {
                                // 任务已处理过，直接生成临时访问URL
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", task.getResultVideo());
                                
                                // 生成临时访问URL供前端显示
                                String tempUrl = FileOperateUtils.getURL(task.getResultVideo());
                                result.put("videoUrl", tempUrl);
                            }
                        } catch (Exception e) {
                            log.error("处理视频URL失败");
                        }
                        break;
                    case 4:
                        result.put("status", STATUS_FAILED);
                        result.put("message", STATUS_DESC_FAILED);
                        if (task != null) {
                            task.setStatus(STATUS_FAILED);
                            task.setVideoMessage(STATUS_DESC_FAILED);
                            task.setUpdateTime(new Date());
                            task.setUpdateBy(SecurityUtils.getUsername());
                            platformVideoMapper.updatePlatformVideo(task);
                        } else {
                            log.warn("无法找到任务ID为 {} 的记录", taskNo);
                        }
                        break;
                    default:
                        throw new ServiceException("未知状态码: " + status);
                }
                if (task != null) {
                    result.put("taskId", task.getId()); // 添加其他任务信息
                    result.put("model", task.getModel());
                    result.put("videoName", task.getNumber());
                    // 处理源文件URL
                    if (StringUtils.isNotEmpty(task.getDrivenVideo())) {
                        String videoTempUrl = FileOperateUtils.getURL(task.getDrivenVideo());
                        result.put("liveVideoUrl", videoTempUrl.toString());
                    }
                    if (StringUtils.isNotEmpty(task.getDrivenAudio())) {
                        String audioTempUrl = FileOperateUtils.getURL(task.getDrivenAudio());
                        result.put("liveSoundUrl", audioTempUrl.toString());
                    }
                    result.put("createTime", task.getCreateTime());
                    result.put("updateTime", task.getUpdateTime());
                }
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建任务  合成 H 版视频
     */
    @Override
    public Long synthesisH(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskStatusAndVersion.H_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;

    }

    @Override
    public Map<String, Object> createDialogueSynthesisEnhanced(DialogueSynthesisRequest request) {
        validateDialogueRequest(request);
        String dialogueGroupId = "dialogue_" + System.currentTimeMillis();
        try {
            // 创建数字人对话合成任务
            List<Long> videoTaskIds = request.getDialogueContent().stream().map(dialogue -> {
                DialogueSynthesisRequest.DigitalHuman human = request.getDigitalHumans().stream()
                    .filter(h -> dialogue.getSpeaker().equals(h.getId())).findFirst()
                    .orElseThrow(() -> new ServiceException("找不到发言人配置: " + dialogue.getSpeakerName()));
                Map<String, String> audioResult = generateAudioForText(dialogue.getText(), human);
                return createVideoTask(audioResult.get("audioUrl"), audioResult.get("audioMd5"),
                    human, dialogue, request.getVersion(), request.getBboxShiftValue(), request.getModel(), dialogueGroupId);
            }).collect(Collectors.toList());
            // 启动自动监控任务
            if (Boolean.TRUE.equals(request.getAutoSynthesis())) {
                startAutoClipMonitoring(dialogueGroupId, request.getTitle(), request.getDescription(),
                    Boolean.TRUE.equals(request.getEnableSubtitles()));
            }
            Map<String, Object> result = new HashMap<>();
            result.put("videoTaskIds", videoTaskIds);
            result.put("dialogueGroupId", dialogueGroupId);
            result.put("autoSynthesis", request.getAutoSynthesis());
            result.put("enableSubtitles", request.getEnableSubtitles());

            // 构建更详细的消息
            String message = Boolean.TRUE.equals(request.getAutoSynthesis()) ?
                "数字人对话合成任务已创建，将自动监控并完成云剪辑合成" : "数字人对话合成任务已创建";

            if (Boolean.TRUE.equals(request.getEnableSubtitles())) {
                message += "（已启用字幕功能）";
            }

            result.put("message", message);
            return result;
        } catch (Exception e) {
            throw new ServiceException("创建数字人对话合成任务失败: " + e.getMessage());
        }
    }

    /**
     * 启动自动云剪辑监控任务（支持字幕）
     */
    private void startAutoClipMonitoring(String dialogueGroupId, String title, String description, boolean enableSubtitles) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                monitorAndAutoClip(dialogueGroupId, title, description, enableSubtitles);
            }
        });
    }

    /**
     * 监控对话组状态并自动执行云剪辑（支持字幕）
     */
    private void monitorAndAutoClip(String dialogueGroupId, String title, String description, boolean enableSubtitles) {
        int maxRetries = 120; // 20分钟，每10秒检查一次
        int checkInterval = 10000;
        for (int retryCount = 0; retryCount < maxRetries; retryCount++) {
            try {
                Map<String, Object> statusResult = checkDialogueGroupStatus(dialogueGroupId);
                String status = (String) statusResult.get("status");
                Boolean canClip = (Boolean) statusResult.get("canClip");
                if ("COMPLETED".equals(status) && Boolean.TRUE.equals(canClip)) {
                    createDialogueVideoClipByGroupId(dialogueGroupId, title, description, enableSubtitles);

                    return;
                } else if ("FAILED".equals(status)) {
                    log.warn("对话组 {} 有任务失败，停止监控", dialogueGroupId);
                    return;
                }
                Thread.sleep(checkInterval);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                return;
            } catch (Exception e) {
                log.error("监控对话组 {} 异常: {}", dialogueGroupId, e.getMessage());
                try {
                    Thread.sleep(checkInterval);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }

    /**
     * 直接处理云剪辑任务结果和扣费
     */
    private void processClipTaskDirectly(Long clipTaskId, Map<String, Object> clipResult) {
        try {
            PlatformVideo clipTask = platformVideoMapper.selectPlatformVideoById(clipTaskId);
            if (clipTask == null) {
                log.error("云剪辑任务不存在: {}", clipTaskId);
                return;
            }

            if (StringUtils.isNotEmpty(clipTask.getResultVideo())) {
                processClipTaskFeeDeduction(clipTask, clipTask.getResultVideo());
            }
        } catch (Exception e) {
            log.error("处理云剪辑任务失败: {}", clipTaskId, e);
        }
    }

    /**
     * 从临时URL中提取真实的文件存储路径
     */
    private String extractRealFilePathFromTempUrl(String temporaryUrl) {
        try {
            if (StringUtils.isEmpty(temporaryUrl)) {
                throw new IllegalArgumentException("临时URL不能为空");
            }
            java.net.URI uri = new java.net.URI(temporaryUrl);
            String path = uri.getPath();
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            return path;
        } catch (Exception e) {
            throw new RuntimeException("从临时URL提取文件路径失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理云剪辑任务的费用扣除
     */
    private void processClipTaskFeeDeduction(PlatformVideo clipTask, String resultVideoUrl) {
        try {
            JSONObject operation = clipTask.getOperationJson();
            if (operation != null && operation.getBooleanValue("fee_deducted", false)) {
                return; // 已扣费
            }
            String userName = clipTask.getCreateBy();
            if (StringUtils.isEmpty(userName)) {
                log.warn("云剪辑任务创建者为空，无法扣费: {}", clipTask.getId());
                return;
            }
            Long hashrateId = platformHashrateService.getHashrateIdByUserId(userName);
            if (hashrateId == null) {
                log.warn("未找到用户算力账户: {}", userName);
                return;
            }
            // 计算费用
            double videoDurationMinutes = calculateVideoDuration(resultVideoUrl);
            int unitCost = (operation != null && operation.containsKey("hashrate_cost_unit"))
                ? operation.getIntValue("hashrate_cost_unit") : 500;
            int totalPoints = Math.max((int) Math.ceil(videoDurationMinutes * unitCost), unitCost);
            platformHashrateService.deductHashratePoints(hashrateId, (long) totalPoints, "云剪辑合成");
            if (operation == null) operation = new JSONObject();
            operation.put("fee_deducted", true);
            operation.put("fee_points", totalPoints);
            operation.put("fee_duration_minutes", videoDurationMinutes);
            operation.put("fee_time", System.currentTimeMillis());
            operation.put("version", "CLIP");
            clipTask.setOperationJson(operation);
            platformVideoMapper.updatePlatformVideo(clipTask);
        } catch (Exception e) {
            log.error("云剪辑任务扣费失败: {}", clipTask.getId(), e);
        }
    }

    /**
     * 计算视频时长（分钟）- 用于云剪辑扣费
     */
    private double calculateVideoDuration(String filePath) {
        try {
            double durationSeconds = getVideoDuration(filePath);
            // 如果无法获取时长，尝试从任务记录获取预估时长
            if (durationSeconds <= 10.0) {
                double estimatedDuration = getEstimatedDurationFromTask(filePath);
                if (estimatedDuration > 0) {
                    durationSeconds = estimatedDuration;
                }
            }
            double durationMinutes = Math.max(durationSeconds / 60.0, 1.0);
            return durationMinutes;
        } catch (Exception e) {
            log.warn("计算视频时长失败，使用默认1分钟: {}", e.getMessage());
            return 1.0;
        }
    }

    /**
     * 从任务记录中获取预估时长
     */
    private double getEstimatedDurationFromTask(String filePath) {
        try {
            PlatformVideo query = new PlatformVideo();
            query.setResultVideo(filePath);
            List<PlatformVideo> tasks = platformVideoMapper.selectPlatformVideoList(query);
            if (tasks != null && !tasks.isEmpty()) {
                PlatformVideo clipTask = tasks.get(0);
                if (StringUtils.isNotEmpty(clipTask.getOperation())) {
                    JSONObject operation = JSONObject.parseObject(clipTask.getOperation());
                    if (operation.containsKey("estimated_duration")) {
                        return operation.getDoubleValue("estimated_duration");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取预估时长失败: {}", e.getMessage());
        }
        return 0.0;
    }

    @Override
    public Map<String, Object> getAutoSynthesisStatus(String dialogueGroupId) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("dialogueGroupId", dialogueGroupId);
        try {
            // 1. 获取对话组基本状态
            Map<String, Object> dialogueStatus = checkDialogueGroupStatus(dialogueGroupId);
            result.putAll(dialogueStatus);
            // 2. 查找云剪辑任务
            List<PlatformVideo> clipTasks = findClipTasksByDialogueGroupId(dialogueGroupId);
            if (!clipTasks.isEmpty()) {
                PlatformVideo clipTask = clipTasks.get(0); // 取第一个云剪辑任务
                Map<String, Object> clipInfo = new HashMap<>();
                clipInfo.put("clipTaskId", clipTask.getId());
                clipInfo.put("clipStatus", clipTask.getStatus());
                clipInfo.put("clipStatusText", getStatusDescription(clipTask.getStatus()));
                clipInfo.put("clipResultVideo", clipTask.getResultVideo());
                clipInfo.put("clipCreateTime", clipTask.getCreateTime());
                clipInfo.put("clipUpdateTime", clipTask.getUpdateTime());
                // 如果云剪辑任务完成，添加结果信息
                if (STATUS_SUCCESS.equals(clipTask.getStatus()) && StringUtils.isNotEmpty(clipTask.getResultVideo())) {
                    clipInfo.put("finalVideoUrl", clipTask.getResultVideo());
                    result.put("overallStatus", "FULLY_COMPLETED");
                    result.put("message", "数字人对话合成和云剪辑已全部完成");
                } else if (STATUS_FAILED.equals(clipTask.getStatus())) {
                    result.put("overallStatus", "CLIP_FAILED");
                    result.put("message", "云剪辑合成失败");
                } else {
                    result.put("overallStatus", "CLIP_PROCESSING");
                    result.put("message", "云剪辑合成进行中");
                }
                result.put("clipInfo", clipInfo);
            } else {
                // 没有云剪辑任务
                String dialogueStatus2 = (String) dialogueStatus.get("status");
                if ("COMPLETED".equals(dialogueStatus2)) {
                    result.put("overallStatus", "DIALOGUE_COMPLETED");
                    result.put("message", "数字人对话合成正在处理中...");
                } else {
                    result.put("overallStatus", dialogueStatus2);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("查询自动合成状态失败: {}", dialogueGroupId, e);
            result.put("overallStatus", "ERROR");
            result.put("message", "查询状态失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据对话组ID查找云剪辑任务
     */
    private List<PlatformVideo> findClipTasksByDialogueGroupId(String dialogueGroupId) {
        // 查询所有任务
        List<PlatformVideo> allTasks = platformVideoMapper.selectPlatformVideoList(new PlatformVideo());
        return allTasks.stream()
            .filter(task -> {
                try {
                    if (StringUtils.isNotEmpty(task.getOperation())) {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        String taskType = operation.getString("task_type");
                        String taskGroupId = operation.getString("dialogueGroupId");
                        return "dialogue_clip".equals(taskType) && dialogueGroupId.equals(taskGroupId);
                    }
                } catch (Exception e) {
                    log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
                }
                return false;
            }).sorted((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime())).collect(Collectors.toList());
    }

    /**
     * 获取状态文本描述
     */
    private String getStatusDescription(String status) {
        switch (status) {
            case STATUS_PENDING: return STATUS_DESC_PENDING;
            case STATUS_PROCESSING: return STATUS_DESC_PROCESSING;
            case STATUS_SUCCESS: return STATUS_DESC_SUCCESS;
            case STATUS_FAILED: return STATUS_DESC_FAILED;
            default: return "未知状态";
        }
    }

    /**
     * 验证对话合成请求参数
     */
    private void validateDialogueRequest(DialogueSynthesisRequest request) {
        if (request.getDigitalHumans() == null || request.getDigitalHumans().isEmpty()) {
            throw new ServiceException("数字人配置不能为空");
        }
        if (request.getDialogueContent() == null || request.getDialogueContent().isEmpty()) {
            throw new ServiceException("对话内容不能为空");
        }
        String version = request.getVersion();
        if (!"M".equalsIgnoreCase(version) && !"H".equalsIgnoreCase(version) && !"V".equalsIgnoreCase(version)) {
            throw new ServiceException("不支持的版本类型: " + version);
        }
        // 数字人配置验证
        String[] supportedVoices = {"zhiyuan", "zhiyue", "zhistella", "zhida", "aiqi", "aicheng", "aijia","siqi", "sijia", "mashu", "yuer", "ruoxi", "aida", "sicheng","ninger", "xiaoyun", "xiaogang", "ruilin"};
        for (DialogueSynthesisRequest.DigitalHuman human : request.getDigitalHumans()) {
            if (StringUtils.isEmpty(human.getAvatarAddress()) || StringUtils.isEmpty(human.getVoiceName())) {
                throw new ServiceException("数字人配置不完整");
            }
            if ("system".equals(human.getVoiceType()) && human.getVoiceId() == null) {
                throw new ServiceException("系统声音ID不能为空");
            }
            if ("builtin".equals(human.getVoiceType())) {
                boolean isSupported = false;
                for (String voice : supportedVoices) {
                    if (voice.equals(human.getVoiceName())) {
                        isSupported = true;
                        break;
                    }
                }
                if (!isSupported) {
                    throw new ServiceException("不支持的内置音色: " + human.getVoiceName());
                }
            }
        }
        if ("M".equalsIgnoreCase(version) && request.getBboxShiftValue() != null) {
            int bboxValue = request.getBboxShiftValue();
            if (bboxValue < -7 || bboxValue > 7) {
                throw new ServiceException("M版边界框偏移值必须在-7到+7之间");
            }
        }
        if ("V".equalsIgnoreCase(version)) {
            if (StringUtils.isEmpty(request.getModel())) {
                throw new ServiceException("V版必须指定模型编码");
            }
            List<PlatformModel> models = getAvailableModels();
            boolean modelExists = models.stream().anyMatch(model -> request.getModel().equals(model.getModelCode()));
            if (!modelExists) {
                throw new ServiceException("模型不存在或不可用: " + request.getModel());
            }
        }
    }

    /**
     * 为单个文本生成音频
     */
    private Map<String, String> generateAudioForText(String text, DialogueSynthesisRequest.DigitalHuman human) {
        try {
            checkAndChargeForAudio(text, human.getVoiceType());
            return switch (human.getVoiceType()) {
                case "system" -> generateSystemAudio(text, human.getVoiceId());
                case "builtin" -> generateBuiltinAudio(text, human.getVoiceName());
                default -> throw new ServiceException("不支持的声音类型: " + human.getVoiceType());
            };
        } catch (Exception e) {
            log.error("音频生成失败 - 文本: {}, 声音类型: {}",
                text.substring(0, Math.min(20, text.length())), human.getVoiceType(), e);
            throw new ServiceException("音频生成失败: " + e.getMessage());
        }
    }

    /**
     * 检查并为音频生成收费
     */
    private void checkAndChargeForAudio(String text, String voiceType) {
        try {
            String userName = SecurityUtils.getUsername();
            Long hashrateId = platformHashrateService.getHashrateIdByUserId(userName);
            if (hashrateId == null) {
                throw new ServiceException("未找到当前算力用户信息");
            }
            PlatformHashrate hashrate = platformHashrateService.selectPlatformHashrateByHashrateId(hashrateId);
            if (hashrate == null) {
                throw new ServiceException("算力用户不存在，请联系管理员");
            }
            int audioPoints = 10;
            long currentBalance = Long.parseLong(hashrate.getHashrateBalance());
            if (currentBalance < audioPoints) {
                throw new ServiceException("算力点不足，请联系管理员充值");
            }
            String description = "system".equals(voiceType) ? "文本转音频" : "阿里云内置音色";
            platformHashrateService.deductHashratePoints(hashrateId, (long) audioPoints, description);
        } catch (Exception e) {
            throw new ServiceException("音频生成收费失败: " + e.getMessage());
        }
    }

    /**
     * 创建视频合成任务
     */
    private Long createVideoTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
    DialogueSynthesisRequest.DialogueContent dialogue, String version, Integer bboxShiftValue, String modelCode, String dialogueGroupId) {
        if ("V".equalsIgnoreCase(version)) {
            return createVVersionTask(audioUrl, audioMd5, human, dialogue, modelCode, dialogueGroupId);
        }
        PlatformVideo videoTask = buildVideoTask(audioUrl, human, dialogue, audioMd5, version, bboxShiftValue, dialogueGroupId);
        return "M".equalsIgnoreCase(version) ? add(videoTask) : synthesisH(videoTask);
    }

    /**
     * 构建视频任务对象
     */
    private PlatformVideo buildVideoTask(String audioUrl, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version, Integer bboxShiftValue, String dialogueGroupId) {
        PlatformVideo videoTask = new PlatformVideo();
        videoTask.setDrivenAudio(audioUrl);
        videoTask.setDrivenVideo(human.getAvatarAddress());
        videoTask.setStatus("1");
        JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, version);
        if ("M".equalsIgnoreCase(version) && bboxShiftValue != null) {
            operation.put("bbox_shift_value", bboxShiftValue);
        }
        // 添加对话组ID和顺序信息
        if (StringUtils.isNotEmpty(dialogueGroupId)) {
            operation.put("dialogueGroupId", dialogueGroupId);
            operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
        }
        videoTask.setOperationJson(operation);
        return videoTask;
    }

    /**
     * 创建V版视频合成任务
     */
    private Long createVVersionTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String modelCode, String dialogueGroupId) {
        try {
            String videoName = FileUrlUtils.generateVideoName();
            Map<String, Object> params = new HashMap<>();
            params.put(PARAM_MODEL, modelCode);
            Map<String, Object> content = new HashMap<>();
            content.put("type", 1);
            content.put("video_name", videoName);
            try {
                String audioTempUrl = FileOperateUtils.getURL(audioUrl);
                String videoTempUrl = FileOperateUtils.getURL(human.getAvatarAddress());
                content.put("live_sound_url", audioTempUrl != null ? audioTempUrl : audioUrl);
                content.put("live_video_url", videoTempUrl != null ? videoTempUrl : human.getAvatarAddress());
            } catch (Exception e) {
                log.warn("获取临时URL失败，使用原始URL");
                content.put("live_sound_url", audioUrl);
                content.put("live_video_url", human.getAvatarAddress());
            }
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);
            params.put("messages", messages);
            Map<String, Object> apiResult = createVideoSynthesis(params);// 调用V版创建任务接口
            if (apiResult.containsKey("code")) {
                Object code = apiResult.get("code");
                if (!"200".equals(String.valueOf(code)) && !Integer.valueOf(200).equals(code)) {
                    log.error("V版API调用失败 - code: {}, msg: {}", code, apiResult.get("msg"));
                }
            }
            return saveVVersionTask(modelCode, apiResult, audioUrl, audioMd5, human, dialogue, videoName, dialogueGroupId);
        } catch (Exception e) {
            throw new ServiceException("创建V版视频任务失败: " + e.getMessage());
        }
    }

    /**
     * 保存V版任务到数据库
     */
    private Long saveVVersionTask(String modelCode, Map<String, Object> apiResult, String audioUrl, String audioMd5,
        DialogueSynthesisRequest.DigitalHuman human, DialogueSynthesisRequest.DialogueContent dialogue, String videoName, String dialogueGroupId) {
        try {
            String taskId = "";
            if (apiResult.containsKey("task_id")) {
                taskId = apiResult.get("task_id").toString();
            } else if (apiResult.containsKey("data") && apiResult.get("data") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
                if (data.containsKey("task_id")) {
                    taskId = data.get("task_id").toString();
                }
            }
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(taskId);
            task.setNumber(videoName);
            task.setVersion(PlatformVideoTaskStatusAndVersion.V_VERSION);
            task.setStatus(apiResult.getOrDefault("status", STATUS_PENDING).toString());
            task.setDrivenAudio(audioUrl);
            task.setDrivenVideo(human.getAvatarAddress());
            task.setCreateBy(SecurityUtils.getUsername());
            task.setUpdateBy(SecurityUtils.getUsername());
            task.setCreatedAt(DateUtils.getNowDate());
            task.setUpdatedAt(DateUtils.getNowDate());
            JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, "V");
            operation.put("video_priority", "0");
            operation.put("code", apiResult.getOrDefault("code", "200"));
            operation.put("video_message", apiResult.getOrDefault("msg", "任务创建成功"));
            // 添加对话组ID和顺序信息
            if (StringUtils.isNotEmpty(dialogueGroupId)) {
                operation.put("dialogueGroupId", dialogueGroupId);
                operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                operation.put("model_price", modelInfo.getModelVersion());
            }
            task.setOperation(operation.toJSONString());
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存V版任务记录失败");
            }
            return task.getId();
        } catch (Exception e) {
            throw new ServiceException("保存V版任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 使用数智宝系统声音生成音频
     */
    private Map<String, String> generateSystemAudio(String text, Long voiceId) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            if (platformTaskService == null) {
                throw new ServiceException("无法获取PlatformTaskService服务");
            }
            Method syncMethod = platformTaskService.getClass().getMethod("createTextToAudioSync", String.class, Long.class, Long.class);
            String audioPath = (String) syncMethod.invoke(platformTaskService, text, voiceId, null);

            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("系统音频合成失败");
            }
            String audioMd5 = getAudioMd5(audioPath);
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("系统音频合成失败: " + e.getMessage());
        }
    }

    /**
     * 使用内置音色生成音频（阿里云）
     */
    private Map<String, String> generateBuiltinAudio(String text, String voiceName) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            Object ttsRequest = createTtsRequest(text, voiceName);
            Method method = platformTaskService.getClass().getMethod("synthesize", ttsRequest.getClass());
            String audioPath = (String) method.invoke(platformTaskService, ttsRequest);
            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("内置音色合成失败");
            }
            String audioMd5 = getAudioMd5(audioPath);
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("内置音色合成失败: " + e.getMessage());
        }
    }

    /**
     * 获取音频MD5值
     */
    private String getAudioMd5(String audioPath) {
        String audioMd5 = FileOperateUtils.getMd5ForFilePath(audioPath);
        if (StringUtils.isEmpty(audioMd5)) {
            PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
            if (audio != null) {
                audioMd5 = audio.getAudioMd5();
            }
        }
        return audioMd5;
    }

    /**
     * 创建TtsRequest对象
     */
    private Object createTtsRequest(String text, String voiceName) {
        try {
            Class<?> ttsRequestClass = Class.forName("com.ruoyi.platform.model.domain.TtsRequest");
            Object ttsRequest = ttsRequestClass.getDeclaredConstructor().newInstance();
            setTtsProperty(ttsRequest, "setText", text);
            setTtsProperty(ttsRequest, "setVoice", voiceName != null ? voiceName : "zhiyuan");
            setTtsProperty(ttsRequest, "setFormat", "wav");
            setTtsProperty(ttsRequest, "setSampleRate", 16000);
            setTtsProperty(ttsRequest, "setVolume", 50);
            return ttsRequest;
        } catch (Exception e) {
            throw new ServiceException("创建TtsRequest对象失败: " + e.getMessage());
        }
    }

    /**
     * 设置TTS对象属性
     */
    private void setTtsProperty(Object ttsRequest, String methodName, Object value) throws Exception {
        Class<?> paramType = value instanceof Integer ? int.class : String.class;
        java.lang.reflect.Method method = ttsRequest.getClass().getMethod(methodName, paramType);
        method.invoke(ttsRequest, value);
    }

    /**
     * 构建基础操作信息JSON
     */
    private JSONObject buildBaseOperationJson(DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version) {
        JSONObject operation = new JSONObject();
        operation.put("dialogue_id", dialogue.getId());
        operation.put("speaker_id", dialogue.getSpeaker());
        operation.put("speaker_name", dialogue.getSpeakerName());
        operation.put("text", dialogue.getText());
        operation.put("avatar_name", human.getAvatarName());
        operation.put("voice_name", human.getVoiceName());
        operation.put("voice_type", human.getVoiceType());
        operation.put("task_type", "dialogue_synthesis_item");
        operation.put("driven_audio_md5", audioMd5);
        operation.put("driven_video_md5", getAvatarMd5(human.getAvatarAddress()));
        operation.put("model_price", "V".equalsIgnoreCase(version) ? "600" : "500");
        return operation;
    }

    /**
     * 获取形象MD5值 - 直接查询数据库
     */
    private String getAvatarMd5(String avatarAddress) {
        if (StringUtils.isEmpty(avatarAddress)) {
            return null;
        }
        try {
            Map<String, Object> condition = Map.of("imageAddress", avatarAddress);
            List<PlatformImage> images = platformImageMapper.selectImagesByCondition(condition);
            return images != null && !images.isEmpty() ? images.get(0).getMd5() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建视频片段列表
     */
    private List<Map<String, Object>> buildVideoClips(List<PlatformVideo> videoTasks) {
        List<Map<String, Object>> clips = new ArrayList<>();
        for (int i = 0; i < videoTasks.size(); i++) {
            PlatformVideo task = videoTasks.get(i);

            if (StringUtils.isEmpty(task.getResultVideo())) {
                throw new ServiceException("任务ID " + task.getId() + " 的结果视频为空");
            }
            // 注册为ICE媒资
            String mediaId = registerOssFileAsIceMedia(task.getResultVideo());
            if (StringUtils.isEmpty(mediaId)) {
                throw new ServiceException("ICE媒资注册失败，任务ID: " + task.getId());
            }
            waitForMediaReady(mediaId);
            // 构建视频片段信息
            Map<String, Object> clip = new HashMap<>();
            clip.put("taskId", task.getId());
            clip.put("videoUrl", mediaId);
            clip.put("order", i + 1);
            // 获取视频时长
            Double duration = getVideoDuration(task.getResultVideo());
            // 从operation字段获取额外信息
            if (duration <= 0 && StringUtils.isNotEmpty(task.getOperation())) {
                try {
                    JSONObject operation = JSONObject.parseObject(task.getOperation());
                    duration = operation.getDouble("video_duration");
                    if (duration == null) duration = operation.getDouble("duration");
                    if (duration == null) duration = operation.getDouble("videoDuration");
                    if (operation.containsKey("speakerName")) {
                        clip.put("speakerName", operation.getString("speakerName"));
                    }
                    if (operation.containsKey("text")) {
                        clip.put("text", operation.getString("text"));
                    }
                    if (operation.containsKey("dialogueOrder")) {
                        clip.put("order", operation.getInteger("dialogueOrder"));
                    }
                } catch (Exception e) {
                    log.warn("解析任务信息失败: {}", task.getId());
                }
            }
            // 保底时长10秒
            if (duration == null || duration <= 0) {
                duration = 10.0;
            }
            clip.put("duration", duration);
            clips.add(clip);
        }
        return clips;
    }

    /**
     * 转换为可访问的OSS URL（带签名的临时访问地址）
     */
    private String convertToStandardOssUrl(String videoPath) {
        try {
            // 如果是相对路径，使用FileOperateUtils生成临时访问URL
            if (videoPath.startsWith("video/")) {
                String tempUrl = FileOperateUtils.getURL(videoPath);
                // 确保使用HTTPS协议（阿里云ICE要求HTTPS）
                if (tempUrl.startsWith("http://")) {
                    tempUrl = tempUrl.replace("http://", "https://");
                }
                return tempUrl;
            }
            // 如果已经是完整的URL，检查是否需要重新生成签名
            if (videoPath.startsWith("https://") || videoPath.startsWith("http://")) {
                // 如果URL包含签名参数，确保使用HTTPS
                if (videoPath.contains("Expires=") && videoPath.contains("Signature=")) {
                    if (videoPath.startsWith("http://")) {
                        videoPath = videoPath.replace("http://", "https://");
                    }
                    return videoPath;
                }
                // 如果是无签名的OSS URL，尝试提取路径并重新生成签名
                if (videoPath.contains("szb-pc.oss-cn-beijing.aliyuncs.com/")) {
                    String ossPath = videoPath.substring(videoPath.indexOf("szb-pc.oss-cn-beijing.aliyuncs.com/") + 36);
                    String tempUrl = FileOperateUtils.getURL(ossPath);
                    // 确保使用HTTPS协议
                    if (tempUrl.startsWith("http://")) {
                        tempUrl = tempUrl.replace("http://", "https://");
                    }
                    return tempUrl;
                }
                // 其他情况确保使用HTTPS
                if (videoPath.startsWith("http://")) {
                    videoPath = videoPath.replace("http://", "https://");
                }
                return videoPath;
            }
            throw new ServiceException("不支持的视频路径格式: " + videoPath);
        } catch (Exception e) {
            throw new ServiceException("转换OSS URL失败: " + e.getMessage());
        }
    }

    /**
     * 将OSS文件注册为阿里云ICE媒资
     * 这样可以避免"媒资未就绪"的问题
     */
    private String registerOssFileAsIceMedia(String videoPath) {
        try {
            // 转换为标准OSS URL（不带签名）
            String ossUrl = convertToStandardOssUrlWithoutSignature(videoPath);
            // 创建注册媒资请求
            RegisterMediaInfoRequest request = new com.aliyun.ice20201109.models.RegisterMediaInfoRequest();
            request.setInputURL(ossUrl);
            request.setMediaType("video");
            request.setBusinessType("media");
            // 调用阿里云ICE API注册媒资
            RegisterMediaInfoResponse response = iceClientAK.registerMediaInfo(request);
            if (response != null && response.getBody() != null) {
                String mediaId = response.getBody().getMediaId();
                // 等待媒资分析完成
                if (waitForMediaReady(mediaId)) {
                    return mediaId;
                } else {
                    return null; // 返回null，回退到使用URL
                }
            }
        } catch (Exception e) {
            // 检查是否是重复注册错误
            if (e.getMessage() != null && e.getMessage().contains("has already been registered with mediaId")) {
                String existingMediaId = extractMediaIdFromErrorMessage(e.getMessage());
                if (StringUtils.isNotEmpty(existingMediaId)) {
                    return existingMediaId;
                }
            }
        }
        return null; // 返回null，回退到使用URL
    }

    /**
     * 转换为标准OSS URL（不带签名，用于ICE媒资注册）
     */
    private String convertToStandardOssUrlWithoutSignature(String videoPath) {
        // 如果是相对路径，转换为完整的OSS HTTPS URL
        if (videoPath.startsWith("video/")) {
            return "https://szb-pc.oss-cn-beijing.aliyuncs.com/" + videoPath;
        }
        // 如果已经是完整的HTTPS URL，移除签名参数
        if (videoPath.startsWith("https://") || videoPath.startsWith("http://")) {
            if (videoPath.contains("?")) {
                videoPath = videoPath.substring(0, videoPath.indexOf("?"));
            }
            // 确保使用HTTPS协议
            if (videoPath.startsWith("http://")) {
                videoPath = videoPath.replace("http://", "https://");
            }
            return videoPath;
        }
        throw new ServiceException("不支持的视频路径格式: " + videoPath);
    }

    /**
     * 从错误消息中提取已存在的MediaId
     */
    private String extractMediaIdFromErrorMessage(String errorMessage) {
        try {
            // 错误消息格式: "has already been registered with mediaId \"ef0a7cd06d1871f0af90e7f7d45b6302\""
            String pattern = "mediaId \"([a-f0-9]{32})\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(errorMessage);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("提取MediaId失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 等待媒资分析完成
     */
    private boolean waitForMediaReady(String mediaId) {
        int maxRetries = 6;
        int retryInterval = 5000;
        for (int i = 0; i < maxRetries; i++) {
            try {
                Thread.sleep(retryInterval);
                if (checkMediaStatus(mediaId)) {
                    return true;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            } catch (Exception e) {
                // 继续重试
            }
        }
        return false;
    }

    /**
     * 检查媒资状态（通过创建测试工程验证）
     */
    private boolean checkMediaStatus(String mediaId) {
        try {
            // 通过创建一个简单的测试工程来验证媒资是否可用
            com.aliyun.ice20201109.models.CreateEditingProjectRequest testRequest =
                new com.aliyun.ice20201109.models.CreateEditingProjectRequest();
            testRequest.setTitle("MediaTest_" + System.currentTimeMillis());
            testRequest.setTemplateType("Timeline");
            // 创建简单的Timeline测试媒资（只取1秒钟）
            String testTimeline = String.format(
                "{\"VideoTracks\":[{\"VideoTrackClips\":[{\"MediaId\":\"%s\",\"In\":0,\"Out\":1.0,\"TimelineIn\":0.0,\"TimelineOut\":1.0}]}]}",
                mediaId);
            testRequest.setTimeline(testTimeline);
            com.aliyun.ice20201109.models.CreateEditingProjectResponse testResponse =
                iceClientAK.createEditingProject(testRequest);
            boolean isReady = testResponse != null && testResponse.getBody() != null;
            // 媒资可用性测试完成
            return isReady;
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("not ready")) {
                return false; // 媒资还在分析中
            }
            return false;
        }
    }

    /**
     * 构建云剪辑Timeline（支持字幕）
     */
    private String buildVideoClipTimelineWithSubtitles(List<Map<String, Object>> videoClips, boolean enableSubtitles, List<PlatformVideo> groupTasks) {
        JSONObject timeline = new JSONObject();
        JSONObject videoTrack = new JSONObject();
        List<JSONObject> videoTrackClips = new ArrayList<>();
        double currentTime = 0.0;

        for (Map<String, Object> clip : videoClips) {
            JSONObject videoClip = new JSONObject();
            String videoUrl = clip.get("videoUrl").toString();
            Double duration = (Double) clip.get("duration");
            // 判断是MediaId还是URL
            if (videoUrl.length() == 32 && !videoUrl.startsWith("http")) {
                videoClip.put("MediaId", videoUrl);
            } else {
                videoClip.put("MediaURL", videoUrl);
            }
            videoClip.put("In", 0);
            videoClip.put("Out", duration);
            videoClip.put("TimelineIn", currentTime);
            videoClip.put("TimelineOut", currentTime + duration);
            // 根据阿里云文档：必须同时设置X、Y、Width、Height，AdaptMode才会生效
            videoClip.put("X", 0);         // X坐标：0表示左边缘
            videoClip.put("Y", 0);         // Y坐标：0表示上边缘
            videoClip.put("Width", 1080);  // 目标区域宽度：1080像素（竖屏宽度）
            videoClip.put("Height", 1920); // 目标区域高度：1920像素（竖屏高度）
            videoClip.put("AdaptMode", "Cover");    // 如需保持比例但裁剪
            videoTrackClips.add(videoClip);
            currentTime += duration;
        }
        videoTrack.put("VideoTrackClips", videoTrackClips);
        timeline.put("VideoTracks", List.of(videoTrack));
        // Timeline的画布尺寸必须与输出配置保持一致
        timeline.put("Width", 1080);
        timeline.put("Height", 1920);
        // 如果启用字幕，添加字幕轨道
        if (enableSubtitles && groupTasks != null && !groupTasks.isEmpty()) {
            List<JSONObject> subtitleTracks = buildSubtitleTracks(groupTasks, videoClips);
            if (!subtitleTracks.isEmpty()) {
                timeline.put("SubtitleTracks", subtitleTracks);
            }
        }
        return timeline.toJSONString();
    }

    /**
     * 构建字幕轨道
     */
    private List<JSONObject> buildSubtitleTracks(List<PlatformVideo> groupTasks, List<Map<String, Object>> videoClips) {
        List<JSONObject> subtitleTracks = new ArrayList<>();
        // 创建一个字幕轨道
        JSONObject subtitleTrack = new JSONObject();
        List<JSONObject> subtitleClips = new ArrayList<>();
        double currentTime = 0.0;
        // 遍历视频片段，为每个片段创建对应的字幕
        for (int i = 0; i < videoClips.size() && i < groupTasks.size(); i++) {
            Map<String, Object> videoClip = videoClips.get(i);
            PlatformVideo task = groupTasks.get(i);
            Double duration = (Double) videoClip.get("duration");
            // 从任务的operation中提取对话信息
            String subtitleText = extractSubtitleText(task);
            String speakerName = extractSpeakerName(task);
            if (StringUtils.isNotEmpty(subtitleText)) {
                // 如果有发言人名称，添加到字幕前面
                String finalText = subtitleText;
                if (StringUtils.isNotEmpty(speakerName)) {
                    finalText = speakerName + ": " + subtitleText;
                }
                // 处理长文本自动换行
                List<String> lines = splitTextForSubtitle(finalText);
                for (int lineIndex = 0; lineIndex < lines.size(); lineIndex++) {
                    JSONObject subtitleClip = new JSONObject();
                    // 根据阿里云ICE文档，使用正确的字幕格式
                    subtitleClip.put("Type", "Text");
                    subtitleClip.put("Content", lines.get(lineIndex));
                    // 字幕位置：使用绝对像素值，确保在屏幕底部可见区域
                    subtitleClip.put("X", 0);  // X坐标：0表示左边缘
                    // Y坐标：修复字幕顺序，最早的话在最下面，最晚的话在最上面
                    // 1920是屏幕高度，字幕从底部开始向上排列
                    // 反转lineIndex的计算方式：第一行（lineIndex=0）在最底部，后续行向上排列
                    int totalLines = lines.size();
                    int reversedLineIndex = totalLines - 1 - lineIndex;  // 反转行索引
                    int subtitleY = 1920 - 200 - (reversedLineIndex * 60);  // 距离底部200像素，每行间隔60像素
                    if (subtitleY < 100) subtitleY = 100 + (reversedLineIndex * 60);  // 确保不超出顶部
                    subtitleClip.put("Y", subtitleY);
                    subtitleClip.put("Alignment", "TopCenter");  // 顶部居中对齐（相对于Y坐标）
                    // 字体和样式设置
                    subtitleClip.put("FontSize", 50);  // 字体大小
                    subtitleClip.put("FontColor", "#FFFFFF");  // 白色字体
                    subtitleClip.put("Font", "SimHei");  // 使用黑体字体（更稳定）
                    // 描边设置（提高可读性）
                    subtitleClip.put("Outline", 4);  // 描边宽度
                    subtitleClip.put("OutlineColour", "#000000");  // 黑色描边
                    // 字体样式
                    JSONObject fontFace = new JSONObject();
                    fontFace.put("Bold", true);  // 加粗
                    fontFace.put("Italic", false);
                    fontFace.put("Underline", false);
                    subtitleClip.put("FontFace", fontFace);
                    // 自动换行设置
                    subtitleClip.put("AdaptMode", "AutoWrap");  // 自动换行
                    subtitleClip.put("TextWidth", 2000);  // 文本宽度：1000像素，适合1080宽度，减少换行
                    // 设置时间轴
                    subtitleClip.put("TimelineIn", currentTime);
                    subtitleClip.put("TimelineOut", currentTime + duration);
                    subtitleClips.add(subtitleClip);
                }
            }
            currentTime += duration;
        }
        if (!subtitleClips.isEmpty()) {
            subtitleTrack.put("SubtitleTrackClips", subtitleClips);
            subtitleTracks.add(subtitleTrack);
        }
        return subtitleTracks;
    }

    /**
     * 将长文本分割为适合字幕显示的多行
     * 根据视频宽度和字体大小优化中文字幕显示
     */
    private List<String> splitTextForSubtitle(String text) {
        List<String> lines = new ArrayList<>();
        if (StringUtils.isEmpty(text)) {
            return lines;
        }
        int maxCharsPerLine = 20; // 竖屏宽度较窄，适当减少每行字符数以确保显示效果
        // 如果文本长度小于等于最大字符数，直接返回
        if (text.length() <= maxCharsPerLine) {
            lines.add(text);
            return lines;
        }
        // 优先在标点符号处分割
        String[] sentences = text.split("(?<=[。！？；])|(?<=[，、：]\\s*)");
        StringBuilder currentLine = new StringBuilder();
        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) continue;
            // 如果单个句子就超过最大长度，需要进一步分割
            if (sentence.length() > maxCharsPerLine) {
                // 先保存当前行（如果有内容）
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString().trim());
                    currentLine = new StringBuilder();
                }
                // 分割长句子
                for (int i = 0; i < sentence.length(); i += maxCharsPerLine) {
                    int end = Math.min(i + maxCharsPerLine, sentence.length());
                    String part = sentence.substring(i, end);
                    lines.add(part);
                }
            } else {
                // 检查加上这个句子是否会超过最大长度
                if (currentLine.length() + sentence.length() > maxCharsPerLine && currentLine.length() > 0) {
                    lines.add(currentLine.toString().trim());
                    currentLine = new StringBuilder();
                }
                currentLine.append(sentence);
            }
        }
        // 添加最后一行
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString().trim());
        }
        // 确保至少有一行内容
        if (lines.isEmpty()) {
            lines.add(text);
        }
        return lines;
    }

    /**
     * 从任务中提取字幕文本
     */
    private String extractSubtitleText(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                return operation.getString("text");
            }
        } catch (Exception e) {
            log.warn("提取字幕文本失败，任务ID: {}", task.getId());
        }
        return null;
    }

    /**
     * 从任务中提取发言人名称
     */
    private String extractSpeakerName(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                return operation.getString("speaker_name");
            }
        } catch (Exception e) {
            log.warn("提取发言人名称失败，任务ID: {}", task.getId());
        }
        return null;
    }

    /**
     * 提交云剪辑合成任务
     */
    private Map<String, Object> submitMediaProducingJobWithTimeline(String timeline, String title, String description) {
        try {
            // 创建请求对象
            SubmitMediaProducingJobRequest request = new com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest();
            // 设置Timeline参数
            request.setTimeline(timeline);
            // 设置输出配置
            String outputMediaURL = generateOutputMediaURL();
            JSONObject outputMediaConfig = createOutputMediaConfig(outputMediaURL);
            request.setOutputMediaConfig(outputMediaConfig.toString());
            // 设置项目元数据
            if (StringUtils.isNotEmpty(title) || StringUtils.isNotEmpty(description)) {
                JSONObject projectMetadata = new JSONObject();
                if (StringUtils.isNotEmpty(title)) {
                    projectMetadata.put("Title", title);
                }
                if (StringUtils.isNotEmpty(description)) {
                    projectMetadata.put("Description", description);
                }
                request.setProjectMetadata(projectMetadata.toString());
            }
            // 调用阿里云ICE API
            SubmitMediaProducingJobResponse response = iceClientAK.submitMediaProducingJob(request);
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                SubmitMediaProducingJobResponseBody body = response.getBody();
                result.put("jobId", body.getJobId());
                result.put("requestId", body.getRequestId());
                result.put("projectId", body.getProjectId());
                result.put("mediaId", body.getMediaId());
                result.put("message", "云剪辑合成任务提交成功");
                result.put("outputMediaURL", outputMediaURL);
                // 等待任务开始处理并查询状态
                Thread.sleep(2000);
                checkJobStatus(body.getJobId());
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("提交云剪辑合成任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成输出媒体URL
     *
     * @return 生成的OSS输出URL
     */
    private String generateOutputMediaURL() {
        return String.format("https://szb-pc.oss-cn-beijing.aliyuncs.com/video/dialogue/dialogue_video_%d.%s",
            System.currentTimeMillis(), "mp4");
    }

    /**
     * 创建输出媒体配置
     *
     * @param outputMediaURL 输出媒体URL
     * @return 输出媒体配置JSON对象
     */
    private JSONObject createOutputMediaConfig(String outputMediaURL) {
        JSONObject config = new JSONObject();
        config.put("MediaURL", outputMediaURL);
        config.put("Width", 1080);
        config.put("Height", 1920);
        config.put("Bitrate", "8000");
        config.put("FrameRate", "30");
        config.put("Format", "mp4");
        return config;
    }

    /**
     * 检查任务状态并输出详细信息
     */
    private Map<String, Object> checkJobStatus(String jobId) {
        try {
            // 创建查询请求
            GetMediaProducingJobRequest request = new com.aliyun.ice20201109.models.GetMediaProducingJobRequest();
            request.setJobId(jobId);
            // 调用阿里云ICE API查询任务状态
            GetMediaProducingJobResponse response = iceClientAK.getMediaProducingJob(request);
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                GetMediaProducingJobResponseBody body = response.getBody();
                if (body.getMediaProducingJob() != null) {
                    String status = body.getMediaProducingJob().getStatus();
                    String message = body.getMediaProducingJob().getMessage();
                    // 构建返回结果
                    Map<String, Object> jobInfo = new HashMap<>();
                    jobInfo.put("jobId", jobId);
                    jobInfo.put("status", status);
                    jobInfo.put("message", message);
                    jobInfo.put("createTime", body.getMediaProducingJob().getCreateTime());
                    jobInfo.put("modifiedTime", body.getMediaProducingJob().getModifiedTime());
                    jobInfo.put("code", body.getMediaProducingJob().getCode());
                    jobInfo.put("completeTime", body.getMediaProducingJob().getCompleteTime());
                    jobInfo.put("mediaId", body.getMediaProducingJob().getMediaId());
                    jobInfo.put("mediaURL", body.getMediaProducingJob().getMediaURL());
                    jobInfo.put("projectId", body.getMediaProducingJob().getProjectId());
                    jobInfo.put("timeline", body.getMediaProducingJob().getTimeline());
                    jobInfo.put("clipsParam", body.getMediaProducingJob().getClipsParam());
                    Map<String, Object> data = new HashMap<>();
                    data.put("mediaProducingJob", jobInfo);
                    data.put("requestId", body.getRequestId());
                    result.put("data", data);
                    // 添加处理后的媒体URL（如果任务成功）
                    if ("Success".equalsIgnoreCase(status) && body.getMediaProducingJob().getMediaURL() != null) {
                        try {
                            String tempUrl = convertToStandardOssUrl(body.getMediaProducingJob().getMediaURL());
                            result.put("mediaURL", tempUrl);
                        } catch (Exception e) {
                            result.put("mediaURL", body.getMediaProducingJob().getMediaURL());
                        }
                    }
                    // 记录任务状态
                    if ("Failed".equalsIgnoreCase(status)) {
                        log.error("任务失败 - JobId: {}, 错误: {}", jobId, message);
                    } else {

                    }
                    return result;
                } else {
                    result.put("error", "任务信息为空");
                    return result;
                }
            } else {
                result.put("error", "查询任务状态响应为空");
                return result;
            }
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "查询任务状态异常: " + e.getMessage());
            return errorResult;
        }
    }

    private Map<String, Object> createDialogueVideoClipByGroupId(String dialogueGroupId, String title, String description, boolean enableSubtitles) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }
        try {
            // 查找对话组的已完成任务（用于云剪辑）
            List<PlatformVideo> groupTasks = findCompletedTasksByDialogueGroupId(dialogueGroupId);
            if (groupTasks.isEmpty()) {
                throw new ServiceException("找到对话组ID为 " + dialogueGroupId + " 的已完成任务");
            }
            // 验证任务完成状态
            validateTasksCompleted(groupTasks);
            // 构建视频片段列表
            List<Map<String, Object>> videoClips = buildVideoClips(groupTasks);
            // 构建云剪辑Timeline（支持字幕）
            String timeline = buildVideoClipTimelineWithSubtitles(videoClips, enableSubtitles, groupTasks);
            // 提交云剪辑合成任务
            Map<String, Object> result = submitMediaProducingJobWithTimeline(timeline, title, description);
            // 标记任务已进行云剪辑合成
            markTasksAsClippedSimple(groupTasks, result);
            // 创建云剪辑任务记录到platform_video表（直接设置为完成状态）
            Long clipTaskId = createClipTaskRecord(result, dialogueGroupId, title, description, groupTasks);
            result.put("clipTaskId", clipTaskId);
            // 直接处理云剪辑结果和扣费，不需要监控（因为任务已设置为完成状态）
            processClipTaskDirectly(clipTaskId, result);
            return result;
        } catch (Exception e) {
            throw new ServiceException("云剪辑合成失败: " + e.getMessage());
        }
    }

    /**
     * 根据对话组ID查找普通视频合成任务（排除云剪辑任务）
     */
    private List<PlatformVideo> findTasksByDialogueGroupId(String dialogueGroupId) {
        // 查询所有任务（不限制状态）
        PlatformVideo queryParam = new PlatformVideo();
        List<PlatformVideo> allTasks = platformVideoMapper.selectPlatformVideoList(queryParam);
        // 筛选出指定对话组的普通视频合成任务（排除云剪辑任务）
        return allTasks.stream().filter(task -> {
                String taskGroupId = extractDialogueGroupId(task);
                if (!dialogueGroupId.equals(taskGroupId)) {
                    return false;
                }
                // 排除云剪辑任务：检查operation中的type字段
                if (StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        String type = operation.getString("type");
                        if ("video_clip".equals(type)) {
                            return false; // 排除云剪辑任务
                        }
                    } catch (Exception e) {
                        // 忽略JSON解析错误，继续处理
                    }
                }
                return true; // 包含普通视频合成任务
            })
            .sorted((t1, t2) -> {
                Integer order1 = extractDialogueOrder(t1);
                Integer order2 = extractDialogueOrder(t2);
                // 处理null值，null值排在最后，如果都是null则按ID排序
                if (order1 == null && order2 == null) {
                    return t1.getId().compareTo(t2.getId());
                }
                if (order1 == null) return 1;
                if (order2 == null) return -1;
                return order1.compareTo(order2);
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据对话组ID查找已完成的任务（用于云剪辑）
     */
    private List<PlatformVideo> findCompletedTasksByDialogueGroupId(String dialogueGroupId) {
        // 查询所有已完成的任务
        List<PlatformVideo> allCompletedTasks = platformVideoMapper.selectCompletedDialogueTasks();
        // 筛选出指定对话组的任务
        return allCompletedTasks.stream()
            .filter(task -> {
                String taskGroupId = extractDialogueGroupId(task);
                return dialogueGroupId.equals(taskGroupId);
            })
            .sorted((t1, t2) -> {
                Integer order1 = extractDialogueOrder(t1);
                Integer order2 = extractDialogueOrder(t2);
                // 处理null值，null值排在最后，如果都是null则按ID排序
                if (order1 == null && order2 == null) {
                    return t1.getId().compareTo(t2.getId());
                }
                if (order1 == null) return 1;
                if (order2 == null) return -1;
                return order1.compareTo(order2);
            })
            .collect(Collectors.toList());
    }

    /**
     * 验证任务是否都已完成
     */
    private void validateTasksCompleted(List<PlatformVideo> tasks) {
        for (PlatformVideo task : tasks) {
            if (!STATUS_SUCCESS.equals(task.getStatus())) {
                throw new ServiceException("任务ID " + task.getId() + " 尚未完成，当前状态: " + task.getStatus());
            }
            if (StringUtils.isEmpty(task.getResultVideo())) {
                throw new ServiceException("任务ID " + task.getId() + " 的结果视频为空");
            }
        }
    }

    /**
     * 从任务中提取对话组ID
     */
    private String extractDialogueGroupId(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                return operation.getString("dialogueGroupId");
            }
        } catch (Exception e) {
            log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
        }
        return null;
    }

    /**
     * 从任务中提取对话顺序
     */
    private Integer extractDialogueOrder(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                Integer order = operation.getInteger("dialogueOrder");
                return order != null ? order : 0;
            }
        } catch (Exception e) {
            log.warn("解析任务对话顺序失败，任务ID: {}", task.getId());
        }
        return 0;
    }

    /**
     * 标记任务已进行云剪辑合成
     */
    private void markTasksAsClippedSimple(List<PlatformVideo> groupTasks, Map<String, Object> clipResult) {
        for (PlatformVideo task : groupTasks) {
            try {
                JSONObject operation = StringUtils.isNotEmpty(task.getOperation())
                    ? JSONObject.parseObject(task.getOperation()): new JSONObject();
                operation.put("videoClipped", true);
                operation.put("clipJobId", clipResult.get("jobId"));
                operation.put("clipProjectId", clipResult.get("projectId"));
                operation.put("clipTime", new Date());
                task.setOperation(operation.toJSONString());
                platformVideoMapper.updatePlatformVideo(task);
            } catch (Exception e) {
                log.error("标记任务云剪辑状态失败，任务ID: {}", task.getId());
            }
        }
    }

    /**
     * 创建云剪辑任务记录到platform_video表
     */
    private Long createClipTaskRecord(Map<String, Object> clipResult, String dialogueGroupId, String title, String    description, List<PlatformVideo> groupTasks) {
        try {
            PlatformVideo clipTask = new PlatformVideo();
            // 基本信息
            clipTask.setNumber(StringUtils.isNotEmpty(title) ? title : "云剪辑合成_" + dialogueGroupId);
            clipTask.setStatus("3"); // 直接设置为完成状态，因为云剪辑任务不参与轮询
            // 从原始任务中获取用户信息，避免异步线程中无法获取用户上下文的问题
            String createBy = null;
            if (!groupTasks.isEmpty()) {
                createBy = groupTasks.get(0).getCreateBy();
            }
            if (StringUtils.isEmpty(createBy)) {
                throw new ServiceException("无法获取任务创建者信息");
            }
            clipTask.setCreateBy(createBy);
            clipTask.setCreatedAt(new Date());
            clipTask.setUpdateBy(createBy);
            clipTask.setUpdateTime(new Date());
            clipTask.setCompleteAt(new Date()); // 设置完成时间，因为直接设置为完成状态
            // 从临时URL中提取真实的文件存储路径并设置结果视频
            String outputMediaURL = (String) clipResult.get("outputMediaURL");
            if (StringUtils.isNotEmpty(outputMediaURL)) {
                try {
                    String resultVideoPath = extractRealFilePathFromTempUrl(outputMediaURL);
                    clipTask.setResultVideo(resultVideoPath);
                } catch (Exception e) {
                    log.warn("提取云剪辑结果视频路径失败: {}", e.getMessage());
                }
            }
            // 构建operation信息
            JSONObject operation = new JSONObject();
            operation.put("type", "video_clip");
            operation.put("dialogueGroupId", dialogueGroupId);
            operation.put("clipJobId", clipResult.get("jobId"));
            operation.put("clipProjectId", clipResult.get("projectId"));
            operation.put("clipMediaId", clipResult.get("mediaId"));
            operation.put("outputMediaURL", clipResult.get("outputMediaURL"));
            operation.put("title", title);
            operation.put("description", description);
            operation.put("clipTime", new Date());
            // 设置收费相关信息（动态计算）
            Map<String, Object> costInfo = calculateClipCostInfo(groupTasks);
            operation.put("hashrate_cost_title", "云剪辑合成");
            operation.put("hashrate_cost_unit", costInfo.get("unitCost"));
            operation.put("version", "CLIP");
            operation.put("source_versions", costInfo.get("sourceVersions")); // 记录源视频版本信息
            // 计算预估时长（基于源视频实际时长）
            double totalDuration = calculateActualTotalDuration(groupTasks);
            operation.put("estimated_duration", totalDuration);
            clipTask.setOperation(operation.toJSONString());
            clipTask.setRemark("云剪辑合成任务 - 对话组ID: " + dialogueGroupId);

            // 根据源视频版本设置云剪辑任务版本
            String clipVersion = determineClipVersion(groupTasks);
            clipTask.setVersion(clipVersion);
            platformVideoMapper.insertPlatformVideo(clipTask);
            return clipTask.getId();
        } catch (Exception e) {
            throw new ServiceException("创建云剪辑任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 确定云剪辑任务的版本（基于源视频版本）
     */
    private String determineClipVersion(List<PlatformVideo> groupTasks) {
        if (groupTasks == null || groupTasks.isEmpty()) {
            return "CLIP"; // 默认云剪辑版本
        }
        // 统计各版本数量
        Map<String, Integer> versionCounts = new HashMap<>();
        for (PlatformVideo task : groupTasks) {
            String version = task.getVersion();
            if (StringUtils.isNotEmpty(version)) {
                versionCounts.put(version, versionCounts.getOrDefault(version, 0) + 1);
            } else {
                versionCounts.put("V", versionCounts.getOrDefault("V", 0) + 1); // 默认V版
            }
        }
        // 如果全部是同一版本，使用该版本
        if (versionCounts.size() == 1) {
            String singleVersion = versionCounts.keySet().iterator().next();
            return "CLIP_" + singleVersion; // 例如：CLIP_V, CLIP_M, CLIP_H
        }
        // 如果是混合版本，使用最高级别的版本
        if (versionCounts.containsKey("V")) {
            return "CLIP_V"; // V版优先级最高
        } else if (versionCounts.containsKey("H")) {
            return "CLIP_H"; // H版次之
        } else if (versionCounts.containsKey("M")) {
            return "CLIP_M"; // M版最后
        }
        return "CLIP"; // 默认
    }

    /**
     * 计算云剪辑收费信息（根据源视频版本和模型动态计算）
     */
    private Map<String, Object> calculateClipCostInfo(List<PlatformVideo> groupTasks) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Integer> versionCounts = new HashMap<>();
        List<String> sourceVersions = new ArrayList<>();
        List<String> sourceModels = new ArrayList<>();
        // 统计各版本数量和收集版本、模型信息
        for (PlatformVideo task : groupTasks) {
            String version = task.getVersion();
            String model = task.getModel();

            if (StringUtils.isNotEmpty(version)) {
                versionCounts.put(version, versionCounts.getOrDefault(version, 0) + 1);
                sourceVersions.add(version);
            } else {
                // 默认为V版
                versionCounts.put("V", versionCounts.getOrDefault("V", 0) + 1);
                sourceVersions.add("V");
            }

            if (StringUtils.isNotEmpty(model)) {
                sourceModels.add(model);
            }
        }
        // 计算云剪辑单价（根据版本和模型）
        int clipUnitCost = calculateUnitCostByVersionAndModel(versionCounts, sourceModels);
        result.put("unitCost", clipUnitCost);
        result.put("sourceVersions", sourceVersions);
        result.put("sourceModels", sourceModels);
        result.put("versionCounts", versionCounts);
        return result;
    }

    /**
     * 根据版本和模型计算单价
     */
    private int calculateUnitCostByVersionAndModel(Map<String, Integer> versionCounts, List<String> sourceModels) {
        int maxUnitCost = 500; // 默认最低单价
        // 检查各版本的单价
        for (String version : versionCounts.keySet()) {
            int versionCost = getVersionUnitCost(version, sourceModels);
            maxUnitCost = Math.max(maxUnitCost, versionCost);
        }
        return maxUnitCost;
    }

    /**
     * 获取指定版本的单价（考虑模型）
     */
    private int getVersionUnitCost(String version, List<String> sourceModels) {
        switch (version.toUpperCase()) {
            case "H":
                return 500; // H版固定500/分钟
            case "M":
                return 500; // M版固定500/分钟
            case "V":
                // V版需要根据模型区分
                return getVVersionUnitCost(sourceModels);
            default:
                return 500; // 默认500/分钟
        }
    }

    /**
     * 获取V版的单价（根据模型）
     */
    private int getVVersionUnitCost(List<String> sourceModels) {
        int maxPrice = 500; // 默认最低价格

        // 遍历所有模型，获取最高价格
        for (String model : sourceModels) {
            if (StringUtils.isNotEmpty(model)) {
                int modelPrice = platformModelService.getModelPrice(model);
                maxPrice = Math.max(maxPrice, modelPrice);
            }
        }
        return maxPrice;
    }

    /**
     * 计算对话组视频实际总时长（基于结果视频）
     */
    private double calculateActualTotalDuration(List<PlatformVideo> groupTasks) {
        double totalDuration = 0.0;
        for (PlatformVideo task : groupTasks) {
            try {
                if (StringUtils.isNotEmpty(task.getResultVideo())) {
                    double duration = getVideoDuration(task.getResultVideo());
                    totalDuration += duration;
                } else {
                    totalDuration += 10.0; // 默认10秒
                    log.warn("任务 {} 没有结果视频，使用默认10秒", task.getId());
                }
            } catch (Exception e) {
                log.warn("获取任务 {} 视频时长失败，使用默认10秒", task.getId());
                totalDuration += 10.0;
            }
        }
        return totalDuration;
    }

    /**
     * 获取视频文件时长（秒）
     */
    private double getVideoDuration(String videoPath) {
        try {
            Object mediaService = SpringUtils.getBean("mediaServiceImpl");
            if (mediaService == null) {
                return 10.0; // 默认10秒
            }
            String fullOssUrl = convertToFullOssUrl(videoPath);
            Method getMediaInfoMethod = mediaService.getClass().getMethod("getMediaInfo",
                String.class, String.class, String.class, String.class);
            String mediaInfoJson = (String) getMediaInfoMethod.invoke(mediaService,
                null, fullOssUrl, null, null);
            if (StringUtils.isNotEmpty(mediaInfoJson)) {
                double duration = parseVideoDurationFromMediaInfo(mediaInfoJson);
                if (duration > 0) {
                    return duration;
                }
            }
            return 10.0; // 默认10秒
        } catch (Exception e) {
            log.error("获取视频时长失败，使用默认10秒: {}", videoPath, e);
            return 10.0;
        }
    }

    /**
     * 转换相对路径为完整的OSS URL
     */
    private String convertToFullOssUrl(String videoPath) {
        try {
            if (StringUtils.isEmpty(videoPath)) {
                return "";
            }
            // 如果已经是完整URL，直接返回
            if (videoPath.startsWith("http://") || videoPath.startsWith("https://")) {
                return videoPath;
            }
            // 构建完整的OSS URL（使用实际的OSS配置）
            // 根据配置文件：bucketName: szb-pc, endpoint: oss-cn-beijing.aliyuncs.com
            String ossEndpoint = "https://szb-pc.oss-cn-beijing.aliyuncs.com";
            // 确保路径以/开头
            if (!videoPath.startsWith("/")) {
                videoPath = "/" + videoPath;
            }
            String fullUrl = ossEndpoint + videoPath;
            return fullUrl;
        } catch (Exception e) {
            log.error("转换OSS URL失败: {}", e.getMessage());
            return videoPath;
        }
    }

    /**
     * 从ICE媒资信息中解析视频时长
     */
    private double parseVideoDurationFromMediaInfo(String mediaInfoJson) {
        try {
            JSONObject mediaInfo = JSONObject.parseObject(mediaInfoJson);
            // 检查响应是否成功
            if (mediaInfo.containsKey("Code") && !"Success".equals(mediaInfo.getString("Code"))) {
                log.warn("ICE API返回错误: {}", mediaInfo.getString("Message"));
                return 0.0;
            }
            // 解析媒资信息
            if (mediaInfo.containsKey("MediaInfo")) {
                JSONObject mediaInfoData = mediaInfo.getJSONObject("MediaInfo");
                // 检查媒资状态
                if (mediaInfoData.containsKey("MediaBasicInfo")) {
                    JSONObject basicInfo = mediaInfoData.getJSONObject("MediaBasicInfo");
                    String status = basicInfo.getString("Status");
                    // 如果媒资还在准备中，记录状态但继续尝试获取时长
                    if ("Preparing".equals(status)) {

                    }
                }
                // 从FileInfoList中获取视频信息
                if (mediaInfoData.containsKey("FileInfoList")) {
                    JSONArray fileInfoList = mediaInfoData.getJSONArray("FileInfoList");
                    if (fileInfoList != null && fileInfoList.size() > 0) {
                        JSONObject fileInfo = fileInfoList.getJSONObject(0);
                        // 获取视频流信息
                        if (fileInfo.containsKey("VideoStreamInfoList")) {
                            JSONArray videoStreams = fileInfo.getJSONArray("VideoStreamInfoList");
                            if (videoStreams != null && videoStreams.size() > 0) {
                                JSONObject videoStream = videoStreams.getJSONObject(0);
                                if (videoStream.containsKey("Duration")) {
                                    // ICE返回的时长通常是字符串格式的秒数
                                    String durationStr = videoStream.getString("Duration");
                                    return Double.parseDouble(durationStr);
                                }
                            }
                        }
                        // 备选方案：从基本信息中获取时长
                        if (fileInfo.containsKey("Duration")) {
                            String durationStr = fileInfo.getString("Duration");
                            return Double.parseDouble(durationStr);
                        }
                    }
                }
            }
            log.warn("无法从媒资信息中解析视频时长，媒资可能还在处理中: {}", mediaInfoJson);
            return 0.0;
        } catch (Exception e) {
            log.error("解析媒资信息失败: {}", e.getMessage());
            return 0.0;
        }
    }

    private Map<String, Object> checkDialogueGroupStatus(String dialogueGroupId) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("dialogueGroupId", dialogueGroupId);
        try {
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);
            if (groupTasks.isEmpty()) {
                result.put("status", "NOT_FOUND");
                result.put("message", "找到对话组ID为 " + dialogueGroupId + " 的任务");
                result.put("canClip", false);
                return result;
            }
            // 统计任务状态
            int totalTasks = groupTasks.size();
            int completedTasks = 0;
            int failedTasks = 0;
            int processingTasks = 0;
            int pendingTasks = 0;
            List<Map<String, Object>> taskDetails = new ArrayList<>();
            for (PlatformVideo task : groupTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskId", task.getId());
                taskInfo.put("status", task.getStatus());
                taskInfo.put("order", extractDialogueOrder(task));
                // 从operation中提取更多信息
                if (StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        taskInfo.put("speakerName", operation.getString("speakerName"));
                        taskInfo.put("text", operation.getString("text"));
                        taskInfo.put("videoClipped", operation.getBooleanValue("videoClipped"));
                    } catch (Exception e) {
                        log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
                    }
                }
                switch (task.getStatus()) {
                    case STATUS_SUCCESS:
                        completedTasks++;
                        break;
                    case STATUS_FAILED:
                        failedTasks++;
                        break;
                    case STATUS_PROCESSING:
                        processingTasks++;
                        break;
                    case STATUS_PENDING:
                        pendingTasks++;
                        break;
                }
                taskInfo.put("statusText", getStatusDescription(task.getStatus()));
                taskDetails.add(taskInfo);
            }
            // 判断整体状态
            boolean allCompleted = completedTasks == totalTasks;
            boolean hasFailures = failedTasks > 0;
            boolean canClip = allCompleted && !hasFailures;
            String overallStatus;
            String message;
            if (allCompleted) {
                overallStatus = "COMPLETED";
                message = "所有任务已完成，可以进行云剪辑合成";
            } else if (hasFailures) {
                overallStatus = "FAILED";
                message = String.format("有 %d 个任务失败，无法进行云剪辑合成", failedTasks);
            } else if (processingTasks > 0) {
                overallStatus = "PROCESSING";
                message = String.format("还有 %d 个任务正在处理中...", processingTasks);
            } else {
                overallStatus = "PENDING";
                message = String.format("还有 %d 个任务待处理", pendingTasks);
            }
            // 检查是否已经进行过云剪辑
            boolean alreadyClipped = groupTasks.stream().anyMatch(task -> {
                try {
                    if (StringUtils.isNotEmpty(task.getOperation())) {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return operation.getBooleanValue("videoClipped");
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
                return false;
            });
            if (alreadyClipped) {
                canClip = false;
                message += "（已进行过云剪辑合成）";
            }
            result.put("status", overallStatus);
            result.put("message", message);
            result.put("canClip", canClip);
            result.put("totalTasks", totalTasks);
            result.put("completedTasks", completedTasks);
            result.put("failedTasks", failedTasks);
            result.put("processingTasks", processingTasks);
            result.put("pendingTasks", pendingTasks);
            result.put("alreadyClipped", alreadyClipped);
            result.put("taskDetails", taskDetails);
            return result;
        } catch (Exception e) {
            log.error("检查对话组状态失败: {}", dialogueGroupId, e);
            result.put("status", "ERROR");
            result.put("message", "检查状态失败: " + e.getMessage());
            result.put("canClip", false);
            return result;
        }
    }
}
