### 与权限有关的注解

`@Anonymous`注解用于配置公开接口

`@PreAuthorize`注解用于配置接口要求用户拥有某些权限才可访问，它拥有如下方法

| 方法        | 参数   | 描述                                           |
| ----------- | ------ | ---------------------------------------------- |
| hasPermi    | String | 验证用户是否具备某权限                         |
| lacksPermi  | String | 验证用户是否不具备某权限，与 hasPermi逻辑相反  |
| hasAnyPermi | String | 验证用户是否具有以下任意一个权限               |
| hasRole     | String | 判断用户是否拥有某个角色                       |
| lacksRole   | String | 验证用户是否不具备某角色，与 isRole逻辑相反    |
| hasAnyRoles | String | 验证用户是否具有以下任意一个角色，多个逗号分隔 |

```java
@PreAuthorize("@ss.hasPermi('system:user:list')")
@PreAuthorize("@ss.lacksPermi('system:user:list')")
@PreAuthorize("@ss.hasAnyPermi('system:user:add,system:user:edit')")
```

`@DataScope`注解用于配置接口数据权限

* `deptAlias`用于指定部门表的别名;
* `userAlias`用于指定用户表的别名;
* 实体需要继承BaseEntity类;
* `全部数据权限`、`自定数据权限`、`部门数据权限`、`部门及以下数据权限`、`仅本人数据权限`五种权限模式在后台角色管理界面配置数据权限

```java
// 部门数据权限注解
@DataScope(deptAlias = "d")
// 部门及用户权限注解
@DataScope(deptAlias = "d", userAlias = "u")
```

1. 使用注解

```java

@DataScope(deptAlias = "d", userAlias = "u")
public List<...> select(...)
{
    return mapper.select(...);
}
```

2. 配置mybatis的xml

```xml
<select id="select" parameterType="..." resultMap="...Result">
    <include refid="select...Vo"/>
    <!-- 数据范围过滤 -->
    ${params.dataScope}
</select>
```

### 安全工具类

com.ruoyi.common.utils.SecurityUtils

| 方法         | 参数   | 返回       | 描述                     |
| ------------ | ------ | ---------- | ------------------------ |
| getUserId    | 无     | Long       | 获取当前用户ID           |
| getDeptId    | 无     | Long       | 获取当前部门ID           |
| getUsername  | 无     | String     | 获取当前用户账户         |
| getLoginUser | 无     | LonginUser | 获取当前登录用户代理     |
| hasPermi     | String | boolean    | 验证用户是否具备某权限   |
| hasRole      | String | boolean    | 验证用户是否拥有某个角色 |
