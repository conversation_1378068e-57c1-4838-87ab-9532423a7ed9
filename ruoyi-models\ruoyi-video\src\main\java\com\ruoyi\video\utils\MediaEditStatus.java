package com.ruoyi.video.utils;

/**
 * 媒体编辑状态枚举
 *
 * <AUTHOR>
 */
public enum MediaEditStatus {

    DRAFT("Draft", "草稿"),
    EDITING("Editing", "编辑中"),
    PRODUCING("Producing", "制作中"),
    PRODUCED("Produced", "已制作完成"),
    PRODUCE_FAILED("ProduceFailed", "制作失败"),
    NORMAL("Normal", "正常"),
    DEFAULT("default", "未知");

    private final String code;
    private final String description;

    MediaEditStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举实例
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static MediaEditStatus fromCode(String code) {
        for (MediaEditStatus status : MediaEditStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举实例
     *
     * @param description 状态描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static MediaEditStatus fromDescription(String description) {
        for (MediaEditStatus status : MediaEditStatus.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    @Override
    public String toString() {
        return this.code + ":" + this.description;
    }
}