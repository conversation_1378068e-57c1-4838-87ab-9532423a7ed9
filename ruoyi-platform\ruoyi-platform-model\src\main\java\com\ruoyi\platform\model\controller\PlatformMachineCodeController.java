package com.ruoyi.platform.model.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.model.domain.PlatformMachineCode;
import com.ruoyi.platform.model.service.IPlatformMachineCodeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 机器管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-20
 */
@RestController
@RequestMapping("/platform/code")
@Tag(name = "【机器管理】管理")
public class PlatformMachineCodeController extends BaseController
{
    @Autowired
    private IPlatformMachineCodeService platformMachineCodeService;

    /**
     * 查询机器管理列表
     */
    @Operation(summary = "查询机器管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlatformMachineCode platformMachineCode)
    {
        startPage();
        List<PlatformMachineCode> list = platformMachineCodeService.selectPlatformMachineCodeList(platformMachineCode);
        return getDataTable(list);
    }

    /**
     * 导出机器管理列表
     */
    @Operation(summary = "导出机器管理列表")
    @PreAuthorize("@ss.hasPermi('platform:code:export')")
    @Log(title = "机器管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformMachineCode platformMachineCode)
    {
        List<PlatformMachineCode> list = platformMachineCodeService.selectPlatformMachineCodeList(platformMachineCode);
        ExcelUtil<PlatformMachineCode> util = new ExcelUtil<PlatformMachineCode>(PlatformMachineCode.class);
        util.exportExcel(response, list, "机器管理数据");
    }

    /**
     * 获取机器管理详细信息
     */
    @Operation(summary = "获取机器管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:code:query')")
    @GetMapping(value = "/{machineCodeId}")
    public AjaxResult getInfo(@PathVariable("machineCodeId") Long machineCodeId)
    {
        return success(platformMachineCodeService.selectPlatformMachineCodeByMachineCodeId(machineCodeId));
    }

    /**
     * 新增机器管理
     */
    @Operation(summary = "新增机器管理")
    //@PreAuthorize("@ss.hasPermi('platform:code:add')")
    @Log(title = "机器管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlatformMachineCode platformMachineCode)
    {
        platformMachineCode.setCreateBy(getUsername());
        platformMachineCode.setUpdateBy(getUsername());
        return toAjax(platformMachineCodeService.insertPlatformMachineCode(platformMachineCode));
    }

    /**
     * 修改机器管理
     */
    @Operation(summary = "修改机器管理")
    //@PreAuthorize("@ss.hasPermi('platform:code:edit')")
    @Log(title = "机器管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlatformMachineCode platformMachineCode)
    {
        platformMachineCode.setUpdateBy(getUsername());
        return toAjax(platformMachineCodeService.updatePlatformMachineCode(platformMachineCode));
    }

    /**
     * 删除机器管理
     */
    @Operation(summary = "删除机器管理")
    //@PreAuthorize("@ss.hasPermi('platform:code:remove')")
    @Log(title = "机器管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{machineCodeIds}")
    public AjaxResult remove(@PathVariable( name = "machineCodeIds" ) Long[] machineCodeIds) 
    {
        return toAjax(platformMachineCodeService.deletePlatformMachineCodeByMachineCodeIds(machineCodeIds));
    }
}
