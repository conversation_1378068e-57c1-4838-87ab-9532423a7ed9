package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能主播对象 platform_anchor
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
@Schema(description = "智能主播对象")
public class PlatformAnchor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Integer anchorId;

    /** 项目Id */
    @Schema(title = "项目Id")
    @Excel(name = "项目Id")
    private Integer projectId;

    /** 智能主播名称 */
    @Schema(title = "智能主播名称")
    @Excel(name = "智能主播名称")
    private String anchorName;

    /** 智能主播知识库 */
    @Schema(title = "智能主播知识库")
    @Excel(name = "智能主播知识库")
    private String anchorRepository;

    public void setAnchorId(Integer anchorId) 
    {
        this.anchorId = anchorId;
    }

    public Integer getAnchorId() 
    {
        return anchorId;
    }


    public void setProjectId(Integer projectId) 
    {
        this.projectId = projectId;
    }

    public Integer getProjectId() 
    {
        return projectId;
    }


    public void setAnchorName(String anchorName) 
    {
        this.anchorName = anchorName;
    }

    public String getAnchorName() 
    {
        return anchorName;
    }


    public void setAnchorRepository(String anchorRepository) 
    {
        this.anchorRepository = anchorRepository;
    }

    public String getAnchorRepository() 
    {
        return anchorRepository;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("anchorId", getAnchorId())
            .append("projectId", getProjectId())
            .append("anchorName", getAnchorName())
            .append("anchorRepository", getAnchorRepository())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
