<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformModelMapper">
    
    <resultMap type="PlatformModel" id="WyModelResult">
        <result property="modelId"    column="model_id"    />
        <result property="modelCode"    column="model_code"    />
        <result property="modelName"    column="model_name"    />
        <result property="modelStatus"    column="model_status"    />
        <result property="modelVersion"    column="model_version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectWyModelVo">
        select model_id, model_code, model_name, model_status, model_version, create_by, create_time, update_by, update_time, remark from platform_model
    </sql>

    <select id="selectWyModelList" parameterType="PlatformModel" resultMap="WyModelResult">
        <include refid="selectWyModelVo"/>
        <where>  
            <if test="modelCode != null  and modelCode != ''"> and model_code = #{modelCode}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="modelStatus != null "> and model_status = #{modelStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWyModelByModelId" parameterType="Long" resultMap="WyModelResult">
        <include refid="selectWyModelVo"/>
        where platform_model.model_id = #{modelId}
    </select>
        
    <insert id="insertWyModel" parameterType="PlatformModel" useGeneratedKeys="true" keyProperty="modelId">
        insert into platform_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelCode != null and modelCode != ''">model_code,</if>
            <if test="modelName != null and modelName != ''">model_name,</if>
            <if test="modelStatus != null">model_status,</if>
            <if test="modelVersion != null">model_version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelCode != null and modelCode != ''">#{modelCode},</if>
            <if test="modelName != null and modelName != ''">#{modelName},</if>
            <if test="modelStatus != null">#{modelStatus},</if>
            <if test="modelVersion != null">#{modelVersion},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateWyModel" parameterType="PlatformModel">
        update platform_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelCode != null and modelCode != ''">model_code = #{modelCode},</if>
            <if test="modelName != null and modelName != ''">model_name = #{modelName},</if>
            <if test="modelStatus != null">model_status = #{modelStatus},</if>
            <if test="modelVersion != null">model_version = #{modelVersion},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where platform_model.model_id = #{modelId}
    </update>

    <delete id="deleteWyModelByModelId" parameterType="Long">
        delete from platform_model where model_id = #{modelId}
    </delete>

    <delete id="deleteWyModelByModelIds" parameterType="String">
        delete from platform_model where model_id in 
        <foreach item="modelId" collection="array" open="(" separator="," close=")">
            #{modelId}
        </foreach>
    </delete>
</mapper>