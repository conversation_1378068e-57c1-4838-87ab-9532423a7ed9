package com.ruoyi.tingwu.service;

import java.util.List;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.tingwu.domain.TingwuPhrase;

/**
 * 词表信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface ITingwuPhraseService
{
    /**
     * 查询词表信息
     *
     * @param keyId 词表信息主键
     * @return 词表信息
     */
    public JSONArray selectTingwuPhraseByPhraseId(String phraseId);

    /**
     * 查询词表信息列表
     *
     * @param tingwuPhrase 词表信息
     * @return 词表信息集合
     */
    public List<TingwuPhrase> selectTingwuPhraseList(TingwuPhrase tingwuPhrase);

    /**
     * 新增词表信息
     *
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    public JSONObject insertTingwuPhrase(TingwuPhrase tingwuPhrase);

    /**
     * 修改词表信息
     *
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    public JSONObject updateTingwuPhrase(TingwuPhrase tingwuPhrase);



    /**
     * 删除词表信息信息
     *
     * @param phraseId 词表信息主键
     * @return 结果
     */
    public JSONObject deleteTingwuPhraseByPhraseIdIds(String[] phraseIds);
}
