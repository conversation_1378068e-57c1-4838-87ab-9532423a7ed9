<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.model.mapper.PlatformTaskMapper">
    
    <resultMap type="PlatformTask" id="PlatformTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="taskContent"    column="task_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="machineCode"    column="machine_code"    />
        <result property="taskResult"    column="task_result"    />
        <result property="type"    column="type"    />
        <result property="modelName"    column="model_name"    />
    </resultMap>

    <sql id="selectPlatformTaskVo">
        select task_id, task_name, task_status, task_content, t.create_by, t.create_time, t.update_by, t.update_time, t.remark, machine_code, task_result, type, model_name from platform_task t left join sys_user u on u.user_name = t.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformTaskList" parameterType="PlatformTask" resultMap="PlatformTaskResult">
        <include refid="selectPlatformTaskVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            ${params.dataScope}
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="taskContent != null  and taskContent != ''"> and task_content = #{taskContent}</if>
            <if test="machineCode != null  and machineCode != ''"> and machine_code = #{machineCode}</if>
            <if test="taskResult != null  and taskResult != ''"> and task_result = #{taskResult}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and t.create_by = #{createBy}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPlatformTaskByTaskId" parameterType="Long" resultMap="PlatformTaskResult">
        <include refid="selectPlatformTaskVo"/>
        where task_id = #{taskId}
    </select>
        
    <insert id="insertPlatformTask" parameterType="PlatformTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into platform_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">task_name,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="taskContent != null">task_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="machineCode != null">machine_code,</if>
            <if test="taskResult != null">task_result,</if>
            <if test="type != null">type,</if>
            <if test="modelName != null">model_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">#{taskName},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="taskContent != null">#{taskContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="machineCode != null">#{machineCode},</if>
            <if test="taskResult != null">#{taskResult},</if>
            <if test="type != null">#{type},</if>
            <if test="modelName != null">#{modelName},</if>
         </trim>
    </insert>

    <update id="updatePlatformTask" parameterType="PlatformTask">
        update platform_task t
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="taskContent != null">task_content = #{taskContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="machineCode != null">machine_code = #{machineCode},</if>
            <if test="taskResult != null">task_result = #{taskResult},</if>
            <if test="type != null">type = #{type},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
        </trim>
        where t.task_id = #{taskId}
    </update>

    <delete id="deletePlatformTaskByTaskId" parameterType="Long">
        delete from platform_task where task_id = #{taskId}
    </delete>

    <delete id="deletePlatformTaskByTaskIds" parameterType="String">
        delete from platform_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>