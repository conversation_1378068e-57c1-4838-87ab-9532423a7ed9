# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        dynamic:
            primary: MASTER
            xa: true
            datasource:
                # 主库数据源
                MASTER:
                    # 线上数据库 生产环境使用
                    # url: **********************************************************************************************************************************************
                    # username: platform
                    # password: Nx7YsD8BN3jiybLL

                    # 公司内部环境 部署环境使用
                    # url: ********************************************************************************************************************************************************************************
                    # username: szb_platform
                    # password: <PERSON><PERSON><PERSON>B<PERSON>@Mysql

                    # 算力机数据库 研发环境使用
                    url: **************************************************************************************************************************************************
                    username: platform
                    password: platform

                # 从库数据源
                # SLAVE:
                #     url: *****************************************************************************************************************************************
                #     username: root
                #     password: 123456
        druid:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            # 配置监控统计拦截的filters  stat=>监控统计 wall=>防SQL注入 slf4j log4j2=>日志记录
            filters: stat,wall,slf4j,log4j2
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
            connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

            # 配置spring监控的匹配类
            aop-patterns: com.ruoyi.*.service.*,com.ruoyi.*.mapper.*

            
            # Web应用 和 URL监控 配置
            web-stat-filter:
                enabled: true
                url-pattern: /*
                exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
                session-stat-enable: false  # 是否开启session统计功能
                
            # 监控视图配置
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                reset-enable: true      # 是否可以重置日志
                login-username: ruoyi   # 用户名
                login-password: 123456  # 密码
                allow: ""               # IP白名单 (没有配置或者为空，则允许所有访问)
                deny: ""                # IP黑名单 (存在共同时，deny优先于allow)
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

# 是否开启分布式事务，如不开启，请删除atomikos插件，否则atomikos相关驱动虽不生效但仍会启动
atomikos:
  enabled: false
