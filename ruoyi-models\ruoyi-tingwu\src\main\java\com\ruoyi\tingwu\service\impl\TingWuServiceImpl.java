package com.ruoyi.tingwu.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.profile.DefaultProfile;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.tingwu.config.TingWuConfig;

import com.ruoyi.tingwu.domain.TingWuRequest;
import com.ruoyi.tingwu.domain.TingWuResult;
import com.ruoyi.tingwu.domain.TingwuTrans;
import com.ruoyi.tingwu.enums.AudioLanguage;
import com.ruoyi.tingwu.enums.AudioVideoStatus;
import com.ruoyi.tingwu.service.ITingwuTransService;
import com.ruoyi.tingwu.service.TingWuService;
import com.ruoyi.tingwu.utils.JsonParserUtil;
import com.ruoyi.tingwu.utils.RequestUtils;
import com.ruoyi.tingwu.utils.TingWuUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class TingWuServiceImpl implements TingWuService {


    private final ITingwuTransService tingwuTransService;

    private final DefaultProfile defaultProfile;

    private final TingWuConfig tingWuConfig;



    /**
     * 提交离线任务
     */
    @Override
    public JSONObject summitOfflineTask(MultipartFile file, TingWuRequest tingWuRequest) {
        try {
            CommonRequest request = RequestUtils.createTaskRequest();
            request.putQueryParameter("type", "offline");

            JSONObject root = new JSONObject();
            root.put("AppKey", tingWuConfig.getAppKey());

            // 将file 存储到oss里面并获取url
            String fullPath = TingWuUtils.getRecordFileFullPath(file);
            String fileUrl = FileOperateUtils.upload(fullPath, file, null);

            JSONObject input = new JSONObject();
            String sourceLanguage = AudioLanguage.fromCode(tingWuRequest.getLanguage()).getDescription();

            input.fluentPut("FileUrl", fileUrl)
                    .fluentPut("SourceLanguage", sourceLanguage)
                    .fluentPut("TaskKey", "task" + System.currentTimeMillis());

            // 如果选择的是多语言，则需要设置LanguageHints，在这里只设置了中文和英文
            String lanCode = tingWuRequest.getLanguage();
            if (AudioLanguage.CHINESE_ENGLISH_FREE.getCode().equals(lanCode))
                input.fluentPut("LanguageHints", new String[] { "cn", "en" });

            root.put("Input", input);

            JSONObject parameters = TingWuUtils.initRequestParameters(tingWuRequest);
            root.put("Parameters", parameters);

            System.out.println(root.toJSONString());
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

            IAcsClient client = new DefaultAcsClient(defaultProfile);
            CommonResponse response = client.getCommonResponse(request);
            JSONObject body = JSONObject.parseObject(response.getData());
            JSONObject data = (JSONObject) body.get("Data");
            log.info("data:{}", data);

            // 组装数据,向数据库表中插入数据
            TingwuTrans tingwuTrans = new TingwuTrans();
            tingwuTrans.setVaName(file.getOriginalFilename());
            tingwuTrans.setVaPath(fullPath);
            tingwuTrans.setVaStatus("1");
            tingwuTrans.setTaskId(data.getString("TaskId"));
            tingwuTransService.insertTingwuTrans(tingwuTrans);
            return data;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取任务状态和结果
     */
    @Override
    public JSONObject getStatusAndResultByTaskId(String taskId) {
        try {
            CommonRequest request = RequestUtils.createResultRequest(taskId);

            IAcsClient client = new DefaultAcsClient(defaultProfile);
            CommonResponse response = client.getCommonResponse(request);

            System.out.println(response);
            System.out.println(response.getData());
            String returnData = response.getData();

            // 判断一下状态
            JSONObject jsonObject = JSONObject.parseObject(returnData);
            JSONObject data = jsonObject.getJSONObject("Data");
            String taskStatus = data.getString("TaskStatus");

            if (AudioVideoStatus.FAILED.getDescription().equals(taskStatus)) {// 如果失败，更新数据
                // 保存到数据库
                TingwuTrans failTrans = new TingwuTrans();
                failTrans.setTaskId(taskId);
                failTrans.setVaStatus(AudioVideoStatus.FAILED.getCode());
                tingwuTransService.updateTingwuTransByTaskId(failTrans);

            } else if ((AudioVideoStatus.SUCCESS.getDescription().equals(taskStatus))) {
                TingWuResult result = JsonParserUtil.parseTingWuResult(returnData);
                if (result != null) {
                    // 解析返回结果并存储到数据库中
                    TingwuTrans processTingwuResult = TingWuUtils.processTingwuResult(result, taskId);
                    tingwuTransService.updateTingwuTransByTaskId(processTingwuResult);
                }
            }
            return JSONObject.parseObject(response.getData());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

}