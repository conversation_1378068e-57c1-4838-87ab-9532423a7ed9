package com.ruoyi.platform.model.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.model.domain.PlatformMachineCode;
import com.ruoyi.platform.model.mapper.PlatformMachineCodeMapper;
import com.ruoyi.platform.model.service.IPlatformMachineCodeService;

/**
 * 机器管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-20
 */
@Service
public class PlatformMachineCodeServiceImpl implements IPlatformMachineCodeService 
{
    @Autowired
    private PlatformMachineCodeMapper platformMachineCodeMapper;

    /**
     * 查询机器管理
     * 
     * @param machineCodeId 机器管理主键
     * @return 机器管理
     */
    @Override
    public PlatformMachineCode selectPlatformMachineCodeByMachineCodeId(Long machineCodeId)
    {
        return platformMachineCodeMapper.selectPlatformMachineCodeByMachineCodeId(machineCodeId);
    }

    /**
     * 查询机器管理列表
     * 
     * @param platformMachineCode 机器管理
     * @return 机器管理
     */
    @Override
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformMachineCode> selectPlatformMachineCodeList(PlatformMachineCode platformMachineCode)
    {
        return platformMachineCodeMapper.selectPlatformMachineCodeList(platformMachineCode);
    }

    /**
     * 新增机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    @Override
    public int insertPlatformMachineCode(PlatformMachineCode platformMachineCode)
    {
        platformMachineCode.setMachineCodeStatus(1L);
        platformMachineCode.setCreateTime(DateUtils.getNowDate());
        return platformMachineCodeMapper.insertPlatformMachineCode(platformMachineCode);
    }

    /**
     * 修改机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    @Override
    public int updatePlatformMachineCode(PlatformMachineCode platformMachineCode)
    {
        platformMachineCode.setUpdateTime(DateUtils.getNowDate());
        return platformMachineCodeMapper.updatePlatformMachineCode(platformMachineCode);
    }

    /**
     * 批量删除机器管理
     * 
     * @param machineCodeIds 需要删除的机器管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformMachineCodeByMachineCodeIds(Long[] machineCodeIds)
    {
        return platformMachineCodeMapper.deletePlatformMachineCodeByMachineCodeIds(machineCodeIds);
    }

    /**
     * 删除机器管理信息
     * 
     * @param machineCodeId 机器管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformMachineCodeByMachineCodeId(Long machineCodeId)
    {
        return platformMachineCodeMapper.deletePlatformMachineCodeByMachineCodeId(machineCodeId);
    }
}
