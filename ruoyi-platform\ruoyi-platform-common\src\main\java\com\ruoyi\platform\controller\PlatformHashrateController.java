package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformHashrate;
import com.ruoyi.platform.service.IPlatformHashrateService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用户算力点数Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@RestController
@RequestMapping("/platform/hashrate")
@Tag(name = "【算力点】管理")
public class PlatformHashrateController extends BaseController
{
    @Autowired
    private IPlatformHashrateService platformHashrateService;

    /**
     * 查询用户算力点数列表
     */
    @Operation(summary = "查询用户算力点数列表")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlatformHashrate platformHashrate)
    {
        startPage();
        List<PlatformHashrate> list = platformHashrateService.selectPlatformHashrateList(platformHashrate);
        return getDataTable(list);
    }

    /**
     * 导出用户算力点数列表
     */
    @Operation(summary = "导出用户算力点数列表")
    @PreAuthorize("@ss.hasPermi('platform:hashrate:export')")
    @Log(title = "导出用户算力点数列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformHashrate platformHashrate)
    {
        List<PlatformHashrate> list = platformHashrateService.selectPlatformHashrateList(platformHashrate);
        ExcelUtil<PlatformHashrate> util = new ExcelUtil<PlatformHashrate>(PlatformHashrate.class);
        util.exportExcel(response, list, "用户算力点数数据");
    }

    /**
     * 获取用户算力点数详细信息
     */
    @Operation(summary = "获取用户算力点数详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:query')")
    @GetMapping(value = "/{hashrateId}")
    public AjaxResult getInfo(@PathVariable("hashrateId") Long hashrateId)
    {
        return success(platformHashrateService.selectPlatformHashrateByHashrateId(hashrateId));
    }

    /**
     * 新增用户算力点数
     */
    @Operation(summary = "新增用户算力点数")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:add')")
    @Log(title = "新增用户算力点数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlatformHashrate platformHashrate)
    {
        platformHashrate.setCreateBy(getUsername());
        platformHashrate.setUpdateBy(getUsername());
        return toAjax(platformHashrateService.insertPlatformHashrate(platformHashrate));
    }

    /**
     * 修改用户算力点数
     */
    @Operation(summary = "修改用户算力点数")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:edit')")
    @Log(title = "修改用户算力点数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlatformHashrate platformHashrate) {
        platformHashrate.setUpdateBy(getUsername()); //更新人
        return toAjax(platformHashrateService.updatePlatformHashrate(platformHashrate));
    }

    /**
     * 批量删除用户算力点数
     */
    @Operation(summary = "批量删除用户算力点数")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:remove')")
    @Log(title = "批量删除用户算力点数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{hashrateIds}")
    public AjaxResult remove(@PathVariable( name = "hashrateIds" ) Long[] hashrateIds) 
    {
        return toAjax(platformHashrateService.deletePlatformHashrateByHashrateIds(hashrateIds));
    }

    /**
     * 充值客户算力点数
     */
    @Operation(summary = "充值客户算力点数")
    //@PreAuthorize("@ss.hasPermi('platform:hashrate:top')")
    @Log(title = "充值客户算力点数", businessType = BusinessType.UPDATE)
    //@MessageLog(description = "通知管理员，用户已充值算力点",title = "充值算力点", messageType = MessageType.INFO,immediateLog = true)
    @PostMapping("/top")
    public AjaxResult top(@RequestBody PlatformHashrate platformHashrate) {
        return toAjax(platformHashrateService.consumptionPlatformHashrate(platformHashrate));
    }

    /**
     * 批量强制删除算力用户
     */
    @Operation(summary = "批量强制删除算力用户")
    @Log(title = "批量强制删除算力用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByUserName/{userNames}")
    public AjaxResult deleteByUserName(@PathVariable( name = "userNames" ) String[] userNames) {
        return toAjax(platformHashrateService.deleteByUserNames(userNames));
    }

    //初始化用户
    @Operation(summary = "初始化用户")
    @Log(title = "初始化用户", businessType = BusinessType.INSERT)
    @PostMapping("/initializeUserHashrate/{userName}")
    public AjaxResult initializeUserHashrate(@PathVariable( name = "userName" ) String userName) {
        return success(platformHashrateService.initializeUserHashrate(userName));
    }

    /**
     * 批量生成算力卡号并导出
     *
     * @param count   卡号数量
     * @param balance 每张卡的面额
     * @param response HttpServletResponse对象，用于导出文件
     */
    @Operation(summary = "批量生成算力卡号并导出")
    //@Log(title = "批量生成算力卡号并导出", businessType = BusinessType.INSERT)
    @PostMapping("/generateAndExport/{count}/{balance}")
    public AjaxResult generateAndExport(@PathVariable int count, @PathVariable String balance,HttpServletResponse response) {
        try {
            List<PlatformHashrate> cards = platformHashrateService.generateCards(count, balance);
            ExcelUtil<PlatformHashrate> util = new ExcelUtil<>(PlatformHashrate.class);
            util.exportExcel(response, cards, "用户算力点数数据");
            return AjaxResult.success("批量生成并导出算力卡号成功");
        } catch (Exception e) {
            return AjaxResult.error("批量生成并导出算力卡号失败，请稍后重试");
        }
    }

    /**
     * 激活卡号并且充值算力点
     *
     * @param hashrateId 卡号的ID
     * @param userName   使用者用户名
     * @return 操作结果
     */
    @Operation(summary = "激活卡号并且充值算力点")
    @Log(title = "激活卡号并且充值算力点", businessType = BusinessType.UPDATE)
    @PostMapping("/useCard/{cardNumber}")
    public AjaxResult useCard(@PathVariable String cardNumber) {
        try {
            Object result = platformHashrateService.useCard(cardNumber);
            return AjaxResult.success("卡号已激活", result);
        } catch (Exception e) {
            return AjaxResult.error("激活并绑定卡号失败：" + e.getMessage());
        }
    }
}
