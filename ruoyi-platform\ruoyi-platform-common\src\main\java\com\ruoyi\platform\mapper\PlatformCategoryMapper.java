package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformCategory;

/**
 * 分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface PlatformCategoryMapper {
    /**
     * 查询分类
     * 
     * @param categoryId 分类主键
     * @return 分类
     */
    public PlatformCategory selectPlatformCategoryByCategoryId(Long categoryId);

    /**
     * 查询分类列表
     * 
     * @param platformCategory 分类
     * @return 分类集合
     */
    public List<PlatformCategory> selectPlatformCategoryList(PlatformCategory platformCategory);

    /**
     * 新增分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    public int insertPlatformCategory(PlatformCategory platformCategory);

    /**
     * 修改分类
     * 
     * @param platformCategory 分类
     * @return 结果
     */
    public int updatePlatformCategory(PlatformCategory platformCategory);

    /**
     * 删除分类
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    public int deletePlatformCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 检查当前分类是否还有文案
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    @Select("select count(article_id) from platform_article where category_id = #{categoryId}")
    public int checkProjectHasCategory(Long categoryId);

    /**
     * 检查当前分类是否还有关键字
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    @Select("select count(keyword_id) from platform_keyword where category_id = #{categoryId}")
    public int checkProjectKeyWord(Long categoryId);

    /**
     * 检查当前分类是否还有音频数据
     * 
     * @param categoryId 分类主键
     * @return 结果
     */
    @Select("select count(audio_id) from platform_audio where category_id = #{categoryId}")
    public int checkProjectAudio(Long categoryId);

    //根据分类ID查询多个数据
    public List<PlatformCategory> getCategoriesByIds(int[] ids);

    //根据项目ID查询数据
    @Select("select * from platform_category where project_id=#{projectId}")
    public List<PlatformCategory> selectCategoryListByProjectId(Long projectId);

    /**
     * 根据直播ID查询分类信息
     */
    public List<PlatformCategory> getCategoriesByLiveId(Long liveId);
}
