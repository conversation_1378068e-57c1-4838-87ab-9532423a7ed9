package com.ruoyi.video.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.service.IVideoEditService;

/**
 * Coze工作流控制器
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/videoEdit")
public class VideoEditController {

    @Autowired
    private IVideoEditService videoEditService;

    /**
     * 列出公共素材库媒资基础信息
     * 
     * @param MediaTagId           媒资标签
     * @param NextToken            下一页目标点
     * @param MaxResults           本次请求所返回的最大记录条数
     * @param PageNO               当前页码 默认1
     * @param PageSize             分页大小 默认10
     * @param IncludeFileBasicInfo 是否返回文件基础信息
     * @param BusinessType         媒资业务类型 贴纸 / 背景音乐 / 背景图片
     * @return AjaxResult 公共媒资基础信息
     * <AUTHOR>
     * @date 2025-07-17
     */
    @RequestMapping("/listPublicMediaBasicInfos")
    public AjaxResult ListPublicMediaBasicInfos(
            @RequestParam(required = false) String MediaTagId,
            @RequestParam(required = false) String NextToken,
            @RequestParam(required = false) Integer MaxResults,
            @RequestParam(required = false) Integer PageNO,
            @RequestParam(required = false) Integer PageSize,
            @RequestParam(required = false) Boolean IncludeFileBasicInfo,
            @RequestParam(required = false) String BusinessType) {
        try {
            String result = videoEditService.ListPublicMediaBasicInfos(MediaTagId, NextToken, MaxResults, PageNO,
                    PageSize, IncludeFileBasicInfo, BusinessType);
            return AjaxResult.success("列出公共素材库媒资基础信息成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("列出公共素材库媒资基础信息失败", e);
        }
    }

    /**
     * 列出公共素材库所有标签
     * 
     * @param BusinessType 媒资业务类型
     * @param EntityId     实体ID
     * @return AjaxResult 媒资业务标签
     * <AUTHOR>
     * @date 2025-07-17
     */
    @RequestMapping("/listAllPublicMediaTags")
    public AjaxResult ListAllPublicMediaTags(
            @RequestParam(required = false) String BusinessType,
            @RequestParam(required = false) String EntityId) {
        try {
            String result = videoEditService.ListAllPublicMediaTags(BusinessType, EntityId);
            return AjaxResult.success("列出公共素材库所有标签成功", result);
        } catch (Exception e) {
            return AjaxResult.error("列出公共素材库所有标签失败", e);
        }
    }
}