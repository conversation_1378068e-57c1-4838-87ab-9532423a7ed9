package com.ruoyi.platform.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformAnchor;

/**
 * 智能主播Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface PlatformAnchorMapper 
{
    /**
     * 查询智能主播
     * 
     * @param anchorId 智能主播主键
     * @return 智能主播
     */
    public PlatformAnchor selectPlatformAnchorByAnchorId(Long anchorId);

    /**
     * 查询智能主播列表
     * 
     * @param platformAnchor 智能主播
     * @return 智能主播集合
     */
    public List<PlatformAnchor> selectPlatformAnchorList(PlatformAnchor platformAnchor);

    /**
     * 新增智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    public int insertPlatformAnchor(PlatformAnchor platformAnchor);

    /**
     * 修改智能主播
     * 
     * @param platformAnchor 智能主播
     * @return 结果
     */
    public int updatePlatformAnchor(PlatformAnchor platformAnchor);

    /**
     * 删除智能主播
     * 
     * @param anchorId 智能主播主键
     * @return 结果
     */
    public int deletePlatformAnchorByAnchorId(Long anchorId);

    /**
     * 批量删除智能主播
     * 
     * @param anchorIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformAnchorByAnchorIds(Long[] anchorIds);

    /**
     * 根据项目ID查询智能主播ID
     * 
     * @param projectId 项目ID
     * @return 智能主播ID
     */
    public Long selectAnchorIdByProjectId(Long projectId);

    //根据项目ID 查询数据
    @Select("select * from platform_anchor where project_id = #{projectId}")
    public PlatformAnchor selectPlatformAnchorByProjectId(Long projectId);

    //根据项目id进行强制删除智能主播数据
    @Delete("delete from platform_anchor where project_id = #{projectId}")
    public int deleteConstraintAnchorProjectId(Long projectId);

    /**
     * 根据直播ID查询智能主播列表
     *
     * @param liveId 直播ID
     * @return 智能主播列表
     */
    public List<PlatformAnchor> getAnchorsByLiveId(Long liveId);
}
