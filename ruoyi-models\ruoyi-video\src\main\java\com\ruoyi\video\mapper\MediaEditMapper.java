package com.ruoyi.video.mapper;

import java.util.List;

import com.ruoyi.video.domain.MediaEdit;

/**
 * 媒体编辑表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface MediaEditMapper
{
    /**
     * 查询媒体编辑表
     *
     * @param jobId 媒体编辑表主键
     * @return 媒体编辑表
     */
    public MediaEdit selectMediaEditByJobId(String jobId);

    /**
     * 查询媒体编辑表列表
     *
     * @param mediaEdit 媒体编辑表
     * @return 媒体编辑表集合
     */
    public List<MediaEdit> selectMediaEditList(MediaEdit mediaEdit);

    /**
     * 新增媒体编辑表
     *
     * @param mediaEdit 媒体编辑表
     * @return 结果
     */
    public int insertMediaEdit(MediaEdit mediaEdit);

    /**
     * 修改媒体编辑表
     *
     * @param mediaEdit 媒体编辑表
     * @return 结果
     */
    public int updateMediaEdit(MediaEdit mediaEdit);

    /**
     * 删除媒体编辑表
     *
     * @param jobId 媒体编辑表主键
     * @return 结果
     */
    public int deleteMediaEditByJobId(String jobId);

    /**
     * 批量删除媒体编辑表
     *
     * @param jobIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMediaEditByJobIds(String[] jobIds);

    /**
     * 根据项目ID批量删除媒体编辑表
     *
     * @param projectIds 需要删除的项目ID集合
     * @return 结果
     */
    public int deleteMediaEditByProjectIds(String[] projectIds);
}
