package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 脚本对象 sys_script
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@Schema(description = "脚本对象")
public class SysScript extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @Schema(title = "主键")
    private Long scriptId;

    /** 标题 */
    @Schema(title = "标题")
    @Excel(name = "标题")
    private String scriptTitle;

    /** 内容 */
    @Schema(title = "内容")
    private String content;
    public void setScriptId(Long scriptId) 
    {
        this.scriptId = scriptId;
    }

    public Long getScriptId() 
    {
        return scriptId;
    }


    public void setScriptTitle(String scriptTitle) 
    {
        this.scriptTitle = scriptTitle;
    }

    public String getScriptTitle() 
    {
        return scriptTitle;
    }


    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scriptId", getScriptId())
            .append("scriptTitle", getScriptTitle())
            .append("content", getContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
