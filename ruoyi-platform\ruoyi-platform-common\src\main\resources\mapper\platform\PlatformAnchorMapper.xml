<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformAnchorMapper">
    
    <resultMap type="PlatformAnchor" id="PlatformAnchorResult">
        <result property="anchorId"    column="anchor_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="anchorName"    column="anchor_name"    />
        <result property="anchorRepository"    column="anchor_repository"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlatformAnchorVo">
        select anchor_id, project_id, anchor_name, anchor_repository, create_by, create_time, update_by, update_time, remark from platform_anchor
    </sql>

    <select id="selectPlatformAnchorList" parameterType="PlatformAnchor" resultMap="PlatformAnchorResult">
        <include refid="selectPlatformAnchorVo"/>
        <where>  
            <if test="anchorName != null  and anchorName != ''"> and anchor_name like concat('%', #{anchorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectPlatformAnchorByAnchorId" parameterType="Long" resultMap="PlatformAnchorResult">
        <include refid="selectPlatformAnchorVo"/>
        where platform_anchor.anchor_id = #{anchorId}
    </select>
        
    <insert id="insertPlatformAnchor" parameterType="PlatformAnchor" useGeneratedKeys="true" keyProperty="anchorId">
        insert into platform_anchor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="anchorName != null">anchor_name,</if>
            <if test="anchorRepository != null">anchor_repository,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="anchorName != null">#{anchorName},</if>
            <if test="anchorRepository != null">#{anchorRepository},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatformAnchor" parameterType="PlatformAnchor">
        update platform_anchor
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="anchorName != null">anchor_name = #{anchorName},</if>
            <if test="anchorRepository != null">anchor_repository = #{anchorRepository},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where platform_anchor.anchor_id = #{anchorId}
    </update>

    <delete id="deletePlatformAnchorByAnchorId" parameterType="Long">
        delete from platform_anchor where anchor_id = #{anchorId}
    </delete>

    <delete id="deletePlatformAnchorByAnchorIds" parameterType="String">
        delete from platform_anchor where anchor_id in 
        <foreach item="anchorId" collection="array" open="(" separator="," close=")">
            #{anchorId}
        </foreach>
    </delete>

    <!-- 根据项目Id查询智能主播数据 --> 
    <select id="selectAnchorIdByProjectId" parameterType="Long">
        SELECT anchor_id FROM platform_anchor WHERE project_id = #{projectId}
    </select>

    <select id="getAnchorsByLiveId" parameterType="Long" resultMap="PlatformAnchorResult">
        SELECT a.* FROM platform_anchor a WHERE a.project_id = (
            SELECT project_id FROM platform_live WHERE live_id = #{liveId})
    </select>
</mapper>