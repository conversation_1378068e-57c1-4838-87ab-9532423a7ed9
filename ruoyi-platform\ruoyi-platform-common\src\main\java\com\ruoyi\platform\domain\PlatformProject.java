package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * 项目对象 platform_project
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Schema(description = "项目对象")
public class PlatformProject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long projectId;

    /** 项目名称 */
    @Schema(title = "项目名称")
    @Excel(name = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String projectTitle;

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }


    public void setProjectTitle(String projectTitle) 
    {
        this.projectTitle = projectTitle;
    }

    public String getProjectTitle() 
    {
        return projectTitle;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("projectId", getProjectId())
            .append("projectTitle", getProjectTitle())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
