package com.ruoyi.video.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 模板操作的请求数据传输对象 (DTO)
 * <p>
 * 用于封装调用阿里云智能媒体服务 (ICE) 模板相关接口（如 AddTemplate, UpdateTemplate, DeleteTemplate）所需的所有参数。
 * 具体字段的必填性和用途取决于调用的具体接口。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class TemplateRequestDTO {

    /**
     * 模板 ID。
     * <p>
     * 对于 UpdateTemplate 和 GetTemplate 接口，此字段是必选。
     * </p>
     */
    @JsonProperty("TemplateId")
    private String templateId;

    /**
     * 需要删除的模板 ID，多个 ID 用半角逗号（,）隔开。
     * <p>
     * 对于 DeleteTemplate 接口，此字段是必选。
     * </p>
     */
    @JsonProperty("TemplateIds")
    private String templateIds;

    /**
     * 自定义模板名称。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("Name")
    private String name;

    /**
     * 模板类型。
     * <p>
     * 目前支持：Timeline（普通模板）和 VETemplate（高级模板）。
     * 对于 AddTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("Type")
    private String type;

    /**
     * 模板配置。基于云剪辑 Timeline 的封装。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("Config")
    private String config;

    /**
     * 模板封面 URL。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("CoverUrl")
    private String coverUrl;

    /**
     * 模板预览视频 MediaId。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("PreviewMedia")
    private String previewMedia;

    /**
     * 模板状态。取值范围：Available, Created, Uploading, Processing, UploadFailed, ProcessFailed。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("Status")
    private String status;

    /**
     * 模板创建来源。取值范围：AliyunConsole, OpenAPI, WebSDK。
     * <p>
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("Source")
    private String source;

    /**
     * 模板关联素材，普通模板编辑器使用。JSON 格式字符串。
     * <p>
     * e.g. {"video":["1805a0c6ca544fb395a06ca683619655"]}
     * 对于 AddTemplate 和 UpdateTemplate 接口，此字段是可选。
     * </p>
     */
    @JsonProperty("RelatedMediaids")
    private String relatedMediaids;
} 