package com.ruoyi.platform.model.config;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alibaba.nls.client.AccessToken;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.ruoyi.common.utils.CacheUtils;

import lombok.Data;

/**
 * 阿里云语音合成配置类 - 整合Token管理功能
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.voice")
public class AliyunConfig {
    
    private static final Logger log = LoggerFactory.getLogger(AliyunConfig.class);
    private static final String CACHE_NAME = "aliyunNlsToken";
    private static final String TOKEN_KEY = "nlsAccessToken";
    
    private String accessKeyId;
    private String accessKeySecret;
    private String regionId;
    private String appKey;
    private String nlsEndpoint = "nls-gateway.cn-shanghai.aliyuncs.com"; // 默认值

    /**
     * 创建通用阿里云客户端实例
     */
    @Bean(name = "aliyunAcsClient")
    public IAcsClient acsClient() {
        DefaultProfile profile = DefaultProfile.getProfile(
            regionId, 
            accessKeyId, 
            accessKeySecret
        );
        return new DefaultAcsClient(profile);
    }
    
    /**
     * 获取语音合成服务端点
     */
    @Bean
    public String nlsEndpoint() {
        return nlsEndpoint;
    }
    
    /**
     * 获取有效的NLS服务Token
     */
    @Bean
    public String nlsAccessToken() {
        // 先从缓存获取
        String cachedToken = CacheUtils.get(CACHE_NAME, TOKEN_KEY, String.class);
        if (cachedToken != null) {
            log.info("从缓存获取NLS Token成功");
            return cachedToken;
        }
        
        // 缓存中没有，重新获取
        return refreshAndCacheToken();
    }
    
    /**
     * 刷新并缓存Token
     */
    private synchronized String refreshAndCacheToken() {
        // 再次检查缓存，避免并发请求重复获取
        String cachedToken = CacheUtils.get(CACHE_NAME, TOKEN_KEY, String.class);
        if (cachedToken != null) {
            return cachedToken;
        }
        
        try {
            // 创建并申请Token
            AccessToken accessToken = new AccessToken(accessKeyId, accessKeySecret);
            accessToken.apply();
            
            String newToken = accessToken.getToken();
            long expireTime = accessToken.getExpireTime();
            long currentTime = System.currentTimeMillis();
            long validSeconds = (expireTime - currentTime) / 1000;
            
            // 提前5分钟过期，确保使用时Token有效
            long cacheSeconds = Math.max(60, validSeconds - 300);
            
            // 使用CacheUtils缓存Token
            CacheUtils.put(CACHE_NAME, TOKEN_KEY, newToken, cacheSeconds, TimeUnit.SECONDS);
            log.info("获取并缓存新的NLS Token成功，有效期: {}秒", cacheSeconds);
            
            return newToken;
        } catch (IOException e) {
            log.error("获取NLS Token失败", e); 
            throw new RuntimeException("获取阿里云NLS服务Token失败", e);
        }
    }
}