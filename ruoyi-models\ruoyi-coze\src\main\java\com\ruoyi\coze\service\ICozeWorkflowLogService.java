package com.ruoyi.coze.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.coze.domain.CozeWorkflowLog;

/**
 * CozeWorkflowLogService接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ICozeWorkflowLogService 
{
    /**
     * 查询CozeWorkflowLog
     * 
     * @param id CozeWorkflowLog主键
     * @return CozeWorkflowLog
     */
    public CozeWorkflowLog selectCozeWorkflowLogById(Long id);

    /**
     * 查询CozeWorkflowLog列表
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return CozeWorkflowLog集合
     */
    public List<CozeWorkflowLog> selectCozeWorkflowLogList(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 新增CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    public int insertCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 修改CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    public int updateCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 批量删除CozeWorkflowLog
     * 
     * @param ids 需要删除的CozeWorkflowLog主键集合
     * @return 结果
     */
    public int deleteCozeWorkflowLogByIds(Long[] ids);

    /**
     * 删除CozeWorkflowLog信息
     * 
     * @param id CozeWorkflowLog主键
     * @return 结果
     */
    public int deleteCozeWorkflowLogById(Long id);

    /**
     * 保存工作流日志-输入
     * @param workflowId
     * @param executeId
     * @param request
     * @return
     */
    public Long saveLogWithInput(String workflowId, String executeId, Object request);

    /**
     * 保存工作流日志-输出
     * @param workflowId
     * @param executeId
     * @param result
     * @return
     */
    public int saveLogWithOutput(String workflowId, String executeId, Map<String,Object> result);

    /**
     * 根据ID更新工作流日志输出
     * @param id
     * @param result
     * @return
     */
    public int saveLogWithOutputById(Long id, Map<String, Object> result);
}
