package com.ruoyi.tingwu.service.impl;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.profile.DefaultProfile;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.tingwu.domain.TingwuPhrase;
import com.ruoyi.tingwu.mapper.TingwuPhraseMapper;
import com.ruoyi.tingwu.service.ITingwuPhraseService;
import com.ruoyi.tingwu.utils.RequestUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 词表信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TingwuPhraseServiceImpl implements ITingwuPhraseService {

    private final TingwuPhraseMapper tingwuPhraseMapper;

    private final DefaultProfile defaultProfile;

    /**
     * 查询词表信息
     *
     * @param keyId 词表信息主键
     * @return 词表信息
     */

    @Override
    public JSONArray selectTingwuPhraseByPhraseId(String phraseId) {
        try {
            CommonRequest request = RequestUtils.phraseDetailRequest(phraseId);

            IAcsClient client = new DefaultAcsClient(defaultProfile);
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            JSONObject body = JSONObject.parseObject(response.getData());
            JSONObject data = (JSONObject) body.get("Data");
            JSONArray phrases = data.getJSONArray("Phrases");
            return phrases;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询词表信息列表
     *
     * @param tingwuPhrase 词表信息
     * @return 词表信息
     */
    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<TingwuPhrase> selectTingwuPhraseList(TingwuPhrase tingwuPhrase) {
        return tingwuPhraseMapper.selectTingwuPhraseList(tingwuPhrase);
    }

    /**
     * 新增词表信息
     *
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    @Override
    public JSONObject insertTingwuPhrase(TingwuPhrase tingwuPhrase) {
        try {
            // 创建请求
            CommonRequest request = RequestUtils.createPhraseRequest();

            // 创建内容
            JSONObject root = new JSONObject();
            JSONObject wordWeights = new JSONObject();

            root.put("Name", tingwuPhrase.getName());
            root.put("Description", tingwuPhrase.getDescription());

            Map<String, Integer> weightMap = JSON.parseObject(
                    tingwuPhrase.getWordWeights(),
                    new TypeReference<Map<String, Integer>>() {
                    });
            // 将weightMap的所有数据添加到wordWeights
            weightMap.forEach(wordWeights::put);
            root.put("WordWeights", wordWeights);
            log.info("root: {}", root);

            request.setHttpContent(root.toJSONString().getBytes(StandardCharsets.UTF_8), "utf-8", FormatType.JSON);

            IAcsClient client = new DefaultAcsClient(defaultProfile);

            CommonResponse response = client.getCommonResponse(request);
            log.info("response: {}", response);

            JSONObject body = JSONObject.parseObject(response.getData());
            JSONObject data = (JSONObject) body.get("Data");
            String PhraseId = data.getString("PhraseId");
            // 将数据添加到数据库中，以便区分不同的用户
            log.info("data: {}", data);
            log.info("tingwuPhrase: {}", tingwuPhrase);
            tingwuPhrase.setPhraseId(PhraseId);
            tingwuPhrase.setCreateBy(SecurityUtils.getUsername());
            tingwuPhrase.setCreateTime(DateUtils.getNowDate());
            tingwuPhraseMapper.insertTingwuPhrase(tingwuPhrase);

            return data;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 修改词表信息
     *
     * @param tingwuPhrase 词表信息
     * @return 结果
     */
    @Override
    public JSONObject updateTingwuPhrase(TingwuPhrase tingwuPhrase) {
        try {
            String phraseId = tingwuPhrase.getPhraseId();
            // 创建请求
            CommonRequest request = RequestUtils.updatePhraseRequest(phraseId);

            // 创建内容
            JSONObject root = new JSONObject();
            JSONObject wordWeights = new JSONObject();

            root.put("Name", tingwuPhrase.getName());
            root.put("Description", tingwuPhrase.getDescription());

            Map<String, Integer> weightMap = JSON.parseObject(
                    tingwuPhrase.getWordWeights(),
                    new TypeReference<Map<String, Integer>>() {
                    });
            // 将weightMap的所有数据添加到wordWeights
            weightMap.forEach(wordWeights::put);
            root.put("WordWeights", wordWeights);

            request.setHttpContent(root.toJSONString().getBytes(StandardCharsets.UTF_8), "utf-8", FormatType.JSON);

            IAcsClient client = new DefaultAcsClient(defaultProfile);
            CommonResponse response = client.getCommonResponse(request);
            log.info("response: {}", response);

            JSONObject body = JSONObject.parseObject(response.getData());
            JSONObject data = (JSONObject) body.get("Data");

            // 更新数据库中的数据
            tingwuPhraseMapper.updateTingwuPhrase(tingwuPhrase);

            return data;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 删除词表信息信息
     *
     * @param phraseId 词表信息主键
     * @return 结果
     */
    @Override
    public JSONObject deleteTingwuPhraseByPhraseIdIds(String[] phraseIds) {
        try {
            for (String phraseId : phraseIds) {
                CommonRequest request = RequestUtils.delPhraseRequest(phraseId);

                IAcsClient client = new DefaultAcsClient(defaultProfile);
                CommonResponse response = client.getCommonResponse(request);

                log.info("response: {}", response);

                JSONObject body = JSONObject.parseObject(response.getData());
                JSONObject data = (JSONObject) body.get("Data");
            }
            tingwuPhraseMapper.deleteTingwuPhraseByPhraseIdIds(phraseIds);
            return JSONObject.parseObject("{}");

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
