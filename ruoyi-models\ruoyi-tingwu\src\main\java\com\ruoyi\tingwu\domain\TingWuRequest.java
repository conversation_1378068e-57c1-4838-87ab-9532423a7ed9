package com.ruoyi.tingwu.domain;

import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TingWuRequest 音视频请求参数
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "音视频请求参数")
public class TingWuRequest extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 音视频语言：0-中文，1-英语，2-日语，3-粤语，4-中英文自由说 */
    @Schema(title = "音视频语言")
    private String language;

    /** 翻译选项：0-不翻译，1-英语，2-日语 */
    @Schema(title = "翻译")
    private String translate;

    /** 区分发言人类型：0-暂不体验，1-单人演讲，2-2人对话，3-多人讨论 */
    @Schema(title = "区分发言人")
    private String speakerType;

    /** 热词编号 */
    @Schema(title = "热词编号")
    private String phraseId;

}
