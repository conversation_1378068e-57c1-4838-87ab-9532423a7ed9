<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-plugins</artifactId>

    <properties>
        <mybatis-plus-boot-starter.version>3.5.7</mybatis-plus-boot-starter.version>
    </properties>

    <description>
        ruoyi-plugins模块
    </description>
    <dependencyManagement>
        <dependencies>

            <!-- mybatis-plus 增强CRUD -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>

            <!-- plugins-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-plugins-starter</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 使用mybatis-jpa jar包 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-mybatis-jpa</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <modules>
        <module>ruoyi-plugins-starter</module>
        <module>ruoyi-mybatis-jpa</module>
    </modules>
    <packaging>pom</packaging>
    
</project>