package com.ruoyi.platform.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.platform.domain.PlatformConsumption;
import com.ruoyi.platform.domain.vo.PlatformConsumptionVo;
import com.ruoyi.platform.mapper.PlatformConsumptionMapper;
import com.ruoyi.platform.service.IPlatformConsumptionService;

/**
 * 用户算力点数变化记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class PlatformConsumptionServiceImpl implements IPlatformConsumptionService 
{
    @Autowired
    private PlatformConsumptionMapper platformConsumptionMapper;

    /**
     * 查询用户算力点数变化记录
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 用户算力点数变化记录
     */
    @Override
    public PlatformConsumption selectPlatformConsumptionByConsumptionId(Long consumptionId)
    {
        return platformConsumptionMapper.selectPlatformConsumptionByConsumptionId(consumptionId);
    }

    /**
     * 查询用户算力点数变化记录列表
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 用户算力点数变化记录
     */
    @Override
    public List<PlatformConsumption> selectPlatformConsumptionList(PlatformConsumption platformConsumption)
    {
        return platformConsumptionMapper.selectPlatformConsumptionList(platformConsumption);
    }

    /**
     * 新增用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    @Override
    public int insertPlatformConsumption(PlatformConsumption platformConsumption)
    {
        platformConsumption.setCreateTime(DateUtils.getNowDate());
        return platformConsumptionMapper.insertPlatformConsumption(platformConsumption);
    }

    /**
     * 修改用户算力点数变化记录
     * 
     * @param platformConsumption 用户算力点数变化记录
     * @return 结果
     */
    @Override
    public int updatePlatformConsumption(PlatformConsumption platformConsumption)
    {
        platformConsumption.setUpdateTime(DateUtils.getNowDate());
        return platformConsumptionMapper.updatePlatformConsumption(platformConsumption);
    }

    /**
     * 批量删除用户算力点数变化记录
     * 
     * @param consumptionIds 需要删除的用户算力点数变化记录主键
     * @return 结果
     */
    @Override
    public int deletePlatformConsumptionByConsumptionIds(Long[] consumptionIds)
    {
        return platformConsumptionMapper.deletePlatformConsumptionByConsumptionIds(consumptionIds);
    }

    /**
     * 删除用户算力点数变化记录信息
     * 
     * @param consumptionId 用户算力点数变化记录主键
     * @return 结果
     */
    @Override
    public int deletePlatformConsumptionByConsumptionId(Long consumptionId)
    {
        return platformConsumptionMapper.deletePlatformConsumptionByConsumptionId(consumptionId);
    }

    /**
     * 查询并汇总某个用户在过去一年内每个业务模块的算力消费情况。
     *
     * @param userName 用户名称
     * @return 用户在各业务模块上的算力消费汇总及占比
     */
    @Override
    public List<PlatformConsumptionVo> summarizeUserConsumption(String userName) {
        Calendar calendar =  Calendar.getInstance();
        calendar.setTime(new Date());
        // 计算过去一年的时间点
        calendar.add(Calendar.YEAR, -1);
        Date oneYearAgo = calendar.getTime();
        return platformConsumptionMapper.selectConsumptionSummary(userName, oneYearAgo, new Date());
    }

    /**
     * 获取算力消费统计数据
     * 
     * @return 统计数据，包含总消费量、日消费量、月消费量等信息
     */
    @Override
    public Map<String, Object> getConsumptionStatistics() {
        return platformConsumptionMapper.selectConsumptionStatistics();
    }
}
