<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.model.mapper.PlatformSoundMapper">
    <resultMap type="PlatformSound" id="PlatformSoundResult">
        <result property="soundId" column="sound_id"/>
        <result property="soundName" column="sound_name"/>
        <result property="soundStatus" column="sound_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="soundTrain" column="sound_train"/>
        <result property="soundRef" column="sound_ref"/>
        <result property="remark" column="remark"/>
        <result property="soundGpt" column="sound_gpt"/>
        <result property="soundSovits" column="sound_sovits"/>
        <result property="soundRefText" column="sound_ref_text"/>
        <result property="deptId"    column="dept_id" />
        <result property="soundFiltration"    column="sound_filtration" />

    </resultMap>
    <sql id="selectPlatformSoundVo"> 
        SELECT s.sound_id, s.sound_name, s.sound_status, s.create_by, s.create_time, s.update_by, s.update_time, s.sound_train, 
        s.sound_ref, s.remark, s.sound_gpt, s.sound_sovits, s.sound_ref_text, s.dept_id, d.dept_name, s.sound_filtration 
        FROM platform_sound s LEFT JOIN sys_dept d ON s.dept_id = d.dept_id LEFT JOIN sys_user u ON s.create_by = u.user_name
    </sql>
    
    <select id="selectPlatformSoundList" parameterType="PlatformSound" resultMap="PlatformSoundResult">
        <include refid="selectPlatformSoundVo"/>
        <where>
            <if test="soundName != null and soundName != ''"> and sound_name like concat('%', #{soundName}, '%')</if>
            <if test="soundStatus != null and soundStatus != ''"> and sound_status = #{soundStatus}</if>
            <if test="soundFiltration != null and soundFiltration != ''"> and s.sound_filtration = #{soundFiltration}</if>
            <trim prefix="AND (" prefixOverrides="AND | OR " suffix=")">
                <if test="params.dataScopeEnabled == true">
                    <choose>
                        <when test="params.deptId == 103">
                            (s.dept_id IN (
                                SELECT dept_id FROM sys_dept WHERE dept_id = 103 
                                OR find_in_set(103, ancestors) UNION
                                SELECT dept_id FROM sys_dept WHERE find_in_set(dept_id, (SELECT ancestors FROM sys_dept WHERE dept_id = 103))
                            ) AND s.sound_filtration = 1 OR s.sound_filtration = 0)
                        </when>
                        
                        <otherwise>
                            (s.dept_id IN (
                                SELECT dept_id FROM sys_dept WHERE dept_id = #{params.deptId} 
                                OR find_in_set(#{params.deptId}, ancestors) UNION
                                SELECT dept_id FROM sys_dept WHERE find_in_set(dept_id, (SELECT ancestors FROM sys_dept WHERE dept_id = #{params.deptId}))
                            ) AND s.sound_filtration = 1)
                            OR (s.dept_id = #{params.deptId} AND s.sound_filtration = 0 and s.create_by = #{params.currentUserName})
                        </otherwise>
                    </choose>
                </if>
                <if test="params.dataScope != null and params.dataScope != ''">
                    <trim prefix="OR (" prefixOverrides="AND | OR " suffix=")"> 
                        ${params.dataScope} 
                    </trim>
                </if>
            </trim>
        </where>
        order by create_time desc
    </select>

    <select id="selectPlatformSoundBySoundId" parameterType="Long" resultMap="PlatformSoundResult">
    <include refid="selectPlatformSoundVo"/>
        where sound_id = #{soundId}
    </select>

    <insert id="insertPlatformSound" parameterType="PlatformSound" useGeneratedKeys="true" keyProperty="soundId">
    insert into platform_sound
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="soundName != null">sound_name,</if>
        <if test="soundStatus != null">sound_status,</if>
        <if test="createBy != null">create_by,</if>
        <if test="createTime != null">create_time,</if>
        <if test="updateBy != null">update_by,</if>
        <if test="updateTime != null">update_time,</if>
        <if test="soundTrain != null">sound_train,</if>
        <if test="soundRef != null">sound_ref,</if>
        <if test="remark != null">remark,</if>
        <if test="soundGpt != null">sound_gpt,</if>
        <if test="soundSovits != null">sound_sovits,</if>
        <if test="soundRefText != null">sound_ref_text,</if>
        <if test="deptId != null">dept_id,</if>
        <if test="soundFiltration != null">sound_filtration,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="soundName != null">#{soundName},</if>
        <if test="soundStatus != null">#{soundStatus},</if>
        <if test="createBy != null">#{createBy},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateBy != null">#{updateBy},</if>
        <if test="updateTime != null">#{updateTime},</if>
        <if test="soundTrain != null">#{soundTrain},</if>
        <if test="soundRef != null">#{soundRef},</if>
        <if test="remark != null">#{remark},</if>
        <if test="soundGpt != null">#{soundGpt},</if>
        <if test="soundSovits != null">#{soundSovits},</if>
        <if test="soundRefText != null">#{soundRefText},</if>
        <if test="deptId != null">#{deptId},</if>
        <if test="soundFiltration != null">#{soundFiltration},</if>
    </trim>
    </insert>
    <update id="updatePlatformSound" parameterType="PlatformSound">
    update platform_sound s
    <trim prefix="SET" suffixOverrides=",">
        <if test="soundName != null">sound_name = #{soundName},</if>
        <if test="soundStatus != null">sound_status = #{soundStatus},</if>
        <if test="createBy != null">create_by = #{createBy},</if>
        <if test="createTime != null">create_time = #{createTime},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="soundTrain != null">sound_train = #{soundTrain},</if>
        <if test="soundRef != null">sound_ref = #{soundRef},</if>
        <if test="remark != null">remark = #{remark},</if>
        <if test="soundGpt != null">sound_gpt = #{soundGpt},</if>
        <if test="soundSovits != null">sound_sovits = #{soundSovits},</if>
        <if test="soundRefText != null">sound_ref_text = #{soundRefText},</if>
        <if test="deptId != null">dept_id = #{deptId},</if>
        <if test="soundFiltration != null">sound_filtration = #{soundFiltration},</if>
    </trim>
    where s.sound_id = #{soundId}
    </update>
    <delete id="deletePlatformSoundBySoundId" parameterType="Long"> 
        delete from platform_sound where sound_id = #{soundId} 
    </delete>
    <delete id="deletePlatformSoundBySoundIds" parameterType="String">
        delete from platform_sound where sound_id in
        <foreach item="soundId" collection="array" open="(" separator="," close=")"> 
        #{soundId}  
        </foreach>
    </delete>

</mapper>