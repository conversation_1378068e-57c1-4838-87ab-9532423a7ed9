package com.ruoyi.platform.service;

import java.util.List;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.platform.domain.PlatformProject;

/**
 * 项目Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface IPlatformProjectService {
    /**
     * 查询项目
     * 
     * @param projectId 项目主键
     * @return 项目
     */
    public PlatformProject selectPlatformProjectByProjectId(Long projectId);

    /**
     * 查询项目列表
     * 
     * @param platformProject 项目
     * @return 项目集合
     */
    @DataScope(deptAlias = "d",userAlias = "u")
    public List<PlatformProject> selectPlatformProjectList(PlatformProject platformProject);

    /**
     * 新增项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    public int insertPlatformProject(PlatformProject platformProject);

    /**
     * 修改项目
     * 
     * @param platformProject 项目
     * @return 结果
     */
    public int updatePlatformProject(PlatformProject platformProject);

    /**
     * 删除项目信息
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    public int deletePlatformProjectByProjectId(Long projectId);

    /**
     * 检查是否还有分类
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    public boolean checkProjectHasCategory(Long projectId);

    //强制删除项目以及相关数据
    public boolean deleteConstraintProjectId(Long projectId) throws Exception;

    /**
     * 根据直播ID获取项目信息
     * 
     * @param liveId 直播ID
     * @return 项目信息
     */
    public PlatformProject getProjectByLiveId(Long liveId);
}
