package com.ruoyi.platform.domain;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.GetMapping;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 场控管理对象 platform_scenecon
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@Schema(description = "场控管理对象")
public class PlatformScenecon extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long sceneconId;

    /** 项目Id */
    @Schema(title = "项目Id")
    @Excel(name = "项目Id")
    @NotNull(message = "项目Id不能为空",groups = { GetMapping.class })
    private Long projectId;

    /** 场控名称 */
    @Schema(title = "场控名称")
    @Excel(name = "场控名称")
    @NotBlank(message = "场控名称不能为空")
    private String sceneconName;

    /** 场控互动分类Ids */
    @Schema(title = "场控互动分类Ids")
    @Excel(name = "场控互动分类Ids")
    private List<Long> sceneconInteractionId;

    /** 场控问答分类Ids */
    @Schema(title = "场控问答分类Ids")
    @Excel(name = "场控问答分类Ids")
    private List<Long> sceneconQuestionsId;
    public void setSceneconId(Long sceneconId) 
    {
        this.sceneconId = sceneconId;
    }

    public Long getSceneconId() 
    {
        return sceneconId;
    }


    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }


    public void setSceneconName(String sceneconName) 
    {
        this.sceneconName = sceneconName;
    }

    public String getSceneconName() 
    {
        return sceneconName;
    }


    public void setSceneconInteractionId(List<Long> sceneconInteractionId) 
    {
        this.sceneconInteractionId = sceneconInteractionId;
    }

    public List<Long> getSceneconInteractionId() 
    {
        return sceneconInteractionId;
    }


    public void setSceneconQuestionsId(List<Long> sceneconQuestionsId) 
    {
        this.sceneconQuestionsId = sceneconQuestionsId;
    }

    public List<Long> getSceneconQuestionsId() 
    {
        return sceneconQuestionsId;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("sceneconId", getSceneconId())
            .append("projectId", getProjectId())
            .append("sceneconName", getSceneconName())
            .append("sceneconInteractionId", getSceneconInteractionId())
            .append("sceneconQuestionsId", getSceneconQuestionsId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
