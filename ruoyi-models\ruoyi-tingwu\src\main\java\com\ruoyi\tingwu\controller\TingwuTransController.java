package com.ruoyi.tingwu.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.tingwu.domain.TingwuTrans;
import com.ruoyi.tingwu.service.ITingwuTransService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 音视频任务Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/tingwu/trans")
@Tag(name = "【音视频任务】管理")
public class TingwuTransController extends BaseController {
    @Autowired
    private ITingwuTransService tingwuTransService;

    /**
     * 查询音视频任务列表
     */
    @Operation(summary = "查询音视频任务列表")
    // @PreAuthorize("@ss.hasPermi('tingwu:trans:list')")
    @GetMapping("/list")
    public TableDataInfo list(TingwuTrans tingwuTrans) {
        startPage();
        List<TingwuTrans> list = tingwuTransService.selectTingwuTransList(tingwuTrans);
        return getDataTable(list);
    }



    /**
     * 获取音视频任务详细信息
     */
    @Operation(summary = "获取音视频任务详细信息")
    // @PreAuthorize("@ss.hasPermi('tingwu:trans:query')")
    @GetMapping(value = "/{vaId}")
    public AjaxResult getInfo(@PathVariable("vaId") Long vaId) {
        return success(tingwuTransService.selectTingwuTransByVaId(vaId));
    }

    /**
     * 新增音视频任务
     */
    @Operation(summary = "新增音视频任务")
    // @PreAuthorize("@ss.hasPermi('tingwu:trans:add')")
    @Log(title = "音视频任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TingwuTrans tingwuTrans) {
        return toAjax(tingwuTransService.insertTingwuTrans(tingwuTrans));
    }

    /**
     * 修改音视频任务
     */
    @Operation(summary = "修改音视频任务")
    // @PreAuthorize("@ss.hasPermi('tingwu:trans:edit')")
    @Log(title = "音视频任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TingwuTrans tingwuTrans) {
        return toAjax(tingwuTransService.updateTingwuTrans(tingwuTrans));
    }

    /**
     * 删除音视频任务
     */
    @Operation(summary = "删除音视频任务")
    // @PreAuthorize("@ss.hasPermi('tingwu:trans:remove')")
    @Log(title = "音视频任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{vaIds}")
    public AjaxResult remove(@PathVariable(name = "vaIds") Long[] vaIds) {
        return toAjax(tingwuTransService.deleteTingwuTransByVaIds(vaIds));
    }

    /**
     * 根据任务ID修改音视频任务
     *
     * @param tingwuTrans
     * @return
     */
    @Operation(summary = "根据任务ID修改音视频任务")

    @Log(title = "音视频任务", businessType = BusinessType.UPDATE)
    @PutMapping("/updateByTaskId")
    public AjaxResult updateByTaskId(@RequestBody TingwuTrans tingwuTrans) {
        if (StringUtils.isEmpty(tingwuTrans.getTaskId())) {
            return error("任务ID不能为空");
        }
        return toAjax(tingwuTransService.updateTingwuTransByTaskId(tingwuTrans));
    }
}
