package com.ruoyi.platform.domain.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户算力点数变化记录对象 platform_consumptionVo
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Schema(description = "用户算力点数变化记录对象")
public class PlatformConsumptionVo {
    
    /** 功能模块标题 */
    @Schema(title = "功能模块标题")
    private String consumptionTitle;

    /** 总共消费的算力点数 */
    @Schema(title = "总共消费的算力点数")
    private BigDecimal totalConsumptionHashrate;

    @Schema(title = "消费占比")
    private BigDecimal percentage;

    public String getConsumptionTitle() {
        return consumptionTitle;
    }

    public void setConsumptionTitle(String consumptionTitle) {
        this.consumptionTitle = consumptionTitle;
    }

    public BigDecimal getTotalConsumptionHashrate() {
        return totalConsumptionHashrate;
    }

    public void setTotalConsumptionHashrate(BigDecimal totalConsumptionHashrate) {
        this.totalConsumptionHashrate = totalConsumptionHashrate;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }

    @Override
    public String toString() {
        return "PlatformConsumptionVo{" +
                "consumptionTitle='" + consumptionTitle + '\'' +
                ", totalConsumptionHashrate=" + totalConsumptionHashrate +
                ", percentage=" + percentage +
                '}';
    }
}
