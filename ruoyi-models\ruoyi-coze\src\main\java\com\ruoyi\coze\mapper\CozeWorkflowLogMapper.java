package com.ruoyi.coze.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ruoyi.coze.domain.CozeWorkflowLog;

/**
 * CozeWorkflowLogMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface CozeWorkflowLogMapper {
    /**
     * 查询CozeWorkflowLog
     * 
     * @param id CozeWorkflowLog主键
     * @return CozeWorkflowLog
     */
    public CozeWorkflowLog selectCozeWorkflowLogById(Long id);

    /**
     * 查询CozeWorkflowLog列表
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return CozeWorkflowLog集合
     */
    public List<CozeWorkflowLog> selectCozeWorkflowLogList(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 新增CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    public int insertCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 修改CozeWorkflowLog
     * 
     * @param cozeWorkflowLog CozeWorkflowLog
     * @return 结果
     */
    public int updateCozeWorkflowLog(CozeWorkflowLog cozeWorkflowLog);

    /**
     * 删除CozeWorkflowLog
     * 
     * @param id CozeWorkflowLog主键
     * @return 结果
     */
    public int deleteCozeWorkflowLogById(Long id);

    /**
     * 批量删除CozeWorkflowLog
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCozeWorkflowLogByIds(Long[] ids);

    public int updateCozeWorkflowLogOutput(
            @Param("workflowId") String workflowId,
            @Param("executeId") String executeId,
            @Param("resultJson") String resultJson,
            @Param("updateBy") String updateBy,
            @Param("updateTime") Date updateTime);

    public int updateCozeWorkflowLogOutputById(
            @Param("id") Long id,
            @Param("resultJson") String resultJson,
            @Param("updateBy") String updateBy,
            @Param("updateTime") Date updateTime);
}
