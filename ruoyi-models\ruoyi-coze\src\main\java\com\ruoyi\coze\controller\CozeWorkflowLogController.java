package com.ruoyi.coze.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.coze.domain.CozeWorkflowLog;
import com.ruoyi.coze.service.ICozeWorkflowLogService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * CozeWorkflowLogController
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/coze/CozeWorkflowLog")
@Tag(name = "【CozeWorkflowLog】管理")
public class CozeWorkflowLogController extends BaseController {
    @Autowired
    private ICozeWorkflowLogService cozeWorkflowLogService;

    /**
     * 查询CozeWorkflowLog列表
     */
    @Operation(summary = "查询CozeWorkflowLog列表")
    @GetMapping("/list")
    public TableDataInfo list(CozeWorkflowLog cozeWorkflowLog) {
        startPage();
        List<CozeWorkflowLog> list = cozeWorkflowLogService.selectCozeWorkflowLogList(cozeWorkflowLog);
        return getDataTable(list);
    }

    /**
     * 导出CozeWorkflowLog列表
     */
    @Operation(summary = "导出CozeWorkflowLog列表")
    @Log(title = "CozeWorkflowLog", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CozeWorkflowLog cozeWorkflowLog) {
        List<CozeWorkflowLog> list = cozeWorkflowLogService.selectCozeWorkflowLogList(cozeWorkflowLog);
        ExcelUtil<CozeWorkflowLog> util = new ExcelUtil<CozeWorkflowLog>(CozeWorkflowLog.class);
        util.exportExcel(response, list, "CozeWorkflowLog数据");
    }

    /**
     * 获取CozeWorkflowLog详细信息
     */
    @Operation(summary = "获取CozeWorkflowLog详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(cozeWorkflowLogService.selectCozeWorkflowLogById(id));
    }

    /**
     * 新增CozeWorkflowLog
     */
    @Operation(summary = "新增CozeWorkflowLog")
    @Log(title = "CozeWorkflowLog", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CozeWorkflowLog cozeWorkflowLog) {
        return toAjax(cozeWorkflowLogService.insertCozeWorkflowLog(cozeWorkflowLog));
    }

    /**
     * 修改CozeWorkflowLog
     */
    @Operation(summary = "修改CozeWorkflowLog")
    @Log(title = "CozeWorkflowLog", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CozeWorkflowLog cozeWorkflowLog) {
        return toAjax(cozeWorkflowLogService.updateCozeWorkflowLog(cozeWorkflowLog));
    }

    /**
     * 删除CozeWorkflowLog
     */
    @Operation(summary = "删除CozeWorkflowLog")
    @Log(title = "CozeWorkflowLog", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable(name = "ids") Long[] ids) {
        return toAjax(cozeWorkflowLogService.deleteCozeWorkflowLogByIds(ids));
    }

    /**
     * 保存工作流日志-输入
     */
    @Operation(summary = "保存工作流日志-输入")
    @PostMapping("/saveLogWithInput")
    public AjaxResult saveLogWithInput(@RequestParam String workflowId,
            @RequestParam String executeId,
            @RequestBody Object request) {
        try {
            cozeWorkflowLogService.saveLogWithInput(workflowId, executeId, request);
            return AjaxResult.success("保存成功");
        } catch (Exception e) {
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 保存工作流日志-输出
     */
    @Operation(summary = "修改工作流日志-输出")
    @PostMapping("/saveLogWithOutput")
    public AjaxResult saveLogWithOutput(@RequestParam String workflowId,
            @RequestParam String executeId,
            @RequestBody Map<String,Object> result) {
        try {
            cozeWorkflowLogService.saveLogWithOutput(workflowId, executeId, result);
            return AjaxResult.success("保存成功");
        } catch (Exception e) {
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }
}
