<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformKeywordMapper">

    <resultMap type="PlatformKeyword" id="PlatformKeywordResult">
        <result property="keywordId" column="keyword_id" />
        <result property="categoryId" column="category_id" />
        <result property="keywordName" column="keyword_name" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectPlatformKeywordVo">
        select k.keyword_id, k.category_id, k.keyword_name, k.create_by, k.create_time, k.update_by, k.update_time from platform_keyword k
        left join sys_user u on u.user_name = k.create_by left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformKeywordList" parameterType="PlatformKeyword" resultMap="PlatformKeywordResult">
        <include refid="selectPlatformKeywordVo"/>
        <where>
            <if test="categoryId != null  and categoryId != ''"> and category_id = #{categoryId}</if>
            <if test="keywordName != null  and keywordName != ''"> and keyword_name like concat('%', #{keywordName}, '%')</if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectPlatformKeywordByKeywordId" parameterType="Long" resultMap="PlatformKeywordResult">
        <include refid="selectPlatformKeywordVo"/>
        where keyword_id = #{keywordId}
    </select>
    <select id="selectPlatformKeywordByCategoryIds" parameterType="List" resultMap="PlatformKeywordResult">
        <include refid="selectPlatformKeywordVo"/>
        <where>
            AND category_id IN
            <foreach item="item" index="index" collection="categoryIds" open="(" close=")" separator=",">  
              #{item}   
            </foreach>
        </where>
    </select>

    <insert id="insertPlatformKeyword" parameterType="PlatformKeyword" useGeneratedKeys="true" keyProperty="keywordId">
        insert into platform_keyword
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="keywordName != null">keyword_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="keywordName != null">#{keywordName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePlatformKeyword" parameterType="PlatformKeyword">
        update platform_keyword k
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="keywordName != null">keyword_name = #{keywordName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where k.keyword_id = #{keywordId} 
    </update>

    <delete id="deletePlatformKeywordByKeywordId" parameterType="Long">
        delete from platform_keyword where keyword_id = #{keywordId}
    </delete>

    <delete id="deletePlatformKeywordByKeywordIds" parameterType="String">
        delete from platform_keyword where keyword_id in 
        <foreach item="keywordId" collection="array" open="(" separator="," close=")">
            #{keywordId}
        </foreach>
    </delete>

    <select id="getKeywordsByLiveId" parameterType="Long" resultMap="PlatformKeywordResult">
        SELECT DISTINCT k.* 
        FROM platform_live l
        LEFT JOIN platform_scenecon s ON l.scenecon_id = s.scenecon_id
        LEFT JOIN platform_goods g ON l.goods_id = g.goods_id
        JOIN platform_keyword k ON 
            (s.scenecon_interaction_id IS NOT NULL AND FIND_IN_SET(k.category_id, REPLACE(REPLACE(s.scenecon_interaction_id,'[',''),']','')) > 0)
            OR (s.scenecon_questions_id IS NOT NULL AND FIND_IN_SET(k.category_id, REPLACE(REPLACE(s.scenecon_questions_id,'[',''),']','')) > 0)
            OR (g.goods_interaction_id IS NOT NULL AND FIND_IN_SET(k.category_id, REPLACE(REPLACE(g.goods_interaction_id,'[',''),']','')) > 0)
            OR (g.goods_questions_id IS NOT NULL AND FIND_IN_SET(k.category_id, REPLACE(REPLACE(g.goods_questions_id,'[',''),']','')) > 0)
        WHERE l.live_id = #{liveId}
    </select>
</mapper>