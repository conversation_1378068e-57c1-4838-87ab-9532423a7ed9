package com.ruoyi.platform.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformKeyword;
import com.ruoyi.platform.service.IPlatformKeywordService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.Data;

/**
 * 关键词管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@RestController
@RequestMapping("/platform/keyword")
@Tag(name = "【关键词管理】管理")
public class PlatformKeywordController extends BaseController {
    
    @Autowired
    private IPlatformKeywordService platformKeywordService;

    /**
     * 查询关键词管理列表
     */
    @Operation(summary = "查询关键词管理列表")
    // @PreAuthorize("@ss.hasPermi('platform:keyword:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformKeyword platformKeyword) {
        List<PlatformKeyword> list = platformKeywordService.selectPlatformKeywordList(platformKeyword);
        return getDataTable(list);
    }

    /**
     * 匹配关键词分类
     */
    @Data
    static class FormatKeyword {
        public ArrayList<Long> categoryIds;
        public ArrayList<String> strs;
    }
    @Operation(summary = "匹配关键词分类")
    @PostMapping("/listByFormat")
    public R<Long> listByFormat(@RequestBody FormatKeyword formatKeyword) {
        return R.ok(platformKeywordService.listByFormat(formatKeyword.categoryIds, formatKeyword.strs));
    } 

    /**
     * 导出关键词管理列表
     */
    @Operation(summary = "导出关键词管理列表")
    @PreAuthorize("@ss.hasPermi('platform:keyword:export')")
    @Log(title = "导出关键词管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformKeyword platformKeyword) {
        List<PlatformKeyword> list = platformKeywordService.selectPlatformKeywordList(platformKeyword);
        ExcelUtil<PlatformKeyword> util = new ExcelUtil<PlatformKeyword>(PlatformKeyword.class);
        util.exportExcel(response, list, "关键词管理数据");
    }

    /**
     * 获取关键词管理详细信息
     */
    @Operation(summary = "获取关键词管理详细信息")
    // @PreAuthorize("@ss.hasPermi('platform:keyword:query')")
    @GetMapping(value = "/{keywordId}")
    public AjaxResult getInfo(@PathVariable("keywordId") Long keywordId) {
        return success(platformKeywordService.selectPlatformKeywordByKeywordId(keywordId));
    }

    /**
     * 新增关键词管理
     */
    @Operation(summary = "新增关键词管理")
    // @PreAuthorize("@ss.hasPermi('platform:keyword:add')")
    @Log(title = "新增关键词管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformKeyword platformKeyword) {
        platformKeyword.setCreateBy(getUsername());
        platformKeyword.setUpdateBy(getUsername());
        return toAjax(platformKeywordService.insertPlatformKeyword(platformKeyword));
    }

    /**
     * 修改关键词管理
     */
    @Operation(summary = "修改关键词管理")
    // @PreAuthorize("@ss.hasPermi('platform:keyword:edit')")
    @Log(title = "修改关键词管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformKeyword platformKeyword) {
        platformKeyword.setUpdateBy(getUsername());
        return toAjax(platformKeywordService.updatePlatformKeyword(platformKeyword));
    }

    /**
     * 删除关键词管理
     */
    @Operation(summary = "删除关键词管理")
    // @PreAuthorize("@ss.hasPermi('platform:keyword:remove')")
    @Log(title = "删除关键词管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{keywordIds}")
    public AjaxResult remove(@PathVariable(name = "keywordIds") Long[] keywordIds) {
        return toAjax(platformKeywordService.deletePlatformKeywordByKeywordIds(keywordIds));
    }
}
