package com.ruoyi.coze.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.ruoyi.coze.domain.WorkflowRunRequest;
import com.ruoyi.coze.service.ICozeWorkflowService;

import lombok.extern.slf4j.Slf4j;

/**
 * Coze工作流控制器
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RestController
@RequestMapping("/coze/workflow")
public class CozeWorkflowController {

    @Autowired
    private ICozeWorkflowService cozeWorkflowService;

    /**
     * 执行工作流任务
     */
    @PostMapping("/run")
    @Log(title = "工作流", businessType = BusinessType.OTHER, operatorType = OperatorType.MANAGE)
    public AjaxResult runWorkflow(@RequestBody WorkflowRunRequest request) {
        try {
            Map<String, Object> result = cozeWorkflowService.runWorkflow(request);
            if (result == null)
                return AjaxResult.error("工作流服务返回结果为空");

            String message = result.get("msg") != null ? result.get("msg").toString() : "工作流服务返回结果为空";

            return AjaxResult.success(message, result);
        } catch (Exception e) {
            log.error("启动工作流失败", e);
            return AjaxResult.error("启动工作流失败: " + e.getMessage());
        }
    }

    /**
     * 查询异步工作流执行状态
     */
    @GetMapping("/status/{workflowId}/{executeId}")
    @Log(title = "工作流状态查询", businessType = BusinessType.OTHER, operatorType = OperatorType.MANAGE)
    public AjaxResult getAsyncWorkflowStatus(@PathVariable String workflowId,
            @PathVariable String executeId) {
        try {
            Map<String, Object> result = cozeWorkflowService.getAsyncWorkflowStatus(workflowId, executeId);
            Boolean success = (Boolean) result.get("success");
            if (success) {
                return AjaxResult.success("查询成功", result);
            } else {
                return AjaxResult.error("工作流执行失败", result);
            }
        } catch (Exception e) {
            log.error("查询工作流状态失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 轮询异步工作流执行结果（直到完成）
     */
    @GetMapping("/poll/{workflowId}/{executeId}")
    @Log(title = "工作流结果轮询", businessType = BusinessType.OTHER, operatorType = OperatorType.MANAGE)
    public AjaxResult pollAsyncWorkflowResult(@PathVariable String workflowId,
            @PathVariable String executeId,
            @RequestParam(defaultValue = "180") int pollInterval) {
        try {
            Map<String, Object> result = cozeWorkflowService.pollAsyncWorkflowResult(workflowId, executeId, pollInterval);
            Boolean success = (Boolean) result.get("success");
            String status = (String) result.get("status");

            if ("TIMEOUT".equals(status)) {
                return AjaxResult.success("轮询超时", result);
            } else if (success) {
                return AjaxResult.success("工作流执行完成", result);
            } else {
                return AjaxResult.error("工作流执行失败", result);
            }
        } catch (Exception e) {
            log.error("轮询工作流结果失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}