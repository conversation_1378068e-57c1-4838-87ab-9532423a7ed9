package com.ruoyi.platform.model.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.platform.model.domain.PlatformTask;
import com.ruoyi.platform.model.service.IPlatformSoundService;
import com.ruoyi.platform.model.service.IPlatformTaskService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
@Anonymous
@RestController
@RequestMapping("/platform/machine/{machineCode}")
@Tag(name = "【AI机器管理】管理")
public class PlatformMachineCodeNameController extends BaseController {

    @Autowired
    private IPlatformSoundService platformSoundService;
    
    @Autowired
    private IPlatformTaskService platformTaskService;

    // 获取成功的任务列表不需要权限
    @Operation(summary = "查询待处理任务队列")
    @GetMapping("/getTask")
    public R<PlatformTask> getTasks(@PathVariable("machineCode") String machineCode) {
        PlatformTask updatedTask = platformTaskService.selectPlatformmachineCode(machineCode);
        return R.ok(updatedTask);
    }

    @Operation(summary = "声音推理任务回调")
    @PostMapping("/tts/callback")
    public AjaxResult ttsCallback(@PathVariable("machineCode") String machineCode, @RequestBody PlatformTask platformTask) {
        return success(platformTaskService.ttsCallback(platformTask));
    }

    // 训练任务回调
    @Operation(summary = "训练任务回调")
    @PostMapping("/train/callback")
    public AjaxResult trainCallback(@PathVariable("machineCode") String machineCode, @RequestBody PlatformTask platformTask) {
        return success(platformTaskService.taskCallback(platformTask));
    }

    // 推理音频上传
    @Operation(summary = "推理音频上传")
    @PostMapping("/uploadTask")
    public R<String> uploadTask(@RequestParam("file") MultipartFile file, @RequestParam("taskId") Long taskId,
        @PathVariable("machineCode") String machineCode) throws Exception {
        String filePath=platformTaskService.uploadTask(file, taskId, machineCode);
        return R.ok(filePath);
    }

    // 模型服务上传训练后的声音模型文件: ModelCheckpoint/sound/xxxx.ckpt
    @Operation(summary = "模型服务上传训练后的声音模型文件")
    @PostMapping("/uploadModelSoundFile")
    public R<Object> uploadModelSoundFile(
        @RequestParam("gptModel") MultipartFile gptModel, @RequestParam("sovitsModel") MultipartFile sovitsModel,
        @RequestParam(value = "taskId", required = false) Long taskId, @RequestParam("soundId") Long soundId,
        @PathVariable(value = "machineCode") String machineCode) throws Exception {
        Object task=platformTaskService.uploadModelSoundFile(gptModel, sovitsModel, taskId, soundId, machineCode);
        return R.ok(task);
    }

    // 模型服务上传推理后的音频文件: ModelTask/sound/{taskId}/xxxx.wav
    @Operation(summary = "模型服务上传推理后的音频文件")
    @PostMapping("/uploadModelAudioFile")
    public R<Object> uploadModelAudioFile(@RequestParam("audioFile") MultipartFile audioFile, @RequestParam("taskId") Long taskId,
        @PathVariable(value = "machineCode", required = false) String machineCode) throws Exception {
        Object filePath=platformTaskService.uploadModelAudioFile(audioFile, taskId, machineCode);
        return R.ok(filePath);
    }

    @Operation(summary = "通过机器码下载音频")
    @GetMapping("/downloadAudioByMachineCode")
    public void downloadAudioByMachineCode(@PathVariable("machineCode") String machineCode,
            @RequestParam("fileUrl") String fileUrl, HttpServletResponse response) throws Exception {
            platformTaskService.downloadAudioByMachineCode(machineCode, fileUrl, response);
    }

    @Operation(summary = "声音模型下载")
    @Deprecated(since = "1.1.0", forRemoval = true)
    @Parameters({
            @Parameter(name = "soundId", description = "声音ID", required = true),
            @Parameter(name = "model", description = "模型类型  gpt/sovits", required = true),
            @Parameter(name = "machineCode", description = "机器码", required = true)
    })
    @GetMapping("/downloadModel/{soundId}/{model}")
    public void downloadModel(@PathVariable("soundId") Long soundId, @PathVariable("model") String model,
            @PathVariable("machineCode") String machineCode,HttpServletResponse response) throws Exception {
        platformSoundService.downloadModel(soundId, model, machineCode, response);
    }

    @Operation(summary = "通用下载音频文件")
    @GetMapping("/downloadAudio")
    public void downloadAudio(@PathVariable("machineCode") String machineCode, @RequestParam("fileUrl") String fileUrl,
            HttpServletResponse response) throws Exception {
        platformTaskService.downloadAudio(machineCode, fileUrl, response); 
    }
}
