package com.ruoyi.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 形象管理对象 platform_image
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Schema(description = "形象管理对象")
public class PlatformImage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(title = "主键")
    private Long imageId;

    /** 形象名称 */
    @Schema(title = "形象名称")
    @Excel(name = "形象名称")
    private String imageName;

    /** 形象代号 */
    @Schema(title = "形象代号")
    @Excel(name = "形象代号")
    private String imageCode;

    /** 形象状态 */
    @Schema(title = "形象状态")
    @Excel(name = "形象状态")
    private String imageStatus;

    /** 形象地址 */
    @Schema(title = "形象地址")
    @Excel(name = "形象地址")
    private String imageAddress;

    @TableField(exist = false)
    private String url;

    /** 形象MD5值 */
    @Schema(title = "视频形象文件的MD5校验码")
    @Excel(name = "MD5校验码")
    @TableField(value = "video_md5" ,exist = true)
    private String md5;

    public void setImageId(Long imageId) 
    {
        this.imageId = imageId;
    }

    public Long getImageId() 
    {
        return imageId;
    }


    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public void setImageName(String imageName) 
    {
        this.imageName = imageName;
    }

    public String getImageName() 
    {
        return imageName;
    }


    public void setImageCode(String imageCode) 
    {
        this.imageCode = imageCode;
    }

    public String getImageCode() 
    {
        return imageCode;
    }


    public void setImageStatus(String imageStatus) 
    {
        this.imageStatus = imageStatus;
    }

    public String getImageStatus() 
    {
        return imageStatus;
    }


    public void setImageAddress(String imageAddress) 
    {
        this.imageAddress = imageAddress;
    }

    public String getImageAddress() 
    {
        return imageAddress;
    }



    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("imageId", getImageId())
            .append("imageName", getImageName())
            .append("imageCode", getImageCode())
            .append("imageStatus", getImageStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("imageAddress", getImageAddress())
            .toString();
    }
}
