<mxfile host="65bd71144e">
    <diagram id="OUCQybPowJdnNh2YIqGc" name="第 1 页">
        <mxGraphModel dx="823" dy="766" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="ellipse;html=1;shape=startState;fillColor=#000000;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="388" y="260" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;" parent="1" source="2" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="403" y="440" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="创建订单" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3" vertex="1" connectable="0">
                    <mxGeometry x="-0.1879" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="未支付" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="343" y="370" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="462" y="540" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="支付" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6" vertex="1" connectable="0">
                    <mxGeometry x="-0.2513" y="-1" relative="1" as="geometry">
                        <mxPoint x="1" y="13" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="已支付" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="343" y="490" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="8" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="288" y="590" as="targetPoint"/>
                        <mxPoint x="343" y="510" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="申请开票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="9" vertex="1" connectable="0">
                    <mxGeometry x="-0.2606" relative="1" as="geometry">
                        <mxPoint x="-7" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="388" y="595" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="待开票" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="129" y="490" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="14" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="273" y="730" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="同意开票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="15" vertex="1" connectable="0">
                    <mxGeometry x="-0.2741" y="1" relative="1" as="geometry">
                        <mxPoint x="-1" y="8" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="已开票" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="129" y="590" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="17" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="273" y="840" as="targetPoint"/>
                        <Array as="points"/>
                        <mxPoint x="299" y="800" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="下载发票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="18" vertex="1" connectable="0">
                    <mxGeometry x="-0.2583" y="-1" relative="1" as="geometry">
                        <mxPoint x="-12" y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="开票失败" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="129" y="370" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="1" source="14" target="21" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="478" y="750" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="219" y="410"/>
                            <mxPoint x="219" y="410"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="不同意开票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="22" vertex="1" connectable="0">
                    <mxGeometry x="-0.1415" y="-1" relative="1" as="geometry">
                        <mxPoint x="-1" y="13" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="1" source="21" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="149" y="580" as="targetPoint"/>
                        <mxPoint x="79" y="440" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="159" y="460"/>
                            <mxPoint x="159" y="460"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="再次申请开票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="25" vertex="1" connectable="0">
                    <mxGeometry x="-0.1415" y="-1" relative="1" as="geometry">
                        <mxPoint x="5" y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="待退款" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="579" y="490" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="27" target="32" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="462" y="700" as="targetPoint"/>
                        <mxPoint x="579" y="650" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="同意退款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="28" vertex="1" connectable="0">
                    <mxGeometry x="-0.2333" y="-2" relative="1" as="geometry">
                        <mxPoint x="-2" y="3" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="8" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="499" y="550" as="targetPoint"/>
                        <mxPoint x="669" y="570" as="sourcePoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="申请退款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="30" vertex="1" connectable="0">
                    <mxGeometry x="0.2333" relative="1" as="geometry">
                        <mxPoint x="-28" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="已退款" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="579" y="590" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="32" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="462" y="830" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="退款到账" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="33" vertex="1" connectable="0">
                    <mxGeometry x="0.0112" y="3" relative="1" as="geometry">
                        <mxPoint x="30" y="-3" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="退款失败" style="rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;" parent="1" vertex="1">
                    <mxGeometry x="579" y="370" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="1" source="27" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="688" y="800" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="669" y="460"/>
                            <mxPoint x="669" y="460"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="拒绝退款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="37" vertex="1" connectable="0">
                    <mxGeometry x="-0.4012" y="2" relative="1" as="geometry">
                        <mxPoint x="2" y="-16" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;" parent="1" source="36" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="698" y="710" as="targetPoint"/>
                        <mxPoint x="738" y="590" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="609" y="470"/>
                            <mxPoint x="609" y="470"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="再次申请退款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="40" vertex="1" connectable="0">
                    <mxGeometry x="-0.4012" y="2" relative="1" as="geometry">
                        <mxPoint x="-12" y="16" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;entryX=1;entryY=0.25;entryDx=0;entryDy=0;exitX=0;exitY=0.25;exitDx=0;exitDy=0;" parent="1" source="27" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="442" y="640" as="targetPoint"/>
                        <mxPoint x="539" y="500" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="539" y="500"/>
                            <mxPoint x="539" y="500"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="拒绝退款" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="43" vertex="1" connectable="0">
                    <mxGeometry x="0.2333" relative="1" as="geometry">
                        <mxPoint x="12" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" parent="1" source="14" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="349" y="610" as="targetPoint"/>
                        <mxPoint x="359" y="550" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="拒绝开票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="45" vertex="1" connectable="0">
                    <mxGeometry x="-0.2606" relative="1" as="geometry">
                        <mxPoint x="6" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="8" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="413" y="500" as="targetPoint"/>
                        <mxPoint x="413" y="420" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="不支持开发票" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="49" vertex="1" connectable="0">
                    <mxGeometry x="-0.2513" y="-1" relative="1" as="geometry">
                        <mxPoint x="1" y="6" as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>