package com.ruoyi.tingwu.enums;

/**
 * 翻译选项枚举
 * 如需添加新的翻译选项，请查询阿里云官网文档
 * https://help.aliyun.com/zh/tingwu/offline-transcribe-of-audio-and-video-files?spm=a2c4g.11186623.help-menu-454189.d_2_1_0.56755041hQHBjQ&scm=20140722.H_2609582._.OR_help-T_cn~zh-V_1#59fb0ee081762
 */
public enum TranslateOption {
    NONE("0", "-1"),  //不翻译
    ENGLISH("1", "en"),  //英文
    JAPANESE("2", "ja"); //日语

    private final String code;
    private final String description;

    TranslateOption(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TranslateOption fromCode(String code) {
        for (TranslateOption translate : TranslateOption.values()) {
            if (translate.getCode().equals(code)) {
                return translate;
            }
        }
        return null;
    }
}