package com.ruoyi.platform.service;

import java.util.List;

import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;

/**
 * AI模型Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface IPlatformModelService 
{
    /**
     * 查询AI模型
     * 
     * @param modelId AI模型主键
     * @return AI模型
     */
    public PlatformModel selectWyModelByModelId(Long modelId);

    /**
     * 查询AI模型列表
     * 
     * @param wyModel AI模型
     * @return AI模型集合
     */
    public List<PlatformModel> selectWyModelList(PlatformModel wyModel);

    /**
     * 新增AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    public int insertWyModel(PlatformModel wyModel);

    /**
     * 修改AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    public int updateWyModel(PlatformModel wyModel);

    /**
     * 批量删除AI模型
     * 
     * @param modelIds 需要删除的AI模型主键集合
     * @return 结果
     */
    public int deleteWyModelByModelIds(Long[] modelIds);

    /**
     * 删除AI模型信息
     * 
     * @param modelId AI模型主键
     * @return 结果
     */
    public int deleteWyModelByModelId(Long modelId);

    /**
     * 获取模型价格
     * @param modelCode 模型代码
     * @return 模型价格点数
     */
    public int getModelPrice(String modelCode);

     /**
     * 处理视频任务的费用扣除
     * @param task 视频任务
     * @return 是否扣除成功
     */
    public boolean processTaskFeeDeduction(PlatformVideo task);
}
