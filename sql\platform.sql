
DROP TABLE IF EXISTS platform_project;
CREATE TABLE platform_project
(
  project_id          BIGINT(20)      NOT NULL AUTO_INCREMENT COMMENT '主键',
  project_title        VARCHAR(255)    NOT NULL COMMENT '项目名称',
  create_by   VARCHAR(64)     DEFAULT ''               COMMENT '创建者',
  create_time DATETIME                                  COMMENT '创建时间',
  update_by   VARCHAR(64)     DEFAULT ''               COMMENT '更新者',
  update_time DATETIME                                  COMMENT '更新时间',
  remark      VARCHAR(500)    DEFAULT NULL             COMMENT '备注',
  PRIMARY KEY (project_id)
) ENGINE=InnoDB AUTO_INCREMENT=100 COMMENT='项目表';

DROP TABLE IF EXISTS platform_category;
CREATE TABLE platform_category
(
  category_id          BIGINT(20)      NOT NULL AUTO_INCREMENT COMMENT '主键',
  category_title        VARCHAR(255)    NOT NULL COMMENT '分类名称',
  project_id  BIGINT(20)      NOT NULL COMMENT '项目ID',
  create_by   VA<PERSON>HA<PERSON>(64)     DEFAULT ''               COMMENT '创建者',
  create_time DATETIME                                  COMMENT '创建时间',
  update_by   VARCHAR(64)     DEFAULT ''               COMMENT '更新者',
  update_time DATETIME                                  COMMENT '更新时间',
  remark      VARCHAR(500)    DEFAULT NULL             COMMENT '备注',
  PRIMARY KEY (category_id)
) ENGINE=InnoDB AUTO_INCREMENT=100 COMMENT='分类表';

DROP TABLE IF EXISTS platform_article;
CREATE TABLE platform_article
(
  article_id          BIGINT(20)      NOT NULL AUTO_INCREMENT COMMENT '主键',
  category_id BIGINT(20)      NOT NULL COMMENT '分类ID',
  article_title       VARCHAR(255)    NOT NULL COMMENT '标题',
  content     TEXT            NOT NULL COMMENT '内容',
  create_by   VARCHAR(64)     DEFAULT ''               COMMENT '创建者',
  create_time DATETIME                                  COMMENT '创建时间',
  update_by   VARCHAR(64)     DEFAULT ''               COMMENT '更新者',
  update_time DATETIME                                  COMMENT '更新时间',
  remark      VARCHAR(500)    DEFAULT NULL             COMMENT '备注',
  PRIMARY KEY (article_id)
) ENGINE=InnoDB AUTO_INCREMENT=100 COMMENT='文案表';


