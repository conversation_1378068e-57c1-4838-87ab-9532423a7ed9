package com.ruoyi.tingwu.enums;

/**
 * 音视频转换状态枚举
 * 
 * <AUTHOR>
 */
public enum AudioVideoStatus {
    
    PROCESSING("1", "ONGOING"), //转换中
    SUCCESS("2", "COMPLETED"), //转换成功
    FAILED("3", "FAILED");  //转换失败
    
    private final String code;
    private final String description;
    
    AudioVideoStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据状态码获取枚举实例
     * 
     * @param code 状态码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static AudioVideoStatus fromCode(String code) {
        for (AudioVideoStatus status : AudioVideoStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据描述获取枚举实例
     * 
     * @param description 状态描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static AudioVideoStatus fromDescription(String description) {
        for (AudioVideoStatus status : AudioVideoStatus.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }
    
    @Override
    public String toString() {
        return this.code + ":" + this.description;
    }
}