package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformProject;
import com.ruoyi.platform.service.IPlatformProjectService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 项目Controller
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@RestController
@RequestMapping("/platform/project")
@Tag(name = "【项目】管理")
public class PlatformProjectController extends BaseController
{
    @Autowired
    private IPlatformProjectService platformProjectService;

    /**
     * 查询项目列表
     */
    @Operation(summary = "查询项目列表")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformProject platformProject)
    {
        startPage();
        List<PlatformProject> list = platformProjectService.selectPlatformProjectList(platformProject);
        return getDataTable(list);
    }

    /**
     * 导出项目列表
     */
    @Operation(summary = "导出项目列表")
    @Log(title = "导出项目列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformProject platformProject)
    {
        List<PlatformProject> list = platformProjectService.selectPlatformProjectList(platformProject);
        ExcelUtil<PlatformProject> util = new ExcelUtil<PlatformProject>(PlatformProject.class);
        util.exportExcel(response, list, "项目数据");
    }

    /**
     * 获取项目详细信息
     */
    @Operation(summary = "获取项目详细信息")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return success(platformProjectService.selectPlatformProjectByProjectId(projectId));
    }

    /**
     * 新增项目
     */
    @Operation(summary = "新增项目")
    @Log(title = "新增项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformProject platformProject)
    {
        platformProject.setCreateBy(getUsername());
        platformProject.setUpdateBy(getUsername());
        return toAjax(platformProjectService.insertPlatformProject(platformProject));
    }

    /**
     * 修改项目
     */
    @Operation(summary = "修改项目")
    @Log(title = "修改项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformProject platformProject)
    {
        platformProject.setUpdateBy(getUsername());
        return toAjax(platformProjectService.updatePlatformProject(platformProject));
    }

    /**
     * 删除项目
     */
    @Operation(summary = "删除项目")
    @Log(title = "删除项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectId}")
    public AjaxResult remove(@PathVariable( name = "projectId" ) Long projectIds) 
    {
        return toAjax(platformProjectService.deletePlatformProjectByProjectId(projectIds));
    }



    //强制删除项目
    @Operation(summary = "强制删除项目")
    @Log(title = "强制删除项目", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteConstraintProjectId/{projectId}")
    public AjaxResult deleteConstraintProjectId(@PathVariable(name = "projectId") Long projectId) {
        try {
            boolean success = platformProjectService.deleteConstraintProjectId(projectId);
            return success ? AjaxResult.success("项目及相关数据已成功删除！") : AjaxResult.error("删除操作未能成功！");
        } catch (Exception e) {
            return AjaxResult.error("删除过程中发生异常: " + e.getMessage());
        }
    }

}
