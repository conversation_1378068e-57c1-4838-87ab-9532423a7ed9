package com.ruoyi.platform.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Select;

import com.ruoyi.platform.domain.PlatformHashrate;

/**
 * 用户算力点数Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface PlatformHashrateMapper 
{
    /**
     * 查询用户算力点数
     * 
     * @param hashrateId 用户算力点数主键
     * @return 用户算力点数
     */
    public PlatformHashrate selectPlatformHashrateByHashrateId(Long hashrateId);

    /**
     * 查询用户算力点数列表
     * 
     * @param platformHashrate 用户算力点数
     * @return 用户算力点数集合
     */
    public List<PlatformHashrate> selectPlatformHashrateList(PlatformHashrate platformHashrate);

    /**
     * 新增用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    public int insertPlatformHashrate(PlatformHashrate platformHashrate);

    /**
     * 修改用户算力点数
     * 
     * @param platformHashrate 用户算力点数
     * @return 结果
     */
    public int updatePlatformHashrate(PlatformHashrate platformHashrate);

    /**
     * 删除用户算力点数
     * 
     * @param hashrateId 用户算力点数主键
     * @return 结果
     */
    public int deletePlatformHashrateByHashrateId(Long hashrateId);

    /**
     * 批量删除用户算力点数
     * 
     * @param hashrateIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformHashrateByHashrateIds(Long[] hashrateIds);

    //查询算力用户
    public PlatformHashrate selectByUserName(String userName);

    //删除之前先查询一下该用户是否还有有消费、充值记录  如果有一并删除掉
    public List<Map<String, Object>> getHashrateAndConsumptionTitleByUserId(String userName);
    
    //单个删除 算力用户不再使用 进行强制删除算力用户、消费、充值记录
    public int deleteByUserName(String userName);

    //批量删除 算力用户 充值、消费记录
    public int deleteByUserNames(String[] userNames);

    /**
     * 批量插入算力点卡号
     */
    public void batchInsert(List<PlatformHashrate> list);

    /**
     * 根据卡号查询卡信息
     *
     * @param cardNumber 卡号
     * @return 卡信息
     */
    @Select("SELECT * FROM platform_hashrate WHERE hashrate_card = #{cardNumber}")
    public PlatformHashrate selectByCardNumber(String cardNumber);
}
