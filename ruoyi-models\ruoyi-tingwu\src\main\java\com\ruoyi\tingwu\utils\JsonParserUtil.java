package com.ruoyi.tingwu.utils;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.tingwu.domain.TingWuResult;

public class JsonParserUtil {

    private static final Logger log = LoggerFactory.getLogger(JsonParserUtil.class);

    /**
     * 解析阿里云听悟回调的JSON字符串，提取关键字段
     *
     * @param returnData JSON字符串
     * @return 解析后的对象，失败返回 null
     */
    public static TingWuResult parseTingWuResult(String returnData) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(returnData);
            JSONObject data = jsonObject.getJSONObject("Data");

            if (data == null) {
                log.warn("JSON中未找到Data字段");
                return null;
            }

            String taskStatus = data.getString("TaskStatus");
            JSONObject result = data.getJSONObject("Result");

            if (result == null) {
                log.warn("Data中未找到Result字段");
                return null;
            }

            TingWuResult tingWuResult = new TingWuResult();
            tingWuResult.setTaskStatus(taskStatus);
            tingWuResult.setMeetingAssistance(result.getString("MeetingAssistance"));
            tingWuResult.setTranscription(result.getString("Transcription"));
            tingWuResult.setAutoChapters(result.getString("AutoChapters"));
            tingWuResult.setSummarization(result.getString("Summarization"));

            return tingWuResult;

        } catch (Exception e) {
            log.error("解析听悟回调JSON失败", e);
            return null;
        }
    }
}
