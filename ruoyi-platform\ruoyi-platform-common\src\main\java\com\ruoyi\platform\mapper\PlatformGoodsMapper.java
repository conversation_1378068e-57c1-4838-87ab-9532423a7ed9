package com.ruoyi.platform.mapper;

import java.util.List;

import com.ruoyi.platform.domain.PlatformGoods;

/**
 * 产品管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
public interface PlatformGoodsMapper 
{
    /**
     * 查询产品管理
     * 
     * @param goodsId 产品管理主键
     * @return 产品管理
     */
    public PlatformGoods selectPlatformGoodsByGoodsId(Long goodsId);
    public List<PlatformGoods> selectPlatformGoodsByGoodsIds(List<Long> goodsIds);

    /**
     * 查询产品管理列表
     * 
     * @param platformGoods 产品管理
     * @return 产品管理集合
     */
    public List<PlatformGoods> selectPlatformGoodsList(PlatformGoods platformGoods);

    /**
     * 新增产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    public int insertPlatformGoods(PlatformGoods platformGoods);

    /**
     * 修改产品管理
     * 
     * @param platformGoods 产品管理
     * @return 结果
     */
    public int updatePlatformGoods(PlatformGoods platformGoods);

    /**
     * 删除产品管理
     * 
     * @param goodsId 产品管理主键
     * @return 结果
     */
    public int deletePlatformGoodsByGoodsId(Long goodsId);

    /**
     * 批量删除产品管理
     * 
     * @param goodsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformGoodsByGoodsIds(Long[] goodsIds);

    /**
     * 根据传入的商品数据Ids查找一一对应的商品数据。
     * 
     * @param ids 商品ID列表
     * @return 匹配的商品列表
     */
    public List<PlatformGoods> getGoodsByIds(List<Long> ids);
}
