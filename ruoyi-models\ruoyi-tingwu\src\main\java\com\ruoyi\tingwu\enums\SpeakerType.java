package com.ruoyi.tingwu.enums;


// 区分发言人类型枚举

//下面为官方文档解释
// 开启说话人分离功能时，设置的说话人数
// 不设置：不使用说话人角色区分。 0：说话人角色区分结果为不定人数。 2：说话人角色区分结果为2人。
// 当有此参数时，使用此参数辅助指定的人数，最终分轨人数以真实分类人数为准。

public enum SpeakerType {
    NONE("0", -1), // 暂不体验
    SINGLE("1", 1), // 单人演讲
    TWO_PEOPLE("2", 2), // 2人对话
    MULTIPLE("3", 0); // 多人讨论

    private final String code;
    private final Integer description;

    SpeakerType(String code, Integer description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public Integer getDescription() {
        return description;
    }

    public static SpeakerType fromCode(String code) {
        for (SpeakerType speakerType : SpeakerType.values()) {
            if (speakerType.getCode().equals(code)) {
                return speakerType;
            }
        }
        return null;
    }
}