package com.ruoyi.platform.mapper;

import java.util.List;

import com.ruoyi.platform.domain.PlatformScenecon;

/**
 * 场控管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
public interface PlatformSceneconMapper 
{
    /**
     * 查询场控管理
     * 
     * @param sceneconId 场控管理主键
     * @return 场控管理
     */
    public PlatformScenecon selectPlatformSceneconBySceneconId(Long sceneconId);

    /**
     * 查询场控管理列表
     * 
     * @param platformScenecon 场控管理
     * @return 场控管理集合
     */
    public List<PlatformScenecon> selectPlatformSceneconList(PlatformScenecon platformScenecon);

    /**
     * 新增场控管理
     * 
     * @param platformScenecon 场控管理
     * @return 结果
     */
    public int insertPlatformScenecon(PlatformScenecon platformScenecon);

    /**
     * 修改场控管理
     * 
     * @param platformScenecon 场控管理
     * @return 结果
     */
    public int updatePlatformScenecon(PlatformScenecon platformScenecon);

    /**
     * 删除场控管理
     * 
     * @param sceneconId 场控管理主键
     * @return 结果
     */
    public int deletePlatformSceneconBySceneconId(Long sceneconId);

    /**
     * 批量删除场控管理
     * 
     * @param sceneconIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformSceneconBySceneconIds(Long[] sceneconIds);
}
