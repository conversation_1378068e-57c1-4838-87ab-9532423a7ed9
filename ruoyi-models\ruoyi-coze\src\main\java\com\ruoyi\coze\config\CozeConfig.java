package com.ruoyi.coze.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;

/**
 * Coze API 配置类
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Configuration("coze")
@ConditionalOnProperty(prefix = "coze", name = { "enabled" }, havingValue = "true", matchIfMissing = false)
@ConfigurationProperties("coze")
public class CozeConfig {

    /**
     * 是否启用 Coze 服务
     */
    private boolean enabled;

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * Bot ID
     */
    private String botId;

    /**
     * 默认用户ID
     */
    private String userId;

    /**
     * 请求超时时间(秒)
     */
    private int timeout;

    // /**
    //  * 工作流轮询间隔(秒)
    //  */
    // private int pollInterval;

    /**
     * 工作流默认超时时间(秒)
     */
    private int pollTimeout;

    /**
     * 创建 Coze API 客户端
     */
    @Bean
    public CozeAPI cozeAPI() {
        TokenAuth authCli = new TokenAuth(accessToken);
        return new CozeAPI.Builder()
                .baseURL(baseUrl)
                .auth(authCli)
                .readTimeout(timeout * 1000)
                .build();
    }

    // 所有的 getter 和 setter 方法
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getBotId() {
        return botId;
    }

    public void setBotId(String botId) {
        this.botId = botId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    // public int getPollInterval() {
    //     return pollInterval;
    // }

    // public void setPollInterval(int pollInterval) {
    //     this.pollInterval = pollInterval;
    // }

    public int getPollTimeout() {
        return pollTimeout;
    }

    public void setPollTimeout(int pollTimeout) {
        this.pollTimeout = pollTimeout;
    }
}
