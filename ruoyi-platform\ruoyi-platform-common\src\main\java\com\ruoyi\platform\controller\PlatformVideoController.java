package com.ruoyi.platform.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.vo.DialogueSynthesisRequest;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.HashrateCostUtils.CostType;
import com.ruoyi.platform.utils.HashrateCostUtils.HashrateCost;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatusAndVersion;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 视频合成Controller
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@RestController
@RequestMapping("/platform/video")
@Tag(name = "【视频合成】管理")
public class PlatformVideoController extends BaseController
{
    @Autowired
    private IPlatformVideoService platformVideoService;
    
    @Autowired
    private FileUrlUtils fileUrlUtils;
    /**
     * 分页查询视频任务列表
     */
    @Operation(summary = "查询视频合成列表")
    @GetMapping("/list")
    public TableDataInfo list(PlatformVideo platformVideo)
    {
        startPage();
        List<PlatformVideo> list = platformVideoService.selectPlatformVideoList(platformVideo);
        FileUrlUtils.processTasksUrls(list);
        return getDataTable(list);
    }

    /**
     * 批量删除合成任务
     */
    @Operation(summary = "批量删除合成任务")
    @Log(title = "删除合成任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable(name = "taskIds") Long[] taskIds) {
        if (taskIds == null || taskIds.length == 0) {
            return error("请选择要删除的数据");
        }
        try {
            return toAjax(platformVideoService.deletePlatformVideoByIds(taskIds));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 创建新的合成视频任务
     */
    @Operation(summary = "创建视频合成M版")
    @HashrateCost(description = "创建视频合成M版", dynamicCost = true, unitCost = 500, calculationType = CostType.VIDEO_DURATION, immediateDeduction = false, resultVideoOnly = true)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody PlatformVideo param) {
        return success(new HashMap<String, Object>() {{
            put("id", platformVideoService.add(param));
        }});
    }

    /**
     * 创建新的合成视频任务 ,H版视频合成
     */
    @Operation(summary = "创建视频合成H版")
    @HashrateCost(description = "创建视频合成H版", dynamicCost = true, unitCost = 500, calculationType = CostType.VIDEO_DURATION, immediateDeduction = false, resultVideoOnly = true)
    @PostMapping("/createHeygem")
    public AjaxResult createHeygem(@RequestBody PlatformVideo param) {
        return success(new HashMap<String, Object>() {{
            put("id", platformVideoService.synthesisH(param));
        }});
    }

    /**
     * 根据任务结果动态调整状态 H M版
     */
    @Operation(summary = "根据任务结果动态调整状态")
    @GetMapping("/genStatus/{id}")
    public AjaxResult genStatus(@PathVariable("id") Long id) {
        Map<String, Object> result = fileUrlUtils.getTaskStatus(id);
        if (result == null) {
            return error("任务不存在");
        }
        if (result.containsKey("error")) {
            return error(result.get("error").toString());
        }
        return success(result);
    }

    /**
     * 获取一个视频合成任务M版
     */
    @Operation(summary = "获取一个待处理视频合成任务M版")
    @GetMapping("/getTask/{version}")
    @Anonymous
    public AjaxResult getTask(@PathVariable(value = "version") String version) {
        Map<String, Object> result = fileUrlUtils.getOneTask(version);
        
        if (result.containsKey("error")) {
            return error(result.get("error").toString());
        }
        if (result.containsKey("message")) {
            return success(result.get("message"));
        }
        if (result.containsKey("data")) {
            return success(result.get("data"));
        }
        return success();
    }
    /**
     * 根据版本获取一个视频合成任务H版
     */
    @Operation(summary = "根据版本获取一个待处理视频合成任务H版")
    @GetMapping("/getOneTaskByVersion/{version}")
    @Anonymous
    public AjaxResult getOneTaskByVersion(@PathVariable(value = "version") String version) {
        Map<String, Object> result = fileUrlUtils.getOneTaskByVersion(version);
    
        if (result.containsKey("error")) {
            return error(result.get("error").toString());
        }
        if (result.containsKey("message")) {
            return success(result.get("message"));
        }
        if (result.containsKey("data")) {
            return success(result.get("data"));
        }
        return success();
    }

    /**
     * 处理音频或视频文件上传 M H版本
     */
    @Operation(summary = "音频/视频上传接口")
    @Anonymous
    @PostMapping("/upload")
    public AjaxResult uploadFile(@RequestBody MultipartFile file) throws Exception {
        try {
            Map<String, String> fileInfo = FileUrlUtils.processUploadFile(file);
            if (fileInfo == null) {
                return error("不支持的文件类型，音频支持mp3/wav等格式，视频支持mp4/avi/rmvb格式");
            }
            // 检查是否为缓存复用的文件
            boolean isCached = "true".equals(fileInfo.get("isCached"));
            if (!isCached) {
                // 只有新文件才需要实际上传
                MultipartFile fileToUpload = MultipartFileUtils.createWithContentType(file, fileInfo.get("contentType"));
                FileOperateUtils.upload(fileInfo.get("fullPath"), fileToUpload);
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", fileInfo.get("fullPath"));
            ajax.put("md5", Md5Utils.getMd5(file));
            ajax.put("fileType", fileInfo.get("extension"));
            ajax.put("isCached", isCached); // 返回是否复用了缓存
            return ajax;
        } catch (Exception e) {
            return error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 更新任务状态为成功 M H版本
     */
    @Operation(summary = "根据合成结果任务状态为成功")
    @Anonymous
    @PostMapping("/taskSuccess")
    public AjaxResult updateTaskStatus(@RequestBody PlatformVideo param) {
        try {
            platformVideoService.updateTaskStatus(param.getId(), Long.valueOf(PlatformVideoTaskStatusAndVersion.STATUS_SUCCESS), param.getResultVideo());
            PlatformVideo updatedTask = platformVideoService.selectPlatformVideoById(param.getId());
            try {
                fileUrlUtils.processHashrateDeduction(updatedTask, param.getResultVideo());
            } catch (Exception e) {
                logger.error("处理算力点扣除失败: {}", e.getMessage(), e);
            }
            return success();
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 更新任务状态为失败 M H版本
     */
    @Operation(summary = "根据合成结果任务状态为失败")
    @PostMapping("/taskFail")
    @Anonymous
    public AjaxResult taskFail(@RequestBody PlatformVideo param) {
        try {
            platformVideoService.updateTaskStatus(param.getId(), Long.valueOf(PlatformVideoTaskStatusAndVersion.STATUS_FAILED), "");
        } catch (Exception e) {
            return error("更新失败");
        }
        return success();
    }

    /**
     * 查询可用视频合成模型 V版
     */
    @Operation(summary = "查询模型列表")
    @GetMapping("/models")
    public AjaxResult getAvailableModels() {
        List<PlatformModel> models = platformVideoService.getAvailableModels();
        return success(models);
    }

    /**
     * 上传媒体文件(视频或音频)
     */
    @Operation(summary = "上传媒体文件")
    @PostMapping("/upload/{type}")
    @Anonymous
    public AjaxResult uploadMedia(@RequestParam("file") MultipartFile file, @PathVariable("type") String type) {
        try {
            Map<String, String> result = platformVideoService.uploadMedia(file, type);
            return success(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 创建视频合成任务 (V版)
     */
    @Operation(summary = "创建视频合成V版")
    @Log(title = "创建视频合成V版", businessType = BusinessType.INSERT)
    @HashrateCost(description = "创建视频合成V版", dynamicCost = true, unitCost = 500, calculationType = CostType.VIDEO_DURATION, immediateDeduction = false, resultVideoOnly = true)
    @PostMapping("/synthesis")
    public AjaxResult createVideo(@RequestBody Map<String, Object> request) {
        try {
            Map<String, Object> result = platformVideoService.createVideoSynthesisWithUrls(request);
            return success(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 查询视频合成任务状态
     */
    @Operation(summary = "查询视频合成任务状态")
    @GetMapping("/statusTaskNo/{taskNo}")
    public AjaxResult getTaskStatus(@PathVariable("taskNo") String taskNo) {
        try {
            Map<String, Object> result = platformVideoService.queryVideoSynthesis(taskNo);
            fileUrlUtils.processTaskCompletionAndFee(result, "status");
            return success(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 数字人对话合成（支持M版、H版、V版，支持一键合成，支持字幕）
     */
    @Operation(summary = "数字人对话合成",description = "支持V版、H版、M版，支持系统音色和内置音色，可选择是否自动完成云剪辑，支持字幕功能")
    @Log(title = "数字人对话合成", businessType = BusinessType.INSERT)
    @HashrateCost(description = "数字人对话合成", dynamicCost = true, unitCost = 500, calculationType = CostType.VIDEO_DURATION, immediateDeduction = false, resultVideoOnly = true)
    @PostMapping("/dialogueSynthesis")
    public AjaxResult dialogueSynthesis(@RequestBody DialogueSynthesisRequest request) {
        try {
            Map<String, Object> result = platformVideoService.createDialogueSynthesisEnhanced(request);
            return success(result);
        } catch (Exception e) {
            return error("数字人对话合成失败: " + e.getMessage());
        }
    }

    /**
     * 查询一键合成任务的完整状态（推荐使用）
     */
    @Operation(summary = "查询一键合成任务的完整状态", description = "查询数字人对话合成和云剪辑的完整状态，包含最终视频地址")
    @GetMapping("/getAutoSynthesisStatus")
    public AjaxResult getAutoSynthesisStatus(@RequestParam String dialogueGroupId) {
        try {
            if (StringUtils.isEmpty(dialogueGroupId)) {
                return error("对话组ID不能为空");
            }
            Map<String, Object> result = platformVideoService.getAutoSynthesisStatus(dialogueGroupId);
            return success(result);
        } catch (Exception e) {
            return error("查询一键合成状态失败: " + e.getMessage());
        }
    }
}
