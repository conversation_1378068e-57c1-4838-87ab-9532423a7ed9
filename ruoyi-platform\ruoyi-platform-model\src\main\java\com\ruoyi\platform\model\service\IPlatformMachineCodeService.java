package com.ruoyi.platform.model.service;

import java.util.List;

import com.ruoyi.platform.model.domain.PlatformMachineCode;

/**
 * 机器管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-20
 */
public interface IPlatformMachineCodeService 
{
    /**
     * 查询机器管理
     * 
     * @param machineCodeId 机器管理主键
     * @return 机器管理
     */
    public PlatformMachineCode selectPlatformMachineCodeByMachineCodeId(Long machineCodeId);

    /**
     * 查询机器管理列表
     * 
     * @param platformMachineCode 机器管理
     * @return 机器管理集合
     */
    public List<PlatformMachineCode> selectPlatformMachineCodeList(PlatformMachineCode platformMachineCode);

    /**
     * 新增机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    public int insertPlatformMachineCode(PlatformMachineCode platformMachineCode);

    /**
     * 修改机器管理
     * 
     * @param platformMachineCode 机器管理
     * @return 结果
     */
    public int updatePlatformMachineCode(PlatformMachineCode platformMachineCode);

    /**
     * 批量删除机器管理
     * 
     * @param machineCodeIds 需要删除的机器管理主键集合
     * @return 结果
     */
    public int deletePlatformMachineCodeByMachineCodeIds(Long[] machineCodeIds);

    /**
     * 删除机器管理信息
     * 
     * @param machineCodeId 机器管理主键
     * @return 结果
     */
    public int deletePlatformMachineCodeByMachineCodeId(Long machineCodeId);
}
