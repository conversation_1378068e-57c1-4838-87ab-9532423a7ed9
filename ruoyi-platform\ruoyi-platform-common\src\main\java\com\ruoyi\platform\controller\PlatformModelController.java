package com.ruoyi.platform.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.service.IPlatformModelService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * AI模型Controller
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
@RestController
@RequestMapping("/platform/model")
@Tag(name = "【模型】管理")
public class PlatformModelController extends BaseController
{
    @Autowired
    private IPlatformModelService wyModelService;

    /**
     * 查询AI模型列表
     */
    @Operation(summary = "查询模型列表")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlatformModel wyModel)
    {
        startPage();
        List<PlatformModel> list = wyModelService.selectWyModelList(wyModel);
        return getDataTable(list);
    }

    /**
     * 导出AI模型列表
     */
    @Operation(summary = "导出模型列表")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:export')")
    @Log(title = "导出模型列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformModel wyModel)
    {
        List<PlatformModel> list = wyModelService.selectWyModelList(wyModel);
        ExcelUtil<PlatformModel> util = new ExcelUtil<PlatformModel>(PlatformModel.class);
        util.exportExcel(response, list, "模型数据");
    }

    /**
     * 获取AI模型详细信息
     */
    @Operation(summary = "获取模型详细信息")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:query')")
    @GetMapping(value = "/{modelId}")
    public AjaxResult getInfo(@PathVariable("modelId") Long modelId)
    {
        return success(wyModelService.selectWyModelByModelId(modelId));
    }

    /**
     * 新增AI模型
     */
    @Operation(summary = "新增模型")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:add')")
    @Log(title = "新增模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlatformModel wyModel)
    {
        return toAjax(wyModelService.insertWyModel(wyModel));
    }

    /**
     * 修改AI模型
     */
    @Operation(summary = "修改模型")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:edit')")
    @Log(title = "修改模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlatformModel wyModel)
    {
        return toAjax(wyModelService.updateWyModel(wyModel));
    }

    /**
     * 删除AI模型
     */
    @Operation(summary = "删除模型")
    //@PreAuthorize("@ss.hasPermi('wy:wymodel:remove')")
    @Log(title = "删除模型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modelIds}")
    public AjaxResult remove(@PathVariable( name = "modelIds" ) Long[] modelIds) 
    {
        return toAjax(wyModelService.deleteWyModelByModelIds(modelIds));
    }
}
