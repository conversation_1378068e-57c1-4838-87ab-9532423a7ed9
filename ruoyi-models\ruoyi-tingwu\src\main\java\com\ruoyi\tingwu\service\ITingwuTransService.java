package com.ruoyi.tingwu.service;

import java.util.List;

import com.ruoyi.tingwu.domain.TingwuTrans;

/**
 * 音视频任务Service接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface ITingwuTransService
{
    /**
     * 查询音视频任务
     *
     * @param vaId 音视频任务主键
     * @return 音视频任务
     */
    public TingwuTrans selectTingwuTransByVaId(Long vaId);

    /**
     * 查询音视频任务列表
     *
     * @param tingwuTrans 音视频任务
     * @return 音视频任务集合
     */
    public List<TingwuTrans> selectTingwuTransList(TingwuTrans tingwuTrans);

    /**
     * 新增音视频任务
     *
     * @param tingwuTrans 音视频任务
     * @return 结果
     */
    public int insertTingwuTrans(TingwuTrans tingwuTrans);

    /**
     * 修改音视频任务
     *
     * @param tingwuTrans 音视频任务
     * @return 结果
     */
    public int updateTingwuTrans(TingwuTrans tingwuTrans);

    /**
     * 批量删除音视频任务
     *
     * @param vaIds 需要删除的音视频任务主键集合
     * @return 结果
     */
    public int deleteTingwuTransByVaIds(Long[] vaIds);

    /**
     * 删除音视频任务信息
     *
     * @param vaId 音视频任务主键
     * @return 结果
     */
    public int deleteTingwuTransByVaId(Long vaId);

    /**
     * 根据编号删除数据
     * @param tingwuTrans
     * @return
     */
    public int updateTingwuTransByTaskId(TingwuTrans tingwuTrans);
}
